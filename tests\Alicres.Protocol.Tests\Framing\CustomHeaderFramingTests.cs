using Alicres.Protocol.Framing;
using Alicres.Protocol.Interfaces;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.Protocol.Tests.Framing;

/// <summary>
/// 自定义帧头帧处理器测试
/// </summary>
public class CustomHeaderFramingTests : IDisposable
{
    private readonly Mock<ILogger<CustomHeaderFraming>> _mockLogger;
    private readonly CustomHeaderFraming _framing;

    // 测试数据：7E F1 05 55 0A 00 00 10 F5 82
    private readonly byte[] _validTestData = { 0x7E, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };

    public CustomHeaderFramingTests()
    {
        _mockLogger = new Mock<ILogger<CustomHeaderFraming>>();
        _framing = new CustomHeaderFraming(0x7E, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Assert
        _framing.FramingName.Should().Be("CustomHeaderFraming");
        _framing.Mode.Should().Be(FramingMode.Custom);
        _framing.FrameHeader.Should().Be(0x7E);
        _framing.LengthFieldOffset.Should().Be(4);
        _framing.LengthIncludesHeader.Should().BeTrue();
        _framing.MinFrameLength.Should().Be(8);
        _framing.MaxFrameLength.Should().Be(256);
        _framing.IsEnabled.Should().BeTrue();
    }

    [Fact]
    public void Constructor_WithCustomHeader_ShouldSetCorrectHeader()
    {
        // Arrange
        var customFraming = new CustomHeaderFraming(0xAA);

        // Assert
        customFraming.FrameHeader.Should().Be(0xAA);
    }

    [Fact]
    public void HasCompleteFrame_WithValidData_ShouldReturnTrue()
    {
        // Act
        var result = _framing.HasCompleteFrame(_validTestData);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasCompleteFrame_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var insufficientData = new byte[] { 0x7E, 0xF1, 0x05 };

        // Act
        var result = _framing.HasCompleteFrame(insufficientData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HasCompleteFrame_WithNoHeader_ShouldReturnFalse()
    {
        // Arrange
        var noHeaderData = new byte[] { 0xFF, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };

        // Act
        var result = _framing.HasCompleteFrame(noHeaderData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void HasCompleteFrame_WithInvalidLength_ShouldReturnFalse()
    {
        // Arrange
        var invalidLengthData = new byte[] { 0x7E, 0xF1, 0x05, 0x55, 0xFF, 0x00, 0x00, 0x10, 0xF5, 0x82 };

        // Act
        var result = _framing.HasCompleteFrame(invalidLengthData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetExpectedFrameLength_WithValidData_ShouldReturnCorrectLength()
    {
        // Act
        var result = _framing.GetExpectedFrameLength(_validTestData);

        // Assert
        result.Should().Be(10); // 数据长度字段的值
    }

    [Fact]
    public void GetExpectedFrameLength_WithInsufficientData_ShouldReturnMinusOne()
    {
        // Arrange
        var insufficientData = new byte[] { 0x7E, 0xF1 };

        // Act
        var result = _framing.GetExpectedFrameLength(insufficientData);

        // Assert
        result.Should().Be(-1);
    }

    [Fact]
    public void GetExpectedFrameLength_WithNoHeader_ShouldReturnMinusOne()
    {
        // Arrange
        var noHeaderData = new byte[] { 0xFF, 0xF1, 0x05, 0x55, 0x0A };

        // Act
        var result = _framing.GetExpectedFrameLength(noHeaderData);

        // Assert
        result.Should().Be(-1);
    }

    [Fact]
    public void FrameMessage_WithValidMessage_ShouldAddHeader()
    {
        // Arrange
        var message = new byte[] { 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };

        // Act
        var result = _framing.FrameMessage(message);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(message.Length + 1);
        result[0].Should().Be(0x7E); // 帧头
        result.Skip(1).Should().Equal(message);
    }

    [Fact]
    public void UnframeMessage_WithValidFramedData_ShouldRemoveHeader()
    {
        // Act
        var result = _framing.UnframeMessage(_validTestData);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(_validTestData.Length - 1);
        result.Should().Equal(new byte[] { 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 });
    }

    [Fact]
    public void UnframeMessage_WithNoHeader_ShouldReturnOriginalData()
    {
        // Arrange
        var noHeaderData = new byte[] { 0xFF, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };

        // Act
        var result = _framing.UnframeMessage(noHeaderData);

        // Assert
        result.Should().Equal(noHeaderData);
    }

    [Fact]
    public void ProcessIncomingData_WithSingleCompleteFrame_ShouldExtractOneFrame()
    {
        // Act
        var result = _framing.ProcessIncomingData(_validTestData);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        result[0].Should().Equal(_validTestData);
    }

    [Fact]
    public void ProcessIncomingData_WithMultipleFrames_ShouldExtractAllFrames()
    {
        // Arrange
        var frame1 = _validTestData;
        var frame2 = new byte[] { 0x7E, 0x01, 0x02, 0x03, 0x08, 0xAA, 0xBB, 0x11, 0x22 };
        var combinedData = frame1.Concat(frame2).ToArray();

        // Act
        var result = _framing.ProcessIncomingData(combinedData);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(2);
        result[0].Should().Equal(frame1);
        result[1].Should().Equal(frame2);
    }

    [Fact]
    public void ProcessIncomingData_WithIncompleteFrame_ShouldReturnEmpty()
    {
        // Arrange
        var incompleteData = new byte[] { 0x7E, 0xF1, 0x05, 0x55 };

        // Act
        var result = _framing.ProcessIncomingData(incompleteData);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(0);
    }

    [Fact]
    public void ProcessIncomingData_WithInvalidHeader_ShouldSkipInvalidData()
    {
        // Arrange
        var invalidData = new byte[] { 0xFF, 0xFF, 0xFF };
        var validFrame = _validTestData;
        var combinedData = invalidData.Concat(validFrame).ToArray();

        // Act
        var result = _framing.ProcessIncomingData(combinedData);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        // 修复：应该跳过无效数据，只返回有效帧
        result[0].Should().Equal(validFrame);
    }

    [Fact]
    public void ProcessIncomingData_WithInvalidLength_ShouldSkipFrame()
    {
        // Arrange
        var invalidLengthFrame = new byte[] { 0x7E, 0xF1, 0x05, 0x55, 0xFF, 0x00, 0x00, 0x10, 0xF5, 0x82 };
        var validFrame = _validTestData;
        var combinedData = invalidLengthFrame.Concat(validFrame).ToArray();

        // Act
        var result = _framing.ProcessIncomingData(combinedData);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        result[0].Should().Equal(validFrame);
    }

    [Fact]
    public void ProcessIncomingData_WithPartialFrameAtEnd_ShouldBufferData()
    {
        // Arrange
        var completeFrame = _validTestData;
        var partialFrame = new byte[] { 0x7E, 0x01, 0x02 };
        var combinedData = completeFrame.Concat(partialFrame).ToArray();

        // Act
        var result1 = _framing.ProcessIncomingData(combinedData);

        // Assert - 第一次处理应该只返回完整帧
        result1.Should().NotBeNull();
        result1.Count.Should().Be(1);
        result1[0].Should().Equal(completeFrame);

        // Arrange - 添加剩余数据完成第二个帧
        var remainingData = new byte[] { 0x03, 0x08, 0xAA, 0xBB, 0x11, 0x22 };

        // Act
        var result2 = _framing.ProcessIncomingData(remainingData);

        // Assert - 第二次处理应该返回完整的第二个帧
        result2.Should().NotBeNull();
        result2.Count.Should().Be(1);
        result2[0].Should().Equal(new byte[] { 0x7E, 0x01, 0x02, 0x03, 0x08, 0xAA, 0xBB, 0x11, 0x22 });
    }

    [Fact]
    public void Reset_ShouldClearBuffer()
    {
        // Arrange
        var partialData = new byte[] { 0x7E, 0xF1, 0x05 };
        _framing.ProcessIncomingData(partialData);

        // Act
        _framing.Reset();

        // Assert
        // 重置后，缓冲区应该被清空，再次处理相同的部分数据应该不会产生完整帧
        var result = _framing.ProcessIncomingData(new byte[] { 0x55, 0x0A });
        result.Count.Should().Be(0);
    }

    [Fact]
    public void FrameMessage_WithNullMessage_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _framing.FrameMessage(null!));
    }

    [Fact]
    public void UnframeMessage_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _framing.UnframeMessage(null!));
    }

    [Fact]
    public void HasCompleteFrame_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _framing.HasCompleteFrame(null!));
    }

    [Fact]
    public void GetExpectedFrameLength_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _framing.GetExpectedFrameLength(null!));
    }

    public void Dispose()
    {
        // CustomHeaderFraming 不实现 IDisposable，无需调用 Dispose
    }
}
