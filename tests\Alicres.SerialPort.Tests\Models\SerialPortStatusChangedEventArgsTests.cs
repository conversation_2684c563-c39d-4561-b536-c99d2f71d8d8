using Alicres.SerialPort.Models;
using FluentAssertions;
using System;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// SerialPortStatusChangedEventArgs 类的单元测试
/// </summary>
public class SerialPortStatusChangedEventArgsTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Constructor_WithNullPortName_ShouldThrowArgumentNullException()
    {
        // Arrange
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act & Assert
        var action = () => new SerialPortStatusChangedEventArgs(null!, previousState, currentState);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("portName");
    }

    [Fact]
    public void Constructor_WithEmptyPortName_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
    }

    [Fact]
    public void Properties_ShouldBeReadOnly()
    {
        // Arrange
        var portName = "COM1";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Act & Assert
        var portNameProperty = typeof(SerialPortStatusChangedEventArgs).GetProperty(nameof(eventArgs.PortName));
        var previousStateProperty = typeof(SerialPortStatusChangedEventArgs).GetProperty(nameof(eventArgs.PreviousState));
        var currentStateProperty = typeof(SerialPortStatusChangedEventArgs).GetProperty(nameof(eventArgs.CurrentState));
        var timestampProperty = typeof(SerialPortStatusChangedEventArgs).GetProperty(nameof(eventArgs.Timestamp));

        portNameProperty?.CanWrite.Should().BeFalse();
        previousStateProperty?.CanWrite.Should().BeFalse();
        currentStateProperty?.CanWrite.Should().BeFalse();
        timestampProperty?.CanWrite.Should().BeFalse();
    }

    [Theory]
    [InlineData("COM1")]
    [InlineData("COM10")]
    [InlineData("/dev/ttyUSB0")]
    [InlineData("")]
    public void Constructor_WithVariousPortNames_ShouldInitializeCorrectly(string portName)
    {
        // Arrange
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
    }

    [Theory]
    [InlineData(SerialPortConnectionState.Disconnected, SerialPortConnectionState.Connecting)]
    [InlineData(SerialPortConnectionState.Connecting, SerialPortConnectionState.Connected)]
    [InlineData(SerialPortConnectionState.Connected, SerialPortConnectionState.Disconnecting)]
    [InlineData(SerialPortConnectionState.Disconnecting, SerialPortConnectionState.Disconnected)]
    [InlineData(SerialPortConnectionState.Connected, SerialPortConnectionState.Reconnecting)]
    [InlineData(SerialPortConnectionState.Reconnecting, SerialPortConnectionState.Connected)]
    [InlineData(SerialPortConnectionState.Connected, SerialPortConnectionState.Error)]
    [InlineData(SerialPortConnectionState.Error, SerialPortConnectionState.Disconnected)]
    public void Constructor_WithAllStateTransitions_ShouldInitializeCorrectly(
        SerialPortConnectionState previousState, 
        SerialPortConnectionState currentState)
    {
        // Arrange
        var portName = "COM1";

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
    }

    [Fact]
    public void Constructor_WithSamePreviousAndCurrentState_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var state = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, state, state);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.PreviousState.Should().Be(state);
        eventArgs.CurrentState.Should().Be(state);
    }

    [Fact]
    public void Timestamp_ShouldBeSetToCurrentTime()
    {
        // Arrange
        var portName = "COM1";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;
        var beforeCreation = DateTime.Now;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);
        var afterCreation = DateTime.Now;

        // Assert
        eventArgs.Timestamp.Should().BeOnOrAfter(beforeCreation);
        eventArgs.Timestamp.Should().BeOnOrBefore(afterCreation);
    }

    [Theory]
    [InlineData(SerialPortConnectionState.Disconnected)]
    [InlineData(SerialPortConnectionState.Connecting)]
    [InlineData(SerialPortConnectionState.Connected)]
    [InlineData(SerialPortConnectionState.Disconnecting)]
    [InlineData(SerialPortConnectionState.Reconnecting)]
    [InlineData(SerialPortConnectionState.Error)]
    public void Constructor_WithAllConnectionStates_ShouldInitializeCorrectly(SerialPortConnectionState state)
    {
        // Arrange
        var portName = "COM1";
        var otherState = state == SerialPortConnectionState.Disconnected 
            ? SerialPortConnectionState.Connected 
            : SerialPortConnectionState.Disconnected;

        // Act
        var eventArgs1 = new SerialPortStatusChangedEventArgs(portName, state, otherState);
        var eventArgs2 = new SerialPortStatusChangedEventArgs(portName, otherState, state);

        // Assert
        eventArgs1.PreviousState.Should().Be(state);
        eventArgs1.CurrentState.Should().Be(otherState);
        eventArgs2.PreviousState.Should().Be(otherState);
        eventArgs2.CurrentState.Should().Be(state);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var portName = "COM1";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("端口 COM1");
        result.Should().Contain("状态变化");
        result.Should().Contain("Disconnected -> Connected");
    }

    [Fact]
    public void ToString_WithDifferentStates_ShouldShowCorrectTransition()
    {
        // Arrange
        var portName = "COM2";
        var previousState = SerialPortConnectionState.Connected;
        var currentState = SerialPortConnectionState.Error;
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("端口 COM2");
        result.Should().Contain("Connected -> Error");
    }

    [Fact]
    public void Constructor_WithLongPortName_ShouldInitializeCorrectly()
    {
        // Arrange
        var longPortName = new string('A', 100);
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(longPortName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(longPortName);
        eventArgs.PortName.Length.Should().Be(100);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
    }

    [Fact]
    public void Constructor_WithSpecialCharactersInPortName_ShouldInitializeCorrectly()
    {
        // Arrange
        var specialPortName = "COM端口_1-测试@#$%";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(specialPortName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(specialPortName);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
    }

    [Fact]
    public void Constructor_MultipleInstances_ShouldHaveDifferentTimestamps()
    {
        // Arrange
        var portName = "COM1";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs1 = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);
        System.Threading.Thread.Sleep(1); // 确保时间戳不同
        var eventArgs2 = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Assert
        eventArgs1.Timestamp.Should().BeBefore(eventArgs2.Timestamp);
    }
}
