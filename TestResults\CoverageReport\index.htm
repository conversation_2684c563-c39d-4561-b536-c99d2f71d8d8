<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=EDGE,chrome=1" />
<link href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAn1BMVEUAAADCAAAAAAA3yDfUAAA3yDfUAAA8PDzr6+sAAAD4+Pg3yDeQkJDTAADt7e3V1dU3yDdCQkIAAADbMTHUAABBykHUAAA2yDY3yDfr6+vTAAB3diDR0dGYcHDUAAAjhiPSAAA3yDeuAADUAAA3yDf////OCALg9+BLzktBuzRelimzKgv87+/dNTVflSn1/PWz6rO126g5yDlYniy0KgwjJ0TyAAAAI3RSTlMABAj0WD6rJcsN7X1HzMqUJyYW+/X08+bltqSeaVRBOy0cE+citBEAAADBSURBVDjLlczXEoIwFIThJPYGiL0XiL3r+z+bBOJs9JDMuLffP8v+Gxfc6aIyDQVjQcnqnvRDEQwLJYtXpZT+YhDHKIjLbS+OUeT4TjkKi6OwOArq+yeKXD9uDqQQbcOjyCy0e6bTojZSftX+U6zUQ7OuittDu1k0WHqRFfdXQijgjKfF6ZwAikvmKD6OQjmKWUcDigkztm5FZN05nMON9ZcoinlBmTNnAUdBnRbUUbgdBZwWbkcBpwXcVsBtxfjb31j1QB5qeebOAAAAAElFTkSuQmCC" rel="icon" type="image/x-icon" />
<title>Summary - Coverage Report</title>
<link rel="stylesheet" type="text/css" href="report.css" />
</head><body><div class="container"><div class="containerleft">
<h1>Summary<a class="button" href="https://github.com/danielpalme/ReportGenerator" title="Star on GitHub"><i class="icon-star"></i>Star</a><a class="button" href="https://github.com/sponsors/danielpalme" title="Become a sponsor"><i class="icon-sponsor"></i>Sponsor</a></h1>
<div class="card-group">
<div class="card">
<div class="card-header">Information</div>
<div class="card-body">
<div class="table">
<table>
<tr>
<th>Parser:</th>
<td class="limit-width " title="MultiReport (3x Cobertura)">MultiReport (3x Cobertura)</td>
</tr>
<tr>
<th>Assemblies:</th>
<td class="limit-width right" title="1">1</td>
</tr>
<tr>
<th>Classes:</th>
<td class="limit-width right" title="25">25</td>
</tr>
<tr>
<th>Files:</th>
<td class="limit-width right" title="13">13</td>
</tr>
<tr>
<th>Coverage date:</th>
<td class="limit-width " title="2025/6/12 - 21:32:58 - 2025/6/12 - 23:06:29">2025/6/12 - 21:32:58 - 2025/6/12 - 23:06:29</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Line coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar31">68%</div>
<div class="table">
<table>
<tr>
<th>Covered lines:</th>
<td class="limit-width right" title="1210">1210</td>
</tr>
<tr>
<th>Uncovered lines:</th>
<td class="limit-width right" title="544">544</td>
</tr>
<tr>
<th>Coverable lines:</th>
<td class="limit-width right" title="1754">1754</td>
</tr>
<tr>
<th>Total lines:</th>
<td class="limit-width right" title="4182">4182</td>
</tr>
<tr>
<th>Line coverage:</th>
<td class="limit-width right" title="1210 of 1754">68.9%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Branch coverage</div>
<div class="card-body">
<div class="large cardpercentagebar cardpercentagebar44">55%</div>
<div class="table">
<table>
<tr>
<th>Covered branches:</th>
<td class="limit-width right" title="304">304</td>
</tr>
<tr>
<th>Total branches:</th>
<td class="limit-width right" title="546">546</td>
</tr>
<tr>
<th>Branch coverage:</th>
<td class="limit-width right" title="304 of 546">55.6%</td>
</tr>
</table>
</div>
</div>
</div>
<div class="card">
<div class="card-header">Method coverage</div>
<div class="card-body">
<div class="center">
<p>Feature is only available for sponsors</p>
<a class="pro-button" href="https://reportgenerator.io/pro" target="_blank">Upgrade to PRO version</a>
</div>
</div>
</div>
</div>
<h1>Risk Hotspots</h1>
<risk-hotspots>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column-min-200" />
<col class="column-min-200" />
<col class="column105" />
<col class="column105" />
</colgroup>
<thead><tr><th>Assembly</th>
<th>Class</th>
<th>Method</th>
<th>Crap Score <a href="https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" target="_blank"><i class="icon-info-circled"></i></a></th>
<th>Cyclomatic complexity <a href="https://en.wikipedia.org/wiki/Cyclomatic_complexity" target="_blank"><i class="icon-info-circled"></i></a></th>
</tr></thead>
<tbody>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortDataException.html">Alicres.SerialPort.Exceptions.SerialPortDataException</a></td>
<td title="GetLocalizedMessage(System.Globalization.CultureInfo)"><a href="Alicres.SerialPort_SerialPortDataException.html#file0_line600">GetLocalizedMessage(...)</a></td><td class="lightred right">240</td>
<td class="lightgreen right">15</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortConnectionException.html">Alicres.SerialPort.Exceptions.SerialPortConnectionException</a></td>
<td title="GetLocalizedMessage(System.Globalization.CultureInfo)"><a href="Alicres.SerialPort_SerialPortConnectionException.html#file0_line273">GetLocalizedMessage(...)</a></td><td class="lightred right">182</td>
<td class="lightgreen right">13</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="MonitorFlowControl(System.Object)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line303">MonitorFlowControl(...)</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td>
<td title="OnDataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)"><a href="Alicres.SerialPort_SerialPortService.html#file0_line618">OnDataReceived(...)</a></td><td class="lightred right">110</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortConfigurationException.html">Alicres.SerialPort.Exceptions.SerialPortConfigurationException</a></td>
<td title="GetLocalizedMessage(System.Globalization.CultureInfo)"><a href="Alicres.SerialPort_SerialPortConfigurationException.html#file0_line433">GetLocalizedMessage(...)</a></td><td class="lightred right">72</td>
<td class="lightgreen right">8</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortConfigurationException.html">Alicres.SerialPort.Exceptions.SerialPortConfigurationException</a></td>
<td title="GetDetailedErrorInfo()"><a href="Alicres.SerialPort_SerialPortConfigurationException.html#file0_line461">GetDetailedErrorInfo()</a></td><td class="lightred right">72</td>
<td class="lightgreen right">8</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td>
<td title="ReconnectAsync()"><a href="Alicres.SerialPort_SerialPortService.html#file0_line738">ReconnectAsync()</a></td><td class="lightred right">72</td>
<td class="lightgreen right">8</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td>
<td title="SendAsync()"><a href="Alicres.SerialPort_SerialPortService.html#file0_line255">SendAsync()</a></td><td class="lightred right">54</td>
<td class="lightgreen right">10</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortDataException.html">Alicres.SerialPort.Exceptions.SerialPortDataException</a></td>
<td title="GetDetailedErrorInfo()"><a href="Alicres.SerialPort_SerialPortDataException.html#file0_line636">GetDetailedErrorInfo()</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="RecordReceive(System.Int32)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line164">RecordReceive(...)</a></td><td class="lightred right">42</td>
<td class="lightgreen right">6</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_AdvancedBufferManager.html">Alicres.SerialPort.Services.AdvancedBufferManager</a></td>
<td title="HandleBufferOverflow(Alicres.SerialPort.Models.SerialPortData)"><a href="Alicres.SerialPort_AdvancedBufferManager.html#file0_line204">HandleBufferOverflow(...)</a></td><td class="lightred right">37</td>
<td class="lightred right">20</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="CanSend(System.Int32)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line103">CanSend(...)</a></td><td class="lightgreen right">26</td>
<td class="lightred right">26</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="ProcessFlowControlData(System.Byte[])"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line177">ProcessFlowControlData(...)</a></td><td class="lightgreen right">26</td>
<td class="lightred right">26</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_SerialPortConfiguration.html">Alicres.SerialPort.Models.SerialPortConfiguration</a></td>
<td title="IsValid()"><a href="Alicres.SerialPort_SerialPortConfiguration.html#file0_line131">IsValid()</a></td><td class="lightgreen right">18</td>
<td class="lightred right">18</td>
</tr>
<tr>
<td>Alicres.SerialPort</td>
<td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td>
<td title="SetRtsFlowControl(System.Boolean)"><a href="Alicres.SerialPort_FlowControlManager.html#file0_line215">SetRtsFlowControl(...)</a></td><td class="lightgreen right">17</td>
<td class="lightred right">16</td>
</tr>
</tbody>
</table>
</div>
</risk-hotspots>
<h1>Coverage</h1>
<coverage-info>
<div class="table-responsive">
<table class="overview table-fixed stripped">
<colgroup>
<col class="column-min-200" />
<col class="column90" />
<col class="column105" />
<col class="column100" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
<col class="column90" />
<col class="column70" />
<col class="column60" />
<col class="column112" />
</colgroup>
<thead>
<tr class="header"><th></th><th colspan="6" class="center">Line coverage</th><th colspan="4" class="center">Branch coverage</th></tr>
<tr><th>Name</th><th class="right">Covered</th><th class="right">Uncovered</th><th class="right">Coverable</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th><th class="right">Covered</th><th class="right">Total</th><th class="center" colspan="2">Percentage</th></tr></thead>
<tbody>
<tr><th>Alicres.SerialPort</th><th class="right">1210</th><th class="right">544</th><th class="right">1754</th><th class="right">7815</th><th title="1210/1754" class="right">68.9%</th><th><table class="coverage"><tr><td class="green covered69">&nbsp;</td><td class="red covered31">&nbsp;</td></tr></table></th><th class="right">304</th><th class="right">546</th><th class="right" title="304/546">55.6%</th><th><table class="coverage"><tr><td class="green covered56">&nbsp;</td><td class="red covered44">&nbsp;</td></tr></table></th></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortConfigurationException.html">Alicres.SerialPort.Exceptions.SerialPortConfigurationException</a></td><td class="right">30</td><td class="right">51</td><td class="right">81</td><td class="right">715</td><td title="30/81" class="right">37%</td><td><table class="coverage"><tr><td class="green covered37">&nbsp;</td><td class="red covered63">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">16</td><td class="right" title="0/16">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortConnectionException.html">Alicres.SerialPort.Exceptions.SerialPortConnectionException</a></td><td class="right">40</td><td class="right">30</td><td class="right">70</td><td class="right">715</td><td title="40/70" class="right">57.1%</td><td><table class="coverage"><tr><td class="green covered57">&nbsp;</td><td class="red covered43">&nbsp;</td></tr></table></td><td class="right">1</td><td class="right">22</td><td class="right" title="1/22">4.5%</td><td><table class="coverage"><tr><td class="green covered4">&nbsp;</td><td class="red covered96">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortDataException.html">Alicres.SerialPort.Exceptions.SerialPortDataException</a></td><td class="right">41</td><td class="right">58</td><td class="right">99</td><td class="right">715</td><td title="41/99" class="right">41.4%</td><td><table class="coverage"><tr><td class="green covered41">&nbsp;</td><td class="red covered59">&nbsp;</td></tr></table></td><td class="right">1</td><td class="right">30</td><td class="right" title="1/30">3.3%</td><td><table class="coverage"><tr><td class="green covered3">&nbsp;</td><td class="red covered97">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortException.html">Alicres.SerialPort.Exceptions.SerialPortException</a></td><td class="right">40</td><td class="right">29</td><td class="right">69</td><td class="right">715</td><td title="40/69" class="right">57.9%</td><td><table class="coverage"><tr><td class="green covered58">&nbsp;</td><td class="red covered42">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">8</td><td class="right" title="0/8">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_GlobalReconnectOptions.html">Alicres.SerialPort.Extensions.GlobalReconnectOptions</a></td><td class="right">3</td><td class="right">0</td><td class="right">3</td><td class="right">96</td><td title="3/3" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortServiceOptions.html">Alicres.SerialPort.Extensions.SerialPortServiceOptions</a></td><td class="right">4</td><td class="right">0</td><td class="right">4</td><td class="right">96</td><td title="4/4" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_ServiceCollectionExtensions.html">Alicres.SerialPort.Extensions.ServiceCollectionExtensions</a></td><td class="right">13</td><td class="right">0</td><td class="right">13</td><td class="right">96</td><td title="13/13" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortAddedEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs</a></td><td class="right">5</td><td class="right">0</td><td class="right">5</td><td class="right">159</td><td title="5/5" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortRemovedEventArgs.html">Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs</a></td><td class="right">5</td><td class="right">0</td><td class="right">5</td><td class="right">159</td><td title="5/5" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_BufferOverflowEventArgs.html">Alicres.SerialPort.Models.BufferOverflowEventArgs</a></td><td class="right">7</td><td class="right">7</td><td class="right">14</td><td class="right">190</td><td title="7/14" class="right">50%</td><td><table class="coverage"><tr><td class="green covered50">&nbsp;</td><td class="red covered50">&nbsp;</td></tr></table></td><td class="right">1</td><td class="right">2</td><td class="right" title="1/2">50%</td><td><table class="coverage"><tr><td class="green covered50">&nbsp;</td><td class="red covered50">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_BufferStatistics.html">Alicres.SerialPort.Models.BufferStatistics</a></td><td class="right">67</td><td class="right">0</td><td class="right">67</td><td class="right">307</td><td title="67/67" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">22</td><td class="right">22</td><td class="right" title="22/22">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_BufferWarningEventArgs.html">Alicres.SerialPort.Models.BufferWarningEventArgs</a></td><td class="right">7</td><td class="right">7</td><td class="right">14</td><td class="right">190</td><td title="7/14" class="right">50%</td><td><table class="coverage"><tr><td class="green covered50">&nbsp;</td><td class="red covered50">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_CongestionDetectedEventArgs.html">Alicres.SerialPort.Models.CongestionDetectedEventArgs</a></td><td class="right">0</td><td class="right">14</td><td class="right">14</td><td class="right">190</td><td title="0/14" class="right">0%</td><td><table class="coverage"><tr><td class="red covered100">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_FlowControlStatistics.html">Alicres.SerialPort.Models.FlowControlStatistics</a></td><td class="right">68</td><td class="right">0</td><td class="right">68</td><td class="right">307</td><td title="68/68" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">20</td><td class="right">20</td><td class="right" title="20/20">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_FlowControlStatusChangedEventArgs.html">Alicres.SerialPort.Models.FlowControlStatusChangedEventArgs</a></td><td class="right">7</td><td class="right">5</td><td class="right">12</td><td class="right">190</td><td title="7/12" class="right">58.3%</td><td><table class="coverage"><tr><td class="green covered58">&nbsp;</td><td class="red covered42">&nbsp;</td></tr></table></td><td class="right">0</td><td class="right">0</td><td class="right" title="-"></td><td><table class="coverage"><tr><td class="gray covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortConfiguration.html">Alicres.SerialPort.Models.SerialPortConfiguration</a></td><td class="right">40</td><td class="right">0</td><td class="right">40</td><td class="right">155</td><td title="40/40" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">18</td><td class="right">18</td><td class="right" title="18/18">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortData.html">Alicres.SerialPort.Models.SerialPortData</a></td><td class="right">56</td><td class="right">0</td><td class="right">56</td><td class="right">167</td><td title="56/56" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">18</td><td class="right">20</td><td class="right" title="18/20">90%</td><td><table class="coverage"><tr><td class="green covered90">&nbsp;</td><td class="red covered10">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortDataReceivedEventArgs.html">Alicres.SerialPort.Models.SerialPortDataReceivedEventArgs</a></td><td class="right">8</td><td class="right">0</td><td class="right">8</td><td class="right">130</td><td title="8/8" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">2</td><td class="right">2</td><td class="right" title="2/2">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortErrorEventArgs.html">Alicres.SerialPort.Models.SerialPortErrorEventArgs</a></td><td class="right">14</td><td class="right">0</td><td class="right">14</td><td class="right">130</td><td title="14/14" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">6</td><td class="right">6</td><td class="right" title="6/6">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortStatus.html">Alicres.SerialPort.Models.SerialPortStatus</a></td><td class="right">46</td><td class="right">0</td><td class="right">46</td><td class="right">175</td><td title="46/46" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">6</td><td class="right">6</td><td class="right" title="6/6">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortStatusChangedEventArgs.html">Alicres.SerialPort.Models.SerialPortStatusChangedEventArgs</a></td><td class="right">14</td><td class="right">0</td><td class="right">14</td><td class="right">130</td><td title="14/14" class="right">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td><td class="right">2</td><td class="right">2</td><td class="right" title="2/2">100%</td><td><table class="coverage"><tr><td class="green covered100">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_AdvancedBufferManager.html">Alicres.SerialPort.Services.AdvancedBufferManager</a></td><td class="right">133</td><td class="right">18</td><td class="right">151</td><td class="right">310</td><td title="133/151" class="right">88%</td><td><table class="coverage"><tr><td class="green covered88">&nbsp;</td><td class="red covered12">&nbsp;</td></tr></table></td><td class="right">50</td><td class="right">76</td><td class="right" title="50/76">65.7%</td><td><table class="coverage"><tr><td class="green covered66">&nbsp;</td><td class="red covered34">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_FlowControlManager.html">Alicres.SerialPort.Services.FlowControlManager</a></td><td class="right">134</td><td class="right">39</td><td class="right">173</td><td class="right">353</td><td title="134/173" class="right">77.4%</td><td><table class="coverage"><tr><td class="green covered77">&nbsp;</td><td class="red covered23">&nbsp;</td></tr></table></td><td class="right">79</td><td class="right">118</td><td class="right" title="79/118">66.9%</td><td><table class="coverage"><tr><td class="green covered67">&nbsp;</td><td class="red covered33">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortManager.html">Alicres.SerialPort.Services.SerialPortManager</a></td><td class="right">159</td><td class="right">38</td><td class="right">197</td><td class="right">445</td><td title="159/197" class="right">80.7%</td><td><table class="coverage"><tr><td class="green covered81">&nbsp;</td><td class="red covered19">&nbsp;</td></tr></table></td><td class="right">25</td><td class="right">38</td><td class="right" title="25/38">65.7%</td><td><table class="coverage"><tr><td class="green covered66">&nbsp;</td><td class="red covered34">&nbsp;</td></tr></table></td></tr>
<tr><td><a href="Alicres.SerialPort_SerialPortService.html">Alicres.SerialPort.Services.SerialPortService</a></td><td class="right">269</td><td class="right">248</td><td class="right">517</td><td class="right">980</td><td title="269/517" class="right">52%</td><td><table class="coverage"><tr><td class="green covered52">&nbsp;</td><td class="red covered48">&nbsp;</td></tr></table></td><td class="right">53</td><td class="right">140</td><td class="right" title="53/140">37.8%</td><td><table class="coverage"><tr><td class="green covered38">&nbsp;</td><td class="red covered62">&nbsp;</td></tr></table></td></tr>
</tbody>
</table>
</div>
</coverage-info>
<div class="footer">Generated by: ReportGenerator *******<br />2025/6/12 - 23:06:56<br /><a href="https://github.com/danielpalme/ReportGenerator">GitHub</a> | <a href="https://reportgenerator.io">reportgenerator.io</a></div></div></div>
<script type="text/javascript">
/* <![CDATA[ */
(function() {
    var url = window.location.href;
    var startOfQueryString = url.indexOf('?');
    var queryString = startOfQueryString > -1 ? url.substr(startOfQueryString) : '';

    if (startOfQueryString > -1) {
        var i = 0, href= null;
        var css = document.getElementsByTagName('link');

        for (i = 0; i < css.length; i++) {
            if (css[i].getAttribute('rel') !== 'stylesheet') {
            continue;
            }

            href = css[i].getAttribute('href');

            if (href) {
            css[i].setAttribute('href', href + queryString);
            }
        }

        var links = document.getElementsByTagName('a');

        for (i = 0; i < links.length; i++) {
            href = links[i].getAttribute('href');

            if (href
                && !href.startsWith('http://')
                && !href.startsWith('https://')
                && !href.startsWith('#')
                && href.indexOf('?') === -1) {
            links[i].setAttribute('href', href + queryString);
            }
        }
    }

    var newScript = document.createElement('script');
    newScript.src = 'main.js' + queryString;
    document.getElementsByTagName('body')[0].appendChild(newScript);
})();
/* ]]> */ 
</script></body></html>