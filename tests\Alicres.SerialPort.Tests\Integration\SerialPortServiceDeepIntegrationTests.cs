using Alicres.SerialPort.Exceptions;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.SerialPort.Tests.Integration.TestHelpers;
using Alicres.SerialPort.Tests.TestHelpers;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;

namespace Alicres.SerialPort.Tests.Integration;

/// <summary>
/// SerialPortService 深度集成测试 - 专注于核心业务逻辑的端到端验证
/// </summary>
public class SerialPortServiceDeepIntegrationTests : IntegrationTestBase
{
    private readonly ITestOutputHelper _output;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="output">测试输出</param>
    public SerialPortServiceDeepIntegrationTests(ITestOutputHelper output)
    {
        _output = output;
    }

    /// <summary>
    /// 创建测试配置
    /// </summary>
    /// <returns>测试配置</returns>
    private SerialPortConfiguration CreateTestConfiguration()
    {
        return new SerialPortConfiguration
        {
            PortName = "COM_DEEP_TEST",
            BaudRate = 115200,
            DataBits = 8,
            StopBits = System.IO.Ports.StopBits.One,
            Parity = System.IO.Ports.Parity.None
        };
    }

    /// <summary>
    /// 测试完整的连接生命周期 - 验证状态转换的完整性
    /// </summary>
    [Fact]
    public async Task CompleteConnectionLifecycle_ShouldWorkCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        var service = CreateSerialPortService(configuration, useMock: false);
        var eventCollector = CreateEventCollector<SerialPortStatusChangedEventArgs>();
        service.StatusChanged += eventCollector.EventHandler;

        // Act & Assert - 打开连接
        var openResult = await service.OpenAsync();
        Assert.False(openResult); // 模拟端口不存在

        // 验证状态变化事件
        var hasEvents = await eventCollector.WaitForEventsAsync(1, TimeSpan.FromSeconds(2));
        if (hasEvents)
        {
            var lastEvent = eventCollector.GetLastEvent();
            Assert.NotNull(lastEvent);

            // 输出调试信息
            _output.WriteLine($"收到状态变化事件: {lastEvent.Args.PreviousState} -> {lastEvent.Args.CurrentState}");

            // 允许多种状态，因为连接失败可能导致不同的最终状态
            var validStates = new[]
            {
                SerialPortConnectionState.Disconnected,
                SerialPortConnectionState.Error,
                SerialPortConnectionState.Connecting,
                SerialPortConnectionState.Disconnecting
            };

            Assert.Contains(lastEvent.Args.CurrentState, validStates);
        }
        else
        {
            // 如果没有收到事件，这也是可以接受的，因为某些实现可能不会触发状态变化事件
            _output.WriteLine("未收到状态变化事件，这在某些实现中是正常的");
        }

        // 验证状态一致性 - 允许 Disconnected 或 Error 状态
        Assert.True(service.Status.ConnectionState == SerialPortConnectionState.Disconnected ||
                   service.Status.ConnectionState == SerialPortConnectionState.Error);
        Assert.False(service.IsConnected);
    }

    /// <summary>
    /// 测试数据发送和接收的完整流程 - 验证数据完整性和流控制
    /// </summary>
    [Fact]
    public async Task DataTransmissionFlow_ShouldHandleCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableFlowControl = true;
        
        var service = CreateSerialPortService(configuration, useMock: false);
        var testData = TestDataGenerator.GenerateRandomBytes(100);

        // Act & Assert - 验证未连接时的异常处理
        await Assert.ThrowsAsync<SerialPortDataException>(
            async () => await service.SendAsync(testData));
            
        // 验证数据统计未更新 - 使用具体实现类型
        if (service is SerialPortService concreteService)
        {
            Assert.Equal(0, concreteService.Status.BytesSent);
            Assert.Equal(0, concreteService.Status.BytesReceived);
        }
    }

    /// <summary>
    /// 测试错误恢复机制 - 验证自动重连和错误处理的完整性
    /// </summary>
    [Fact]
    public async Task ErrorRecoveryMechanism_ShouldWorkCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableAutoReconnect = true;
        configuration.MaxReconnectAttempts = 3;
        configuration.ReconnectInterval = 100; // 快速重连用于测试
        
        var service = CreateSerialPortService(configuration, useMock: false);
        var errorCollector = CreateEventCollector<SerialPortErrorEventArgs>();
        
        service.ErrorOccurred += errorCollector.EventHandler;

        // Act - 尝试连接不存在的端口
        var result = await service.OpenAsync();

        // Assert - 验证错误处理
        Assert.False(result);
        Assert.Equal(0, service.Status.ReconnectAttempts);
        
        // 等待错误事件
        var hasErrorEvents = await errorCollector.WaitForEventsAsync(1, TimeSpan.FromSeconds(2));
        if (hasErrorEvents)
        {
            var errorEvent = errorCollector.GetFirstEvent();
            Assert.NotNull(errorEvent);
            Assert.Contains("打开串口", errorEvent.Args.Exception.Message);
            Assert.Equal(configuration.PortName, errorEvent.Args.PortName);
        }
    }

    /// <summary>
    /// 测试流控制集成 - 验证流控制管理器的初始化和配置
    /// </summary>
    [Fact]
    public void FlowControlIntegration_ShouldInitializeCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableFlowControl = true;
        configuration.SendRateLimit = 1000;

        // Act
        var service = CreateSerialPortService(configuration, useMock: false);

        // Assert - 验证流控制配置
        Assert.True(configuration.EnableFlowControl);
        Assert.Equal(1000, configuration.SendRateLimit);
        
        // 验证流控制统计信息
        if (service is SerialPortService concreteService)
        {
            var flowStats = concreteService.GetFlowControlStatistics();
            if (flowStats != null)
            {
                Assert.Equal(FlowControlStatus.Normal, flowStats.CurrentStatus);
            }
        }
    }

    /// <summary>
    /// 测试高级缓冲管理集成 - 验证缓冲管理器的初始化和统计
    /// </summary>
    [Fact]
    public void AdvancedBufferingIntegration_ShouldInitializeCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableAdvancedBuffering = true;
        configuration.DataQueueMaxLength = 1000;

        // Act
        var service = CreateSerialPortService(configuration, useMock: false);

        // Assert - 验证缓冲配置
        Assert.True(configuration.EnableAdvancedBuffering);
        Assert.Equal(1000, configuration.DataQueueMaxLength);

        if (service is SerialPortService concreteService)
        {
            Assert.Equal(0, concreteService.GetQueueLength());
            Assert.Equal(0, concreteService.GetQueueUsagePercentage());

            // 验证缓冲统计信息
            var bufferStats = concreteService.GetBufferStatistics();
            if (bufferStats != null)
            {
                Assert.Equal(0, bufferStats.QueueLength);
                Assert.Equal(0, bufferStats.TotalBytesReceived);
                Assert.Equal(0, bufferStats.TotalBytesDropped);
            }
        }
    }

    /// <summary>
    /// 测试并发操作安全性 - 验证多线程环境下的线程安全性
    /// </summary>
    [Fact]
    public async Task ConcurrentOperations_ShouldBeSafe()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        var service = CreateSerialPortService(configuration, useMock: false);
        var tasks = new List<Task>();
        var exceptions = new List<Exception>();

        // Act - 并发执行多个操作
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    await service.OpenAsync();
                    await Task.Delay(10); // 模拟操作延迟
                    await service.CloseAsync();
                }
                catch (Exception ex)
                {
                    lock (exceptions)
                    {
                        exceptions.Add(ex);
                    }
                    _output.WriteLine($"并发操作异常: {ex.Message}");
                }
            }));
        }

        // Assert - 等待所有任务完成
        await Task.WhenAll(tasks);
        // 允许 Disconnected 或 Error 状态，因为并发操作可能导致不同的最终状态
        Assert.True(service.Status.ConnectionState == SerialPortConnectionState.Disconnected ||
                   service.Status.ConnectionState == SerialPortConnectionState.Error);

        // 验证并发安全性 - 所有异常都应该是预期的连接异常
        foreach (var ex in exceptions)
        {
            Assert.True(ex is SerialPortConnectionException || ex is ObjectDisposedException);
        }
    }

    /// <summary>
    /// 测试配置动态更新 - 验证运行时配置更改的正确性
    /// </summary>
    [Fact]
    public void ConfigurationDynamicUpdate_ShouldWorkCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        var service = CreateSerialPortService(configuration, useMock: false);
        var newConfiguration = CreateTestConfiguration();
        newConfiguration.BaudRate = 115200;
        newConfiguration.PortName = "COM2";
        newConfiguration.DataBits = 7;
        newConfiguration.EnableFlowControl = true;

        // Act
        service.Configure(newConfiguration);

        // Assert - 验证配置更新
        Assert.Equal(115200, service.Configuration.BaudRate);
        Assert.Equal("COM2", service.Configuration.PortName);
        Assert.Equal(7, service.Configuration.DataBits);
        Assert.True(service.Configuration.EnableFlowControl);
        
        // 验证状态同步
        Assert.Equal("COM2", service.Status.PortName);
    }

    /// <summary>
    /// 测试资源清理 - 验证 Dispose 模式的正确实现
    /// </summary>
    [Fact]
    public void ResourceCleanup_ShouldWorkCorrectly()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        var service = new SerialPortService(configuration, SerialPortLogger);

        // Act
        service.Dispose();

        // Assert - 验证资源已清理
        Assert.Throws<ObjectDisposedException>(() => service.GetBytesToRead());
        Assert.Throws<ObjectDisposedException>(() => service.GetBytesToWrite());
        
        // 验证多次 Dispose 不会抛出异常
        service.Dispose(); // 应该安全
    }

    /// <summary>
    /// 测试流控制和缓冲管理的协同工作 - 验证两个子系统的集成
    /// </summary>
    [Fact]
    public void FlowControlAndBufferingIntegration_ShouldWorkTogether()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableFlowControl = true;
        configuration.EnableAdvancedBuffering = true;
        configuration.DataQueueMaxLength = 500;
        configuration.SendRateLimit = 2000;

        // Act
        var service = CreateSerialPortService(configuration, useMock: false);

        // Assert - 验证两个子系统都已初始化
        Assert.True(configuration.EnableFlowControl);
        Assert.True(configuration.EnableAdvancedBuffering);

        if (service is SerialPortService concreteService)
        {
            var flowStats = concreteService.GetFlowControlStatistics();
            var bufferStats = concreteService.GetBufferStatistics();

            Assert.NotNull(flowStats);
            Assert.NotNull(bufferStats);
            Assert.Equal(0, bufferStats.QueueLength);
        }
    }

    /// <summary>
    /// 测试内存使用和性能 - 验证长时间运行的稳定性
    /// </summary>
    [Fact]
    public async Task MemoryAndPerformance_ShouldBeStable()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableAdvancedBuffering = true;
        configuration.DataQueueMaxLength = 100;
        
        var service = CreateSerialPortService(configuration, useMock: false);
        
        // Act - 模拟大量操作
        for (int i = 0; i < 100; i++)
        {
            try
            {
                await service.OpenAsync();
                await service.CloseAsync();
                
                // 模拟数据操作
                if (service is SerialPortService concreteServiceInner)
                {
                    concreteServiceInner.ClearDataQueue();
                    var queueLength = concreteServiceInner.GetQueueLength();
                    var usage = concreteServiceInner.GetQueueUsagePercentage();

                    Assert.Equal(0, queueLength);
                    Assert.Equal(0, usage);
                }
            }
            catch (Exception ex)
            {
                // 记录但不中断测试
                _output.WriteLine($"操作 {i} 异常: {ex.Message}");
            }
        }

        // Assert - 验证服务仍然可用 - 允许 Disconnected 或 Error 状态
        Assert.True(service.Status.ConnectionState == SerialPortConnectionState.Disconnected ||
                   service.Status.ConnectionState == SerialPortConnectionState.Error);
        if (service is SerialPortService concreteService)
        {
            Assert.Equal(0, concreteService.GetQueueLength());
        }
    }
}
