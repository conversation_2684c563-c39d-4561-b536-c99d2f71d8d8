using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// FlowControlStatistics 类的单元测试
/// </summary>
public class FlowControlStatisticsTests
{
    /// <summary>
    /// 测试当流控制被禁用时，性能建议应该建议启用流控制
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithFlowControlDisabled_ShouldSuggestEnabling()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = false,
            CurrentSendRate = 500,      // 设置基础属性
            SendRateLimit = 1000,       // RateUsagePercentage 将为 50%
            IsXoffReceived = false,
            IsRtsPaused = false,
            CurrentStatus = FlowControlStatus.Normal
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("流控制未启用，考虑启用以提高数据传输可靠性");
    }

    /// <summary>
    /// 测试当发送速率使用率较高时，性能建议应该建议优化
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithHighRateUsage_ShouldSuggestOptimization()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = true,
            CurrentSendRate = 950,
            SendRateLimit = 1000,
            IsXoffReceived = false,
            IsRtsPaused = false,
            CurrentStatus = FlowControlStatus.Normal
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("发送速率接近限制，考虑增加速率限制或优化数据发送");
    }

    /// <summary>
    /// 测试当收到 XOFF 信号时，性能建议应该建议检查接收端
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithXoffReceived_ShouldSuggestCheckingReceiver()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = true,
            CurrentSendRate = 500,      // 设置基础属性
            SendRateLimit = 1000,       // RateUsagePercentage 将为 50%
            IsXoffReceived = true,
            IsRtsPaused = false,
            CurrentStatus = FlowControlStatus.Normal
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("接收到 XOFF 信号，对方要求暂停发送，检查接收方处理能力");
    }

    /// <summary>
    /// 测试当 RTS 被暂停时，性能建议应该建议检查硬件连接
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithRtsPaused_ShouldSuggestCheckingHardware()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = true,
            CurrentSendRate = 500,      // 设置基础属性
            SendRateLimit = 1000,       // RateUsagePercentage 将为 50%
            IsXoffReceived = false,
            IsRtsPaused = true,
            CurrentStatus = FlowControlStatus.Normal
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("RTS 流控制暂停，检查硬件连接和对方设备状态");
    }

    /// <summary>
    /// 测试当流控制状态为错误时，性能建议应该建议检查配置
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithErrorStatus_ShouldSuggestCheckingConfiguration()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = true,
            CurrentSendRate = 500,      // 设置基础属性
            SendRateLimit = 1000,       // RateUsagePercentage 将为 50%
            IsXoffReceived = false,
            IsRtsPaused = false,
            CurrentStatus = FlowControlStatus.Error
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("流控制出现错误，检查配置和硬件连接");
    }

    /// <summary>
    /// 测试当流控制条件良好时，性能建议应该返回正面反馈
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithGoodConditions_ShouldReturnPositiveFeedback()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = true,
            CurrentSendRate = 500,      // 设置基础属性
            SendRateLimit = 1000,       // RateUsagePercentage 将为 50%
            IsXoffReceived = false,
            IsRtsPaused = false,
            CurrentStatus = FlowControlStatus.Normal
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().NotBeEmpty();
        suggestions.Should().Contain("流控制运行状态良好，无需特别优化");
    }

    /// <summary>
    /// 测试当速率限制为零时，使用率百分比应该返回零
    /// </summary>
    [Fact]
    public void RateUsagePercentage_WithZeroLimit_ShouldReturnZero()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CurrentSendRate = 100,
            SendRateLimit = 0
        };

        // Act
        var percentage = stats.RateUsagePercentage;

        // Assert
        percentage.Should().Be(0);
    }

    /// <summary>
    /// 测试当速率限制有效时，使用率百分比应该正确计算
    /// </summary>
    [Fact]
    public void RateUsagePercentage_WithValidLimit_ShouldCalculateCorrectly()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CurrentSendRate = 750,
            SendRateLimit = 1000
        };

        // Act
        var percentage = stats.RateUsagePercentage;

        // Assert
        percentage.Should().Be(75.0);
    }

    /// <summary>
    /// 测试详细报告应该包含所有相关信息
    /// </summary>
    [Fact]
    public void GetDetailedReport_ShouldContainAllRelevantInformation()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CurrentStatus = FlowControlStatus.Normal,
            FlowControlType = FlowControlType.XonXoff,
            IsEnabled = true,
            SendRateLimit = 1000,
            CurrentSendRate = 750,
            TotalBytesSent = 10000,
            TotalBytesReceived = 8000,
            IsXoffReceived = false,
            IsRtsPaused = false,
            CongestionThreshold = 80
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("=== 流控制统计报告 ===");
        report.Should().Contain("流控制状态: Normal");
        report.Should().Contain("流控制类型: XonXoff");
        report.Should().Contain("是否启用: True");
        report.Should().Contain("当前速率: 750.00 字节/秒");
        report.Should().Contain("速率限制: 1000 字节/秒");
        report.Should().Contain("使用率: 75.0%");
        report.Should().Contain("总发送: 10,000 字节");
        report.Should().Contain("总接收: 8,000 字节");
        report.Should().Contain("XOFF 状态: 正常");
        report.Should().Contain("RTS 状态: 正常");
        report.Should().Contain("拥塞阈值: 80%");
    }

    /// <summary>
    /// 测试当速率无限制时，详细报告应该显示无限制
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithUnlimitedRate_ShouldShowUnlimited()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            SendRateLimit = 0,
            CurrentSendRate = 750
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("速率限制: 无限制 字节/秒");
    }

    /// <summary>
    /// 测试当收到 XOFF 时，详细报告应该显示 XOFF 状态
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithXoffReceived_ShouldShowXoffStatus()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsXoffReceived = true,
            IsRtsPaused = false
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("XOFF 状态: 已接收");
        report.Should().Contain("RTS 状态: 正常");
    }

    /// <summary>
    /// 测试当 RTS 暂停时，详细报告应该显示 RTS 状态
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithRtsPaused_ShouldShowRtsStatus()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsXoffReceived = false,
            IsRtsPaused = true
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("XOFF 状态: 正常");
        report.Should().Contain("RTS 状态: 暂停");
    }

    /// <summary>
    /// 测试当 XOFF 和 RTS 都暂停时，详细报告应该显示两种状态
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithBothXoffAndRtsPaused_ShouldShowBothStatuses()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsXoffReceived = true,
            IsRtsPaused = true
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("XOFF 状态: 已接收");
        report.Should().Contain("RTS 状态: 暂停");
    }

    /// <summary>
    /// 测试详细报告应该包含格式化的时间戳
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithTimestamp_ShouldIncludeFormattedTime()
    {
        // Arrange
        var testTime = new DateTime(2025, 6, 12, 14, 30, 45, 123);
        var stats = new FlowControlStatistics
        {
            Timestamp = testTime
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("生成时间: 2025-06-12 14:30:45.123");
    }

    /// <summary>
    /// 测试详细报告应该显示拥塞阈值
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithCongestionThreshold_ShouldShowThreshold()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CongestionThreshold = 85
        };

        // Act
        var report = stats.GetDetailedReport();

        // Assert
        report.Should().Contain("拥塞阈值: 85%");
    }

    /// <summary>
    /// 测试 ToString 方法应该返回格式化的字符串
    /// </summary>
    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var testTime = new DateTime(2025, 6, 12, 14, 30, 45, 123);
        var stats = new FlowControlStatistics
        {
            Timestamp = testTime,
            CurrentStatus = FlowControlStatus.Normal,
            FlowControlType = FlowControlType.XonXoff,
            CurrentSendRate = 750.5,
            SendRateLimit = 1000,
            TotalBytesSent = 12345,
            TotalBytesReceived = 9876
        };

        // Act
        var result = stats.ToString();

        // Assert
        result.Should().Contain("流控制统计 [14:30:45.123]");
        result.Should().Contain("状态 Normal");
        result.Should().Contain("类型 XonXoff");
        result.Should().Contain("速率 750.50/1000 (75.0%)");
        result.Should().Contain("发送 12345 字节");
        result.Should().Contain("接收 9876 字节");
    }

    /// <summary>
    /// 测试当速率限制为零时，ToString 应该正确处理无限制情况
    /// </summary>
    [Fact]
    public void ToString_WithZeroRateLimit_ShouldHandleUnlimited()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CurrentSendRate = 500,
            SendRateLimit = 0,
            TotalBytesSent = 1000,
            TotalBytesReceived = 800
        };

        // Act
        var result = stats.ToString();

        // Assert
        result.Should().Contain("速率 500.00/0");
        result.Should().Contain("发送 1000 字节");
        result.Should().Contain("接收 800 字节");
    }



    /// <summary>
    /// 测试当前状态属性应该接受所有有效的流控制状态值
    /// </summary>
    /// <param name="status">要测试的流控制状态</param>
    [Theory]
    [InlineData(FlowControlStatus.Normal)]
    [InlineData(FlowControlStatus.Paused)]
    [InlineData(FlowControlStatus.Error)]
    [InlineData(FlowControlStatus.Congested)]
    public void CurrentStatus_ShouldAcceptAllValidValues(FlowControlStatus status)
    {
        // Arrange & Act
        var stats = new FlowControlStatistics
        {
            CurrentStatus = status
        };

        // Assert
        stats.CurrentStatus.Should().Be(status);
    }

    /// <summary>
    /// 测试流控制类型属性应该接受所有有效的流控制类型值
    /// </summary>
    /// <param name="type">要测试的流控制类型</param>
    [Theory]
    [InlineData(FlowControlType.None)]
    [InlineData(FlowControlType.XonXoff)]
    [InlineData(FlowControlType.RtsCts)]
    [InlineData(FlowControlType.Both)]
    public void FlowControlType_ShouldAcceptAllValidValues(FlowControlType type)
    {
        // Arrange & Act
        var stats = new FlowControlStatistics
        {
            FlowControlType = type
        };

        // Assert
        stats.FlowControlType.Should().Be(type);
    }

    /// <summary>
    /// 测试当存在多个问题时，性能建议应该返回所有相关建议
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithMultipleIssues_ShouldReturnAllSuggestions()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            IsEnabled = false,
            CurrentSendRate = 950,
            SendRateLimit = 1000,
            IsXoffReceived = true,
            IsRtsPaused = true,
            CurrentStatus = FlowControlStatus.Error
        };

        // Act
        var suggestions = stats.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().HaveCount(5);
        suggestions.Should().Contain("流控制未启用，考虑启用以提高数据传输可靠性");
        suggestions.Should().Contain("发送速率接近限制，考虑增加速率限制或优化数据发送");
        suggestions.Should().Contain("接收到 XOFF 信号，对方要求暂停发送，检查接收方处理能力");
        suggestions.Should().Contain("RTS 流控制暂停，检查硬件连接和对方设备状态");
        suggestions.Should().Contain("流控制出现错误，检查配置和硬件连接");
    }

    /// <summary>
    /// 测试在正常值下，速率使用率百分比应该正确计算
    /// </summary>
    [Fact]
    public void RateUsagePercentage_WithNormalValues_ShouldCalculateCorrectly()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CurrentSendRate = 750,
            SendRateLimit = 1000
        };

        // Act & Assert
        stats.RateUsagePercentage.Should().Be(75.0);
    }

    /// <summary>
    /// 测试当发送速率超过限制时，使用率百分比应该返回超过100%的值
    /// </summary>
    [Fact]
    public void RateUsagePercentage_WithExceedingRate_ShouldReturnOver100()
    {
        // Arrange
        var stats = new FlowControlStatistics
        {
            CurrentSendRate = 1200,
            SendRateLimit = 1000
        };

        // Act & Assert
        stats.RateUsagePercentage.Should().Be(120.0);
    }

    /// <summary>
    /// 测试详细报告应该为所有流控制类型显示正确的类型信息
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithAllFlowControlTypes_ShouldShowCorrectType()
    {
        // Arrange & Act & Assert
        var noneStats = new FlowControlStatistics { FlowControlType = FlowControlType.None };
        noneStats.GetDetailedReport().Should().Contain("流控制类型: None");

        var xonXoffStats = new FlowControlStatistics { FlowControlType = FlowControlType.XonXoff };
        xonXoffStats.GetDetailedReport().Should().Contain("流控制类型: XonXoff");

        var rtsCtsStats = new FlowControlStatistics { FlowControlType = FlowControlType.RtsCts };
        rtsCtsStats.GetDetailedReport().Should().Contain("流控制类型: RtsCts");

        var bothStats = new FlowControlStatistics { FlowControlType = FlowControlType.Both };
        bothStats.GetDetailedReport().Should().Contain("流控制类型: Both");
    }

    /// <summary>
    /// 测试详细报告应该为所有流控制状态显示正确的状态信息
    /// </summary>
    [Fact]
    public void GetDetailedReport_WithAllStatuses_ShouldShowCorrectStatus()
    {
        // Arrange & Act & Assert
        var normalStats = new FlowControlStatistics { CurrentStatus = FlowControlStatus.Normal };
        normalStats.GetDetailedReport().Should().Contain("流控制状态: Normal");

        var pausedStats = new FlowControlStatistics { CurrentStatus = FlowControlStatus.Paused };
        pausedStats.GetDetailedReport().Should().Contain("流控制状态: Paused");

        var congestedStats = new FlowControlStatistics { CurrentStatus = FlowControlStatus.Congested };
        congestedStats.GetDetailedReport().Should().Contain("流控制状态: Congested");

        var errorStats = new FlowControlStatistics { CurrentStatus = FlowControlStatus.Error };
        errorStats.GetDetailedReport().Should().Contain("流控制状态: Error");
    }
}
