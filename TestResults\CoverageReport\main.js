/* Chartist.js 0.11.4
 * Copyright © 2019 Gion Kunz
 * Free to use under either the WTFPL license or the MIT license.
 * https://raw.githubusercontent.com/gionkunz/chartist-js/master/LICENSE-WTFPL
 * https://raw.githubusercontent.com/gionkunz/chartist-js/master/LICENSE-MIT
 */

!function (e, t) { "function" == typeof define && define.amd ? define("Chartist", [], (function () { return e.Chartist = t() })) : "object" == typeof module && module.exports ? module.exports = t() : e.Chartist = t() }(this, (function () { var e = { version: "0.11.4" }; return function (e, t) { "use strict"; var i = e.window, n = e.document; t.namespaces = { svg: "http://www.w3.org/2000/svg", xmlns: "http://www.w3.org/2000/xmlns/", xhtml: "http://www.w3.org/1999/xhtml", xlink: "http://www.w3.org/1999/xlink", ct: "http://gionkunz.github.com/chartist-js/ct" }, t.noop = function (e) { return e }, t.alphaNumerate = function (e) { return String.fromCharCode(97 + e % 26) }, t.extend = function (e) { var i, n, s, r; for (e = e || {}, i = 1; i < arguments.length; i++)for (var a in n = arguments[i], r = Object.getPrototypeOf(e), n) "__proto__" === a || "constructor" === a || null !== r && a in r || (s = n[a], e[a] = "object" != typeof s || null === s || s instanceof Array ? s : t.extend(e[a], s)); return e }, t.replaceAll = function (e, t, i) { return e.replace(new RegExp(t, "g"), i) }, t.ensureUnit = function (e, t) { return "number" == typeof e && (e += t), e }, t.quantity = function (e) { if ("string" == typeof e) { var t = /^(\d+)\s*(.*)$/g.exec(e); return { value: +t[1], unit: t[2] || void 0 } } return { value: e } }, t.querySelector = function (e) { return e instanceof Node ? e : n.querySelector(e) }, t.times = function (e) { return Array.apply(null, new Array(e)) }, t.sum = function (e, t) { return e + (t || 0) }, t.mapMultiply = function (e) { return function (t) { return t * e } }, t.mapAdd = function (e) { return function (t) { return t + e } }, t.serialMap = function (e, i) { var n = [], s = Math.max.apply(null, e.map((function (e) { return e.length }))); return t.times(s).forEach((function (t, s) { var r = e.map((function (e) { return e[s] })); n[s] = i.apply(null, r) })), n }, t.roundWithPrecision = function (e, i) { var n = Math.pow(10, i || t.precision); return Math.round(e * n) / n }, t.precision = 8, t.escapingMap = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#039;" }, t.serialize = function (e) { return null == e ? e : ("number" == typeof e ? e = "" + e : "object" == typeof e && (e = JSON.stringify({ data: e })), Object.keys(t.escapingMap).reduce((function (e, i) { return t.replaceAll(e, i, t.escapingMap[i]) }), e)) }, t.deserialize = function (e) { if ("string" != typeof e) return e; e = Object.keys(t.escapingMap).reduce((function (e, i) { return t.replaceAll(e, t.escapingMap[i], i) }), e); try { e = void 0 !== (e = JSON.parse(e)).data ? e.data : e } catch (e) { } return e }, t.createSvg = function (e, i, n, s) { var r; return i = i || "100%", n = n || "100%", Array.prototype.slice.call(e.querySelectorAll("svg")).filter((function (e) { return e.getAttributeNS(t.namespaces.xmlns, "ct") })).forEach((function (t) { e.removeChild(t) })), (r = new t.Svg("svg").attr({ width: i, height: n }).addClass(s))._node.style.width = i, r._node.style.height = n, e.appendChild(r._node), r }, t.normalizeData = function (e, i, n) { var s, r = { raw: e, normalized: {} }; return r.normalized.series = t.getDataArray({ series: e.series || [] }, i, n), s = r.normalized.series.every((function (e) { return e instanceof Array })) ? Math.max.apply(null, r.normalized.series.map((function (e) { return e.length }))) : r.normalized.series.length, r.normalized.labels = (e.labels || []).slice(), Array.prototype.push.apply(r.normalized.labels, t.times(Math.max(0, s - r.normalized.labels.length)).map((function () { return "" }))), i && t.reverseData(r.normalized), r }, t.safeHasProperty = function (e, t) { return null !== e && "object" == typeof e && e.hasOwnProperty(t) }, t.isDataHoleValue = function (e) { return null == e || "number" == typeof e && isNaN(e) }, t.reverseData = function (e) { e.labels.reverse(), e.series.reverse(); for (var t = 0; t < e.series.length; t++)"object" == typeof e.series[t] && void 0 !== e.series[t].data ? e.series[t].data.reverse() : e.series[t] instanceof Array && e.series[t].reverse() }, t.getDataArray = function (e, i, n) { return e.series.map((function e(i) { if (t.safeHasProperty(i, "value")) return e(i.value); if (t.safeHasProperty(i, "data")) return e(i.data); if (i instanceof Array) return i.map(e); if (!t.isDataHoleValue(i)) { if (n) { var s = {}; return "string" == typeof n ? s[n] = t.getNumberOrUndefined(i) : s.y = t.getNumberOrUndefined(i), s.x = i.hasOwnProperty("x") ? t.getNumberOrUndefined(i.x) : s.x, s.y = i.hasOwnProperty("y") ? t.getNumberOrUndefined(i.y) : s.y, s } return t.getNumberOrUndefined(i) } })) }, t.normalizePadding = function (e, t) { return t = t || 0, "number" == typeof e ? { top: e, right: e, bottom: e, left: e } : { top: "number" == typeof e.top ? e.top : t, right: "number" == typeof e.right ? e.right : t, bottom: "number" == typeof e.bottom ? e.bottom : t, left: "number" == typeof e.left ? e.left : t } }, t.getMetaData = function (e, t) { var i = e.data ? e.data[t] : e[t]; return i ? i.meta : void 0 }, t.orderOfMagnitude = function (e) { return Math.floor(Math.log(Math.abs(e)) / Math.LN10) }, t.projectLength = function (e, t, i) { return t / i.range * e }, t.getAvailableHeight = function (e, i) { return Math.max((t.quantity(i.height).value || e.height()) - (i.chartPadding.top + i.chartPadding.bottom) - i.axisX.offset, 0) }, t.getHighLow = function (e, i, n) { var s = { high: void 0 === (i = t.extend({}, i, n ? i["axis" + n.toUpperCase()] : {})).high ? -Number.MAX_VALUE : +i.high, low: void 0 === i.low ? Number.MAX_VALUE : +i.low }, r = void 0 === i.high, a = void 0 === i.low; return (r || a) && function e(t) { if (void 0 !== t) if (t instanceof Array) for (var i = 0; i < t.length; i++)e(t[i]); else { var o = n ? +t[n] : +t; r && o > s.high && (s.high = o), a && o < s.low && (s.low = o) } }(e), (i.referenceValue || 0 === i.referenceValue) && (s.high = Math.max(i.referenceValue, s.high), s.low = Math.min(i.referenceValue, s.low)), s.high <= s.low && (0 === s.low ? s.high = 1 : s.low < 0 ? s.high = 0 : (s.high > 0 || (s.high = 1), s.low = 0)), s }, t.isNumeric = function (e) { return null !== e && isFinite(e) }, t.isFalseyButZero = function (e) { return !e && 0 !== e }, t.getNumberOrUndefined = function (e) { return t.isNumeric(e) ? +e : void 0 }, t.isMultiValue = function (e) { return "object" == typeof e && ("x" in e || "y" in e) }, t.getMultiValue = function (e, i) { return t.isMultiValue(e) ? t.getNumberOrUndefined(e[i || "y"]) : t.getNumberOrUndefined(e) }, t.rho = function (e) { if (1 === e) return e; function t(e, i) { return e % i == 0 ? i : t(i, e % i) } function i(e) { return e * e + 1 } var n, s = 2, r = 2; if (e % 2 == 0) return 2; do { s = i(s) % e, r = i(i(r)) % e, n = t(Math.abs(s - r), e) } while (1 === n); return n }, t.getBounds = function (e, i, n, s) { var r, a, o, l = 0, h = { high: i.high, low: i.low }; h.valueRange = h.high - h.low, h.oom = t.orderOfMagnitude(h.valueRange), h.step = Math.pow(10, h.oom), h.min = Math.floor(h.low / h.step) * h.step, h.max = Math.ceil(h.high / h.step) * h.step, h.range = h.max - h.min, h.numberOfSteps = Math.round(h.range / h.step); var u = t.projectLength(e, h.step, h) < n, c = s ? t.rho(h.range) : 0; if (s && t.projectLength(e, 1, h) >= n) h.step = 1; else if (s && c < h.step && t.projectLength(e, c, h) >= n) h.step = c; else for (; ;) { if (u && t.projectLength(e, h.step, h) <= n) h.step *= 2; else { if (u || !(t.projectLength(e, h.step / 2, h) >= n)) break; if (h.step /= 2, s && h.step % 1 != 0) { h.step *= 2; break } } if (l++ > 1e3) throw new Error("Exceeded maximum number of iterations while optimizing scale step!") } var d = 2221e-19; function p(e, t) { return e === (e += t) && (e *= 1 + (t > 0 ? d : -d)), e } for (h.step = Math.max(h.step, d), a = h.min, o = h.max; a + h.step <= h.low;)a = p(a, h.step); for (; o - h.step >= h.high;)o = p(o, -h.step); h.min = a, h.max = o, h.range = h.max - h.min; var f = []; for (r = h.min; r <= h.max; r = p(r, h.step)) { var m = t.roundWithPrecision(r); m !== f[f.length - 1] && f.push(m) } return h.values = f, h }, t.polarToCartesian = function (e, t, i, n) { var s = (n - 90) * Math.PI / 180; return { x: e + i * Math.cos(s), y: t + i * Math.sin(s) } }, t.createChartRect = function (e, i, n) { var s = !(!i.axisX && !i.axisY), r = s ? i.axisY.offset : 0, a = s ? i.axisX.offset : 0, o = e.width() || t.quantity(i.width).value || 0, l = e.height() || t.quantity(i.height).value || 0, h = t.normalizePadding(i.chartPadding, n); o = Math.max(o, r + h.left + h.right), l = Math.max(l, a + h.top + h.bottom); var u = { padding: h, width: function () { return this.x2 - this.x1 }, height: function () { return this.y1 - this.y2 } }; return s ? ("start" === i.axisX.position ? (u.y2 = h.top + a, u.y1 = Math.max(l - h.bottom, u.y2 + 1)) : (u.y2 = h.top, u.y1 = Math.max(l - h.bottom - a, u.y2 + 1)), "start" === i.axisY.position ? (u.x1 = h.left + r, u.x2 = Math.max(o - h.right, u.x1 + 1)) : (u.x1 = h.left, u.x2 = Math.max(o - h.right - r, u.x1 + 1))) : (u.x1 = h.left, u.x2 = Math.max(o - h.right, u.x1 + 1), u.y2 = h.top, u.y1 = Math.max(l - h.bottom, u.y2 + 1)), u }, t.createGrid = function (e, i, n, s, r, a, o, l) { var h = {}; h[n.units.pos + "1"] = e, h[n.units.pos + "2"] = e, h[n.counterUnits.pos + "1"] = s, h[n.counterUnits.pos + "2"] = s + r; var u = a.elem("line", h, o.join(" ")); l.emit("draw", t.extend({ type: "grid", axis: n, index: i, group: a, element: u }, h)) }, t.createGridBackground = function (e, t, i, n) { var s = e.elem("rect", { x: t.x1, y: t.y2, width: t.width(), height: t.height() }, i, !0); n.emit("draw", { type: "gridBackground", group: e, element: s }) }, t.createLabel = function (e, i, s, r, a, o, l, h, u, c, d) { var p, f = {}; if (f[a.units.pos] = e + l[a.units.pos], f[a.counterUnits.pos] = l[a.counterUnits.pos], f[a.units.len] = i, f[a.counterUnits.len] = Math.max(0, o - 10), c) { var m = n.createElement("span"); m.className = u.join(" "), m.setAttribute("xmlns", t.namespaces.xhtml), m.innerText = r[s], m.style[a.units.len] = Math.round(f[a.units.len]) + "px", m.style[a.counterUnits.len] = Math.round(f[a.counterUnits.len]) + "px", p = h.foreignObject(m, t.extend({ style: "overflow: visible;" }, f)) } else p = h.elem("text", f, u.join(" ")).text(r[s]); d.emit("draw", t.extend({ type: "label", axis: a, index: s, group: h, element: p, text: r[s] }, f)) }, t.getSeriesOption = function (e, t, i) { if (e.name && t.series && t.series[e.name]) { var n = t.series[e.name]; return n.hasOwnProperty(i) ? n[i] : t[i] } return t[i] }, t.optionsProvider = function (e, n, s) { var r, a, o = t.extend({}, e), l = []; function h(e) { var l = r; if (r = t.extend({}, o), n) for (a = 0; a < n.length; a++) { i.matchMedia(n[a][0]).matches && (r = t.extend(r, n[a][1])) } s && e && s.emit("optionsChanged", { previousOptions: l, currentOptions: r }) } if (!i.matchMedia) throw "window.matchMedia not found! Make sure you're using a polyfill."; if (n) for (a = 0; a < n.length; a++) { var u = i.matchMedia(n[a][0]); u.addListener(h), l.push(u) } return h(), { removeMediaQueryListeners: function () { l.forEach((function (e) { e.removeListener(h) })) }, getCurrentOptions: function () { return t.extend({}, r) } } }, t.splitIntoSegments = function (e, i, n) { n = t.extend({}, { increasingX: !1, fillHoles: !1 }, n); for (var s = [], r = !0, a = 0; a < e.length; a += 2)void 0 === t.getMultiValue(i[a / 2].value) ? n.fillHoles || (r = !0) : (n.increasingX && a >= 2 && e[a] <= e[a - 2] && (r = !0), r && (s.push({ pathCoordinates: [], valueData: [] }), r = !1), s[s.length - 1].pathCoordinates.push(e[a], e[a + 1]), s[s.length - 1].valueData.push(i[a / 2])); return s } }(this || global, e), function (e, t) { "use strict"; t.Interpolation = {}, t.Interpolation.none = function (e) { return e = t.extend({}, { fillHoles: !1 }, e), function (i, n) { for (var s = new t.Svg.Path, r = !0, a = 0; a < i.length; a += 2) { var o = i[a], l = i[a + 1], h = n[a / 2]; void 0 !== t.getMultiValue(h.value) ? (r ? s.move(o, l, !1, h) : s.line(o, l, !1, h), r = !1) : e.fillHoles || (r = !0) } return s } }, t.Interpolation.simple = function (e) { e = t.extend({}, { divisor: 2, fillHoles: !1 }, e); var i = 1 / Math.max(1, e.divisor); return function (n, s) { for (var r, a, o, l = new t.Svg.Path, h = 0; h < n.length; h += 2) { var u = n[h], c = n[h + 1], d = (u - r) * i, p = s[h / 2]; void 0 !== p.value ? (void 0 === o ? l.move(u, c, !1, p) : l.curve(r + d, a, u - d, c, u, c, !1, p), r = u, a = c, o = p) : e.fillHoles || (r = u = o = void 0) } return l } }, t.Interpolation.cardinal = function (e) { e = t.extend({}, { tension: 1, fillHoles: !1 }, e); var i = Math.min(1, Math.max(0, e.tension)), n = 1 - i; return function s(r, a) { var o = t.splitIntoSegments(r, a, { fillHoles: e.fillHoles }); if (o.length) { if (o.length > 1) { var l = []; return o.forEach((function (e) { l.push(s(e.pathCoordinates, e.valueData)) })), t.Svg.Path.join(l) } if (r = o[0].pathCoordinates, a = o[0].valueData, r.length <= 4) return t.Interpolation.none()(r, a); for (var h = (new t.Svg.Path).move(r[0], r[1], !1, a[0]), u = 0, c = r.length; c - 2 > u; u += 2) { var d = [{ x: +r[u - 2], y: +r[u - 1] }, { x: +r[u], y: +r[u + 1] }, { x: +r[u + 2], y: +r[u + 3] }, { x: +r[u + 4], y: +r[u + 5] }]; c - 4 === u ? d[3] = d[2] : u || (d[0] = { x: +r[u], y: +r[u + 1] }), h.curve(i * (-d[0].x + 6 * d[1].x + d[2].x) / 6 + n * d[2].x, i * (-d[0].y + 6 * d[1].y + d[2].y) / 6 + n * d[2].y, i * (d[1].x + 6 * d[2].x - d[3].x) / 6 + n * d[2].x, i * (d[1].y + 6 * d[2].y - d[3].y) / 6 + n * d[2].y, d[2].x, d[2].y, !1, a[(u + 2) / 2]) } return h } return t.Interpolation.none()([]) } }, t.Interpolation.monotoneCubic = function (e) { return e = t.extend({}, { fillHoles: !1 }, e), function i(n, s) { var r = t.splitIntoSegments(n, s, { fillHoles: e.fillHoles, increasingX: !0 }); if (r.length) { if (r.length > 1) { var a = []; return r.forEach((function (e) { a.push(i(e.pathCoordinates, e.valueData)) })), t.Svg.Path.join(a) } if (n = r[0].pathCoordinates, s = r[0].valueData, n.length <= 4) return t.Interpolation.none()(n, s); var o, l, h = [], u = [], c = n.length / 2, d = [], p = [], f = [], m = []; for (o = 0; o < c; o++)h[o] = n[2 * o], u[o] = n[2 * o + 1]; for (o = 0; o < c - 1; o++)f[o] = u[o + 1] - u[o], m[o] = h[o + 1] - h[o], p[o] = f[o] / m[o]; for (d[0] = p[0], d[c - 1] = p[c - 2], o = 1; o < c - 1; o++)0 === p[o] || 0 === p[o - 1] || p[o - 1] > 0 != p[o] > 0 ? d[o] = 0 : (d[o] = 3 * (m[o - 1] + m[o]) / ((2 * m[o] + m[o - 1]) / p[o - 1] + (m[o] + 2 * m[o - 1]) / p[o]), isFinite(d[o]) || (d[o] = 0)); for (l = (new t.Svg.Path).move(h[0], u[0], !1, s[0]), o = 0; o < c - 1; o++)l.curve(h[o] + m[o] / 3, u[o] + d[o] * m[o] / 3, h[o + 1] - m[o] / 3, u[o + 1] - d[o + 1] * m[o] / 3, h[o + 1], u[o + 1], !1, s[o + 1]); return l } return t.Interpolation.none()([]) } }, t.Interpolation.step = function (e) { return e = t.extend({}, { postpone: !0, fillHoles: !1 }, e), function (i, n) { for (var s, r, a, o = new t.Svg.Path, l = 0; l < i.length; l += 2) { var h = i[l], u = i[l + 1], c = n[l / 2]; void 0 !== c.value ? (void 0 === a ? o.move(h, u, !1, c) : (e.postpone ? o.line(h, r, !1, a) : o.line(s, u, !1, c), o.line(h, u, !1, c)), s = h, r = u, a = c) : e.fillHoles || (s = r = a = void 0) } return o } } }(this || global, e), function (e, t) { "use strict"; t.EventEmitter = function () { var e = []; return { addEventHandler: function (t, i) { e[t] = e[t] || [], e[t].push(i) }, removeEventHandler: function (t, i) { e[t] && (i ? (e[t].splice(e[t].indexOf(i), 1), 0 === e[t].length && delete e[t]) : delete e[t]) }, emit: function (t, i) { e[t] && e[t].forEach((function (e) { e(i) })), e["*"] && e["*"].forEach((function (e) { e(t, i) })) } } } }(this || global, e), function (e, t) { "use strict"; t.Class = { extend: function (e, i) { var n = i || this.prototype || t.Class, s = Object.create(n); t.Class.cloneDefinitions(s, e); var r = function () { var e, i = s.constructor || function () { }; return e = this === t ? Object.create(s) : this, i.apply(e, Array.prototype.slice.call(arguments, 0)), e }; return r.prototype = s, r.super = n, r.extend = this.extend, r }, cloneDefinitions: function () { var e = function (e) { var t = []; if (e.length) for (var i = 0; i < e.length; i++)t.push(e[i]); return t }(arguments), t = e[0]; return e.splice(1, e.length - 1).forEach((function (e) { Object.getOwnPropertyNames(e).forEach((function (i) { delete t[i], Object.defineProperty(t, i, Object.getOwnPropertyDescriptor(e, i)) })) })), t } } }(this || global, e), function (e, t) { "use strict"; var i = e.window; function n() { i.addEventListener("resize", this.resizeListener), this.optionsProvider = t.optionsProvider(this.options, this.responsiveOptions, this.eventEmitter), this.eventEmitter.addEventHandler("optionsChanged", function () { this.update() }.bind(this)), this.options.plugins && this.options.plugins.forEach(function (e) { e instanceof Array ? e[0](this, e[1]) : e(this) }.bind(this)), this.eventEmitter.emit("data", { type: "initial", data: this.data }), this.createChart(this.optionsProvider.getCurrentOptions()), this.initializeTimeoutId = void 0 } t.Base = t.Class.extend({ constructor: function (e, i, s, r, a) { this.container = t.querySelector(e), this.data = i || {}, this.data.labels = this.data.labels || [], this.data.series = this.data.series || [], this.defaultOptions = s, this.options = r, this.responsiveOptions = a, this.eventEmitter = t.EventEmitter(), this.supportsForeignObject = t.Svg.isSupported("Extensibility"), this.supportsAnimations = t.Svg.isSupported("AnimationEventsAttribute"), this.resizeListener = function () { this.update() }.bind(this), this.container && (this.container.__chartist__ && this.container.__chartist__.detach(), this.container.__chartist__ = this), this.initializeTimeoutId = setTimeout(n.bind(this), 0) }, optionsProvider: void 0, container: void 0, svg: void 0, eventEmitter: void 0, createChart: function () { throw new Error("Base chart type can't be instantiated!") }, update: function (e, i, n) { return e && (this.data = e || {}, this.data.labels = this.data.labels || [], this.data.series = this.data.series || [], this.eventEmitter.emit("data", { type: "update", data: this.data })), i && (this.options = t.extend({}, n ? this.options : this.defaultOptions, i), this.initializeTimeoutId || (this.optionsProvider.removeMediaQueryListeners(), this.optionsProvider = t.optionsProvider(this.options, this.responsiveOptions, this.eventEmitter))), this.initializeTimeoutId || this.createChart(this.optionsProvider.getCurrentOptions()), this }, detach: function () { return this.initializeTimeoutId ? i.clearTimeout(this.initializeTimeoutId) : (i.removeEventListener("resize", this.resizeListener), this.optionsProvider.removeMediaQueryListeners()), this }, on: function (e, t) { return this.eventEmitter.addEventHandler(e, t), this }, off: function (e, t) { return this.eventEmitter.removeEventHandler(e, t), this }, version: t.version, supportsForeignObject: !1 }) }(this || global, e), function (e, t) { "use strict"; var i = e.document; t.Svg = t.Class.extend({ constructor: function (e, n, s, r, a) { e instanceof Element ? this._node = e : (this._node = i.createElementNS(t.namespaces.svg, e), "svg" === e && this.attr({ "xmlns:ct": t.namespaces.ct })), n && this.attr(n), s && this.addClass(s), r && (a && r._node.firstChild ? r._node.insertBefore(this._node, r._node.firstChild) : r._node.appendChild(this._node)) }, attr: function (e, i) { return "string" == typeof e ? i ? this._node.getAttributeNS(i, e) : this._node.getAttribute(e) : (Object.keys(e).forEach(function (i) { if (void 0 !== e[i]) if (-1 !== i.indexOf(":")) { var n = i.split(":"); this._node.setAttributeNS(t.namespaces[n[0]], i, e[i]) } else this._node.setAttribute(i, e[i]) }.bind(this)), this) }, elem: function (e, i, n, s) { return new t.Svg(e, i, n, this, s) }, parent: function () { return this._node.parentNode instanceof SVGElement ? new t.Svg(this._node.parentNode) : null }, root: function () { for (var e = this._node; "svg" !== e.nodeName;)e = e.parentNode; return new t.Svg(e) }, querySelector: function (e) { var i = this._node.querySelector(e); return i ? new t.Svg(i) : null }, querySelectorAll: function (e) { var i = this._node.querySelectorAll(e); return i.length ? new t.Svg.List(i) : null }, getNode: function () { return this._node }, foreignObject: function (e, n, s, r) { if ("string" == typeof e) { var a = i.createElement("div"); a.innerHTML = e, e = a.firstChild } e.setAttribute("xmlns", t.namespaces.xmlns); var o = this.elem("foreignObject", n, s, r); return o._node.appendChild(e), o }, text: function (e) { return this._node.appendChild(i.createTextNode(e)), this }, empty: function () { for (; this._node.firstChild;)this._node.removeChild(this._node.firstChild); return this }, remove: function () { return this._node.parentNode.removeChild(this._node), this.parent() }, replace: function (e) { return this._node.parentNode.replaceChild(e._node, this._node), e }, append: function (e, t) { return t && this._node.firstChild ? this._node.insertBefore(e._node, this._node.firstChild) : this._node.appendChild(e._node), this }, classes: function () { return this._node.getAttribute("class") ? this._node.getAttribute("class").trim().split(/\s+/) : [] }, addClass: function (e) { return this._node.setAttribute("class", this.classes(this._node).concat(e.trim().split(/\s+/)).filter((function (e, t, i) { return i.indexOf(e) === t })).join(" ")), this }, removeClass: function (e) { var t = e.trim().split(/\s+/); return this._node.setAttribute("class", this.classes(this._node).filter((function (e) { return -1 === t.indexOf(e) })).join(" ")), this }, removeAllClasses: function () { return this._node.setAttribute("class", ""), this }, height: function () { return this._node.getBoundingClientRect().height }, width: function () { return this._node.getBoundingClientRect().width }, animate: function (e, i, n) { return void 0 === i && (i = !0), Object.keys(e).forEach(function (s) { function r(e, i) { var r, a, o, l = {}; e.easing && (o = e.easing instanceof Array ? e.easing : t.Svg.Easing[e.easing], delete e.easing), e.begin = t.ensureUnit(e.begin, "ms"), e.dur = t.ensureUnit(e.dur, "ms"), o && (e.calcMode = "spline", e.keySplines = o.join(" "), e.keyTimes = "0;1"), i && (e.fill = "freeze", l[s] = e.from, this.attr(l), a = t.quantity(e.begin || 0).value, e.begin = "indefinite"), r = this.elem("animate", t.extend({ attributeName: s }, e)), i && setTimeout(function () { try { r._node.beginElement() } catch (t) { l[s] = e.to, this.attr(l), r.remove() } }.bind(this), a), n && r._node.addEventListener("beginEvent", function () { n.emit("animationBegin", { element: this, animate: r._node, params: e }) }.bind(this)), r._node.addEventListener("endEvent", function () { n && n.emit("animationEnd", { element: this, animate: r._node, params: e }), i && (l[s] = e.to, this.attr(l), r.remove()) }.bind(this)) } e[s] instanceof Array ? e[s].forEach(function (e) { r.bind(this)(e, !1) }.bind(this)) : r.bind(this)(e[s], i) }.bind(this)), this } }), t.Svg.isSupported = function (e) { return i.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#" + e, "1.1") }; t.Svg.Easing = { easeInSine: [.47, 0, .745, .715], easeOutSine: [.39, .575, .565, 1], easeInOutSine: [.445, .05, .55, .95], easeInQuad: [.55, .085, .68, .53], easeOutQuad: [.25, .46, .45, .94], easeInOutQuad: [.455, .03, .515, .955], easeInCubic: [.55, .055, .675, .19], easeOutCubic: [.215, .61, .355, 1], easeInOutCubic: [.645, .045, .355, 1], easeInQuart: [.895, .03, .685, .22], easeOutQuart: [.165, .84, .44, 1], easeInOutQuart: [.77, 0, .175, 1], easeInQuint: [.755, .05, .855, .06], easeOutQuint: [.23, 1, .32, 1], easeInOutQuint: [.86, 0, .07, 1], easeInExpo: [.95, .05, .795, .035], easeOutExpo: [.19, 1, .22, 1], easeInOutExpo: [1, 0, 0, 1], easeInCirc: [.6, .04, .98, .335], easeOutCirc: [.075, .82, .165, 1], easeInOutCirc: [.785, .135, .15, .86], easeInBack: [.6, -.28, .735, .045], easeOutBack: [.175, .885, .32, 1.275], easeInOutBack: [.68, -.55, .265, 1.55] }, t.Svg.List = t.Class.extend({ constructor: function (e) { var i = this; this.svgElements = []; for (var n = 0; n < e.length; n++)this.svgElements.push(new t.Svg(e[n])); Object.keys(t.Svg.prototype).filter((function (e) { return -1 === ["constructor", "parent", "querySelector", "querySelectorAll", "replace", "append", "classes", "height", "width"].indexOf(e) })).forEach((function (e) { i[e] = function () { var n = Array.prototype.slice.call(arguments, 0); return i.svgElements.forEach((function (i) { t.Svg.prototype[e].apply(i, n) })), i } })) } }) }(this || global, e), function (e, t) { "use strict"; var i = { m: ["x", "y"], l: ["x", "y"], c: ["x1", "y1", "x2", "y2", "x", "y"], a: ["rx", "ry", "xAr", "lAf", "sf", "x", "y"] }, n = { accuracy: 3 }; function s(e, i, n, s, r, a) { var o = t.extend({ command: r ? e.toLowerCase() : e.toUpperCase() }, i, a ? { data: a } : {}); n.splice(s, 0, o) } function r(e, t) { e.forEach((function (n, s) { i[n.command.toLowerCase()].forEach((function (i, r) { t(n, i, s, r, e) })) })) } t.Svg.Path = t.Class.extend({ constructor: function (e, i) { this.pathElements = [], this.pos = 0, this.close = e, this.options = t.extend({}, n, i) }, position: function (e) { return void 0 !== e ? (this.pos = Math.max(0, Math.min(this.pathElements.length, e)), this) : this.pos }, remove: function (e) { return this.pathElements.splice(this.pos, e), this }, move: function (e, t, i, n) { return s("M", { x: +e, y: +t }, this.pathElements, this.pos++, i, n), this }, line: function (e, t, i, n) { return s("L", { x: +e, y: +t }, this.pathElements, this.pos++, i, n), this }, curve: function (e, t, i, n, r, a, o, l) { return s("C", { x1: +e, y1: +t, x2: +i, y2: +n, x: +r, y: +a }, this.pathElements, this.pos++, o, l), this }, arc: function (e, t, i, n, r, a, o, l, h) { return s("A", { rx: +e, ry: +t, xAr: +i, lAf: +n, sf: +r, x: +a, y: +o }, this.pathElements, this.pos++, l, h), this }, scale: function (e, t) { return r(this.pathElements, (function (i, n) { i[n] *= "x" === n[0] ? e : t })), this }, translate: function (e, t) { return r(this.pathElements, (function (i, n) { i[n] += "x" === n[0] ? e : t })), this }, transform: function (e) { return r(this.pathElements, (function (t, i, n, s, r) { var a = e(t, i, n, s, r); (a || 0 === a) && (t[i] = a) })), this }, parse: function (e) { var n = e.replace(/([A-Za-z])([0-9])/g, "$1 $2").replace(/([0-9])([A-Za-z])/g, "$1 $2").split(/[\s,]+/).reduce((function (e, t) { return t.match(/[A-Za-z]/) && e.push([]), e[e.length - 1].push(t), e }), []); "Z" === n[n.length - 1][0].toUpperCase() && n.pop(); var s = n.map((function (e) { var n = e.shift(), s = i[n.toLowerCase()]; return t.extend({ command: n }, s.reduce((function (t, i, n) { return t[i] = +e[n], t }), {})) })), r = [this.pos, 0]; return Array.prototype.push.apply(r, s), Array.prototype.splice.apply(this.pathElements, r), this.pos += s.length, this }, stringify: function () { var e = Math.pow(10, this.options.accuracy); return this.pathElements.reduce(function (t, n) { var s = i[n.command.toLowerCase()].map(function (t) { return this.options.accuracy ? Math.round(n[t] * e) / e : n[t] }.bind(this)); return t + n.command + s.join(",") }.bind(this), "") + (this.close ? "Z" : "") }, clone: function (e) { var i = new t.Svg.Path(e || this.close); return i.pos = this.pos, i.pathElements = this.pathElements.slice().map((function (e) { return t.extend({}, e) })), i.options = t.extend({}, this.options), i }, splitByCommand: function (e) { var i = [new t.Svg.Path]; return this.pathElements.forEach((function (n) { n.command === e.toUpperCase() && 0 !== i[i.length - 1].pathElements.length && i.push(new t.Svg.Path), i[i.length - 1].pathElements.push(n) })), i } }), t.Svg.Path.elementDescriptions = i, t.Svg.Path.join = function (e, i, n) { for (var s = new t.Svg.Path(i, n), r = 0; r < e.length; r++)for (var a = e[r], o = 0; o < a.pathElements.length; o++)s.pathElements.push(a.pathElements[o]); return s } }(this || global, e), function (e, t) { "use strict"; e.window, e.document; var i = { x: { pos: "x", len: "width", dir: "horizontal", rectStart: "x1", rectEnd: "x2", rectOffset: "y2" }, y: { pos: "y", len: "height", dir: "vertical", rectStart: "y2", rectEnd: "y1", rectOffset: "x1" } }; t.Axis = t.Class.extend({ constructor: function (e, t, n, s) { this.units = e, this.counterUnits = e === i.x ? i.y : i.x, this.chartRect = t, this.axisLength = t[e.rectEnd] - t[e.rectStart], this.gridOffset = t[e.rectOffset], this.ticks = n, this.options = s }, createGridAndLabels: function (e, i, n, s, r) { var a = s["axis" + this.units.pos.toUpperCase()], o = this.ticks.map(this.projectValue.bind(this)), l = this.ticks.map(a.labelInterpolationFnc); o.forEach(function (h, u) { var c, d = { x: 0, y: 0 }; c = o[u + 1] ? o[u + 1] - h : Math.max(this.axisLength - h, 30), t.isFalseyButZero(l[u]) && "" !== l[u] || ("x" === this.units.pos ? (h = this.chartRect.x1 + h, d.x = s.axisX.labelOffset.x, "start" === s.axisX.position ? d.y = this.chartRect.padding.top + s.axisX.labelOffset.y + (n ? 5 : 20) : d.y = this.chartRect.y1 + s.axisX.labelOffset.y + (n ? 5 : 20)) : (h = this.chartRect.y1 - h, d.y = s.axisY.labelOffset.y - (n ? c : 0), "start" === s.axisY.position ? d.x = n ? this.chartRect.padding.left + s.axisY.labelOffset.x : this.chartRect.x1 - 10 : d.x = this.chartRect.x2 + s.axisY.labelOffset.x + 10), a.showGrid && t.createGrid(h, u, this, this.gridOffset, this.chartRect[this.counterUnits.len](), e, [s.classNames.grid, s.classNames[this.units.dir]], r), a.showLabel && t.createLabel(h, c, u, l, this, a.offset, d, i, [s.classNames.label, s.classNames[this.units.dir], "start" === a.position ? s.classNames[a.position] : s.classNames.end], n, r)) }.bind(this)) }, projectValue: function (e, t, i) { throw new Error("Base axis can't be instantiated!") } }), t.Axis.units = i }(this || global, e), function (e, t) { "use strict"; e.window, e.document; t.AutoScaleAxis = t.Axis.extend({ constructor: function (e, i, n, s) { var r = s.highLow || t.getHighLow(i, s, e.pos); this.bounds = t.getBounds(n[e.rectEnd] - n[e.rectStart], r, s.scaleMinSpace || 20, s.onlyInteger), this.range = { min: this.bounds.min, max: this.bounds.max }, t.AutoScaleAxis.super.constructor.call(this, e, n, this.bounds.values, s) }, projectValue: function (e) { return this.axisLength * (+t.getMultiValue(e, this.units.pos) - this.bounds.min) / this.bounds.range } }) }(this || global, e), function (e, t) { "use strict"; e.window, e.document; t.FixedScaleAxis = t.Axis.extend({ constructor: function (e, i, n, s) { var r = s.highLow || t.getHighLow(i, s, e.pos); this.divisor = s.divisor || 1, this.ticks = s.ticks || t.times(this.divisor).map(function (e, t) { return r.low + (r.high - r.low) / this.divisor * t }.bind(this)), this.ticks.sort((function (e, t) { return e - t })), this.range = { min: r.low, max: r.high }, t.FixedScaleAxis.super.constructor.call(this, e, n, this.ticks, s), this.stepLength = this.axisLength / this.divisor }, projectValue: function (e) { return this.axisLength * (+t.getMultiValue(e, this.units.pos) - this.range.min) / (this.range.max - this.range.min) } }) }(this || global, e), function (e, t) { "use strict"; e.window, e.document; t.StepAxis = t.Axis.extend({ constructor: function (e, i, n, s) { t.StepAxis.super.constructor.call(this, e, n, s.ticks, s); var r = Math.max(1, s.ticks.length - (s.stretch ? 1 : 0)); this.stepLength = this.axisLength / r }, projectValue: function (e, t) { return this.stepLength * t } }) }(this || global, e), function (e, t) { "use strict"; e.window, e.document; var i = { axisX: { offset: 30, position: "end", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: t.noop, type: void 0 }, axisY: { offset: 40, position: "start", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: t.noop, type: void 0, scaleMinSpace: 20, onlyInteger: !1 }, width: void 0, height: void 0, showLine: !0, showPoint: !0, showArea: !1, areaBase: 0, lineSmooth: !0, showGridBackground: !1, low: void 0, high: void 0, chartPadding: { top: 15, right: 15, bottom: 5, left: 10 }, fullWidth: !1, reverseData: !1, classNames: { chart: "ct-chart-line", label: "ct-label", labelGroup: "ct-labels", series: "ct-series", line: "ct-line", point: "ct-point", area: "ct-area", grid: "ct-grid", gridGroup: "ct-grids", gridBackground: "ct-grid-background", vertical: "ct-vertical", horizontal: "ct-horizontal", start: "ct-start", end: "ct-end" } }; t.Line = t.Base.extend({ constructor: function (e, n, s, r) { t.Line.super.constructor.call(this, e, n, i, t.extend({}, i, s), r) }, createChart: function (e) { var n = t.normalizeData(this.data, e.reverseData, !0); this.svg = t.createSvg(this.container, e.width, e.height, e.classNames.chart); var s, r, a = this.svg.elem("g").addClass(e.classNames.gridGroup), o = this.svg.elem("g"), l = this.svg.elem("g").addClass(e.classNames.labelGroup), h = t.createChartRect(this.svg, e, i.padding); s = void 0 === e.axisX.type ? new t.StepAxis(t.Axis.units.x, n.normalized.series, h, t.extend({}, e.axisX, { ticks: n.normalized.labels, stretch: e.fullWidth })) : e.axisX.type.call(t, t.Axis.units.x, n.normalized.series, h, e.axisX), r = void 0 === e.axisY.type ? new t.AutoScaleAxis(t.Axis.units.y, n.normalized.series, h, t.extend({}, e.axisY, { high: t.isNumeric(e.high) ? e.high : e.axisY.high, low: t.isNumeric(e.low) ? e.low : e.axisY.low })) : e.axisY.type.call(t, t.Axis.units.y, n.normalized.series, h, e.axisY), s.createGridAndLabels(a, l, this.supportsForeignObject, e, this.eventEmitter), r.createGridAndLabels(a, l, this.supportsForeignObject, e, this.eventEmitter), e.showGridBackground && t.createGridBackground(a, h, e.classNames.gridBackground, this.eventEmitter), n.raw.series.forEach(function (i, a) { var l = o.elem("g"); l.attr({ "ct:series-name": i.name, "ct:meta": t.serialize(i.meta) }), l.addClass([e.classNames.series, i.className || e.classNames.series + "-" + t.alphaNumerate(a)].join(" ")); var u = [], c = []; n.normalized.series[a].forEach(function (e, o) { var l = { x: h.x1 + s.projectValue(e, o, n.normalized.series[a]), y: h.y1 - r.projectValue(e, o, n.normalized.series[a]) }; u.push(l.x, l.y), c.push({ value: e, valueIndex: o, meta: t.getMetaData(i, o) }) }.bind(this)); var d = { lineSmooth: t.getSeriesOption(i, e, "lineSmooth"), showPoint: t.getSeriesOption(i, e, "showPoint"), showLine: t.getSeriesOption(i, e, "showLine"), showArea: t.getSeriesOption(i, e, "showArea"), areaBase: t.getSeriesOption(i, e, "areaBase") }, p = ("function" == typeof d.lineSmooth ? d.lineSmooth : d.lineSmooth ? t.Interpolation.monotoneCubic() : t.Interpolation.none())(u, c); if (d.showPoint && p.pathElements.forEach(function (n) { var o = l.elem("line", { x1: n.x, y1: n.y, x2: n.x + .01, y2: n.y }, e.classNames.point).attr({ "ct:value": [n.data.value.x, n.data.value.y].filter(t.isNumeric).join(","), "ct:meta": t.serialize(n.data.meta) }); this.eventEmitter.emit("draw", { type: "point", value: n.data.value, index: n.data.valueIndex, meta: n.data.meta, series: i, seriesIndex: a, axisX: s, axisY: r, group: l, element: o, x: n.x, y: n.y }) }.bind(this)), d.showLine) { var f = l.elem("path", { d: p.stringify() }, e.classNames.line, !0); this.eventEmitter.emit("draw", { type: "line", values: n.normalized.series[a], path: p.clone(), chartRect: h, index: a, series: i, seriesIndex: a, seriesMeta: i.meta, axisX: s, axisY: r, group: l, element: f }) } if (d.showArea && r.range) { var m = Math.max(Math.min(d.areaBase, r.range.max), r.range.min), g = h.y1 - r.projectValue(m); p.splitByCommand("M").filter((function (e) { return e.pathElements.length > 1 })).map((function (e) { var t = e.pathElements[0], i = e.pathElements[e.pathElements.length - 1]; return e.clone(!0).position(0).remove(1).move(t.x, g).line(t.x, t.y).position(e.pathElements.length + 1).line(i.x, g) })).forEach(function (t) { var o = l.elem("path", { d: t.stringify() }, e.classNames.area, !0); this.eventEmitter.emit("draw", { type: "area", values: n.normalized.series[a], path: t.clone(), series: i, seriesIndex: a, axisX: s, axisY: r, chartRect: h, index: a, group: l, element: o }) }.bind(this)) } }.bind(this)), this.eventEmitter.emit("created", { bounds: r.bounds, chartRect: h, axisX: s, axisY: r, svg: this.svg, options: e }) } }) }(this || global, e), function (e, t) { "use strict"; e.window, e.document; var i = { axisX: { offset: 30, position: "end", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: t.noop, scaleMinSpace: 30, onlyInteger: !1 }, axisY: { offset: 40, position: "start", labelOffset: { x: 0, y: 0 }, showLabel: !0, showGrid: !0, labelInterpolationFnc: t.noop, scaleMinSpace: 20, onlyInteger: !1 }, width: void 0, height: void 0, high: void 0, low: void 0, referenceValue: 0, chartPadding: { top: 15, right: 15, bottom: 5, left: 10 }, seriesBarDistance: 15, stackBars: !1, stackMode: "accumulate", horizontalBars: !1, distributeSeries: !1, reverseData: !1, showGridBackground: !1, classNames: { chart: "ct-chart-bar", horizontalBars: "ct-horizontal-bars", label: "ct-label", labelGroup: "ct-labels", series: "ct-series", bar: "ct-bar", grid: "ct-grid", gridGroup: "ct-grids", gridBackground: "ct-grid-background", vertical: "ct-vertical", horizontal: "ct-horizontal", start: "ct-start", end: "ct-end" } }; t.Bar = t.Base.extend({ constructor: function (e, n, s, r) { t.Bar.super.constructor.call(this, e, n, i, t.extend({}, i, s), r) }, createChart: function (e) { var n, s; e.distributeSeries ? (n = t.normalizeData(this.data, e.reverseData, e.horizontalBars ? "x" : "y")).normalized.series = n.normalized.series.map((function (e) { return [e] })) : n = t.normalizeData(this.data, e.reverseData, e.horizontalBars ? "x" : "y"), this.svg = t.createSvg(this.container, e.width, e.height, e.classNames.chart + (e.horizontalBars ? " " + e.classNames.horizontalBars : "")); var r = this.svg.elem("g").addClass(e.classNames.gridGroup), a = this.svg.elem("g"), o = this.svg.elem("g").addClass(e.classNames.labelGroup); if (e.stackBars && 0 !== n.normalized.series.length) { var l = t.serialMap(n.normalized.series, (function () { return Array.prototype.slice.call(arguments).map((function (e) { return e })).reduce((function (e, t) { return { x: e.x + (t && t.x) || 0, y: e.y + (t && t.y) || 0 } }), { x: 0, y: 0 }) })); s = t.getHighLow([l], e, e.horizontalBars ? "x" : "y") } else s = t.getHighLow(n.normalized.series, e, e.horizontalBars ? "x" : "y"); s.high = +e.high || (0 === e.high ? 0 : s.high), s.low = +e.low || (0 === e.low ? 0 : s.low); var h, u, c, d, p, f = t.createChartRect(this.svg, e, i.padding); u = e.distributeSeries && e.stackBars ? n.normalized.labels.slice(0, 1) : n.normalized.labels, e.horizontalBars ? (h = d = void 0 === e.axisX.type ? new t.AutoScaleAxis(t.Axis.units.x, n.normalized.series, f, t.extend({}, e.axisX, { highLow: s, referenceValue: 0 })) : e.axisX.type.call(t, t.Axis.units.x, n.normalized.series, f, t.extend({}, e.axisX, { highLow: s, referenceValue: 0 })), c = p = void 0 === e.axisY.type ? new t.StepAxis(t.Axis.units.y, n.normalized.series, f, { ticks: u }) : e.axisY.type.call(t, t.Axis.units.y, n.normalized.series, f, e.axisY)) : (c = d = void 0 === e.axisX.type ? new t.StepAxis(t.Axis.units.x, n.normalized.series, f, { ticks: u }) : e.axisX.type.call(t, t.Axis.units.x, n.normalized.series, f, e.axisX), h = p = void 0 === e.axisY.type ? new t.AutoScaleAxis(t.Axis.units.y, n.normalized.series, f, t.extend({}, e.axisY, { highLow: s, referenceValue: 0 })) : e.axisY.type.call(t, t.Axis.units.y, n.normalized.series, f, t.extend({}, e.axisY, { highLow: s, referenceValue: 0 }))); var m = e.horizontalBars ? f.x1 + h.projectValue(0) : f.y1 - h.projectValue(0), g = []; c.createGridAndLabels(r, o, this.supportsForeignObject, e, this.eventEmitter), h.createGridAndLabels(r, o, this.supportsForeignObject, e, this.eventEmitter), e.showGridBackground && t.createGridBackground(r, f, e.classNames.gridBackground, this.eventEmitter), n.raw.series.forEach(function (i, s) { var r, o, l = s - (n.raw.series.length - 1) / 2; r = e.distributeSeries && !e.stackBars ? c.axisLength / n.normalized.series.length / 2 : e.distributeSeries && e.stackBars ? c.axisLength / 2 : c.axisLength / n.normalized.series[s].length / 2, (o = a.elem("g")).attr({ "ct:series-name": i.name, "ct:meta": t.serialize(i.meta) }), o.addClass([e.classNames.series, i.className || e.classNames.series + "-" + t.alphaNumerate(s)].join(" ")), n.normalized.series[s].forEach(function (a, u) { var v, x, y, b; if (b = e.distributeSeries && !e.stackBars ? s : e.distributeSeries && e.stackBars ? 0 : u, v = e.horizontalBars ? { x: f.x1 + h.projectValue(a && a.x ? a.x : 0, u, n.normalized.series[s]), y: f.y1 - c.projectValue(a && a.y ? a.y : 0, b, n.normalized.series[s]) } : { x: f.x1 + c.projectValue(a && a.x ? a.x : 0, b, n.normalized.series[s]), y: f.y1 - h.projectValue(a && a.y ? a.y : 0, u, n.normalized.series[s]) }, c instanceof t.StepAxis && (c.options.stretch || (v[c.units.pos] += r * (e.horizontalBars ? -1 : 1)), v[c.units.pos] += e.stackBars || e.distributeSeries ? 0 : l * e.seriesBarDistance * (e.horizontalBars ? -1 : 1)), y = g[u] || m, g[u] = y - (m - v[c.counterUnits.pos]), void 0 !== a) { var w = {}; w[c.units.pos + "1"] = v[c.units.pos], w[c.units.pos + "2"] = v[c.units.pos], !e.stackBars || "accumulate" !== e.stackMode && e.stackMode ? (w[c.counterUnits.pos + "1"] = m, w[c.counterUnits.pos + "2"] = v[c.counterUnits.pos]) : (w[c.counterUnits.pos + "1"] = y, w[c.counterUnits.pos + "2"] = g[u]), w.x1 = Math.min(Math.max(w.x1, f.x1), f.x2), w.x2 = Math.min(Math.max(w.x2, f.x1), f.x2), w.y1 = Math.min(Math.max(w.y1, f.y2), f.y1), w.y2 = Math.min(Math.max(w.y2, f.y2), f.y1); var E = t.getMetaData(i, u); x = o.elem("line", w, e.classNames.bar).attr({ "ct:value": [a.x, a.y].filter(t.isNumeric).join(","), "ct:meta": t.serialize(E) }), this.eventEmitter.emit("draw", t.extend({ type: "bar", value: a, index: u, meta: E, series: i, seriesIndex: s, axisX: d, axisY: p, chartRect: f, group: o, element: x }, w)) } }.bind(this)) }.bind(this)), this.eventEmitter.emit("created", { bounds: h.bounds, chartRect: f, axisX: d, axisY: p, svg: this.svg, options: e }) } }) }(this || global, e), function (e, t) { "use strict"; e.window, e.document; var i = { width: void 0, height: void 0, chartPadding: 5, classNames: { chartPie: "ct-chart-pie", chartDonut: "ct-chart-donut", series: "ct-series", slicePie: "ct-slice-pie", sliceDonut: "ct-slice-donut", sliceDonutSolid: "ct-slice-donut-solid", label: "ct-label" }, startAngle: 0, total: void 0, donut: !1, donutSolid: !1, donutWidth: 60, showLabel: !0, labelOffset: 0, labelPosition: "inside", labelInterpolationFnc: t.noop, labelDirection: "neutral", reverseData: !1, ignoreEmptyValues: !1 }; function n(e, t, i) { var n = t.x > e.x; return n && "explode" === i || !n && "implode" === i ? "start" : n && "implode" === i || !n && "explode" === i ? "end" : "middle" } t.Pie = t.Base.extend({ constructor: function (e, n, s, r) { t.Pie.super.constructor.call(this, e, n, i, t.extend({}, i, s), r) }, createChart: function (e) { var s, r, a, o, l, h = t.normalizeData(this.data), u = [], c = e.startAngle; this.svg = t.createSvg(this.container, e.width, e.height, e.donut ? e.classNames.chartDonut : e.classNames.chartPie), r = t.createChartRect(this.svg, e, i.padding), a = Math.min(r.width() / 2, r.height() / 2), l = e.total || h.normalized.series.reduce((function (e, t) { return e + t }), 0); var d = t.quantity(e.donutWidth); "%" === d.unit && (d.value *= a / 100), a -= e.donut && !e.donutSolid ? d.value / 2 : 0, o = "outside" === e.labelPosition || e.donut && !e.donutSolid ? a : "center" === e.labelPosition ? 0 : e.donutSolid ? a - d.value / 2 : a / 2, o += e.labelOffset; var p = { x: r.x1 + r.width() / 2, y: r.y2 + r.height() / 2 }, f = 1 === h.raw.series.filter((function (e) { return e.hasOwnProperty("value") ? 0 !== e.value : 0 !== e })).length; h.raw.series.forEach(function (e, t) { u[t] = this.svg.elem("g", null, null) }.bind(this)), e.showLabel && (s = this.svg.elem("g", null, null)), h.raw.series.forEach(function (i, r) { if (0 !== h.normalized.series[r] || !e.ignoreEmptyValues) { u[r].attr({ "ct:series-name": i.name }), u[r].addClass([e.classNames.series, i.className || e.classNames.series + "-" + t.alphaNumerate(r)].join(" ")); var m = l > 0 ? c + h.normalized.series[r] / l * 360 : 0, g = Math.max(0, c - (0 === r || f ? 0 : .2)); m - g >= 359.99 && (m = g + 359.99); var v, x, y, b = t.polarToCartesian(p.x, p.y, a, g), w = t.polarToCartesian(p.x, p.y, a, m), E = new t.Svg.Path(!e.donut || e.donutSolid).move(w.x, w.y).arc(a, a, 0, m - c > 180, 0, b.x, b.y); e.donut ? e.donutSolid && (y = a - d.value, v = t.polarToCartesian(p.x, p.y, y, c - (0 === r || f ? 0 : .2)), x = t.polarToCartesian(p.x, p.y, y, m), E.line(v.x, v.y), E.arc(y, y, 0, m - c > 180, 1, x.x, x.y)) : E.line(p.x, p.y); var S = e.classNames.slicePie; e.donut && (S = e.classNames.sliceDonut, e.donutSolid && (S = e.classNames.sliceDonutSolid)); var A = u[r].elem("path", { d: E.stringify() }, S); if (A.attr({ "ct:value": h.normalized.series[r], "ct:meta": t.serialize(i.meta) }), e.donut && !e.donutSolid && (A._node.style.strokeWidth = d.value + "px"), this.eventEmitter.emit("draw", { type: "slice", value: h.normalized.series[r], totalDataSum: l, index: r, meta: i.meta, series: i, group: u[r], element: A, path: E.clone(), center: p, radius: a, startAngle: c, endAngle: m }), e.showLabel) { var z, M; z = 1 === h.raw.series.length ? { x: p.x, y: p.y } : t.polarToCartesian(p.x, p.y, o, c + (m - c) / 2), M = h.normalized.labels && !t.isFalseyButZero(h.normalized.labels[r]) ? h.normalized.labels[r] : h.normalized.series[r]; var O = e.labelInterpolationFnc(M, r); if (O || 0 === O) { var C = s.elem("text", { dx: z.x, dy: z.y, "text-anchor": n(p, z, e.labelDirection) }, e.classNames.label).text("" + O); this.eventEmitter.emit("draw", { type: "label", index: r, group: s, element: C, text: "" + O, x: z.x, y: z.y }) } } c = m } }.bind(this)), this.eventEmitter.emit("created", { chartRect: r, svg: this.svg, options: e }) }, determineAnchorPosition: n }) }(this || global, e), e }));

var i, l, selectedLine = null;

/* Navigate to hash without browser history entry */
var navigateToHash = function () {
    if (window.history !== undefined && window.history.replaceState !== undefined) {
        window.history.replaceState(undefined, undefined, this.getAttribute("href"));
    }
};

var hashLinks = document.getElementsByClassName('navigatetohash');
for (i = 0, l = hashLinks.length; i < l; i++) {
    hashLinks[i].addEventListener('click', navigateToHash);
}

/* Switch test method */
var switchTestMethod = function () {
    var method = this.getAttribute("value");
    console.log("Selected test method: " + method);

    var lines, i, l, coverageData, lineAnalysis, cells;

    lines = document.querySelectorAll('.lineAnalysis tr');

    for (i = 1, l = lines.length; i < l; i++) {
        coverageData = JSON.parse(lines[i].getAttribute('data-coverage').replace(/'/g, '"'));
        lineAnalysis = coverageData[method];
        cells = lines[i].querySelectorAll('td');
        if (lineAnalysis === undefined) {
            lineAnalysis = coverageData.AllTestMethods;
            if (lineAnalysis.LVS !== 'gray') {
                cells[0].setAttribute('class', 'red');
                cells[1].innerText = cells[1].textContent = '0';
                cells[4].setAttribute('class', 'lightred');
            }
        } else {
            cells[0].setAttribute('class', lineAnalysis.LVS);
            cells[1].innerText = cells[1].textContent = lineAnalysis.VC;
            cells[4].setAttribute('class', 'light' + lineAnalysis.LVS);
        }
    }
};

var testMethods = document.getElementsByClassName('switchtestmethod');
for (i = 0, l = testMethods.length; i < l; i++) {
    testMethods[i].addEventListener('change', switchTestMethod);
}

/* Highlight test method by line */
var toggleLine = function () {
    if (selectedLine === this) {
        selectedLine = null;
    } else {
        selectedLine = null;
        unhighlightTestMethods();
        highlightTestMethods.call(this);
        selectedLine = this;
    }
    
};
var highlightTestMethods = function () {
    if (selectedLine !== null) {
        return;
    }

    var lineAnalysis;
    var coverageData = JSON.parse(this.getAttribute('data-coverage').replace(/'/g, '"'));
    var testMethods = document.getElementsByClassName('testmethod');

    for (i = 0, l = testMethods.length; i < l; i++) {
        lineAnalysis = coverageData[testMethods[i].id];
        if (lineAnalysis === undefined) {
            testMethods[i].className = testMethods[i].className.replace(/\s*light.+/g, "");
        } else {
            testMethods[i].className += ' light' + lineAnalysis.LVS;
        }
    }
};
var unhighlightTestMethods = function () {
    if (selectedLine !== null) {
        return;
    }

    var testMethods = document.getElementsByClassName('testmethod');
    for (i = 0, l = testMethods.length; i < l; i++) {
        testMethods[i].className = testMethods[i].className.replace(/\s*light.+/g, "");
    }
};
var coverableLines = document.getElementsByClassName('coverableline');
for (i = 0, l = coverableLines.length; i < l; i++) {
    coverableLines[i].addEventListener('click', toggleLine);
    coverableLines[i].addEventListener('mouseenter', highlightTestMethods);
    coverableLines[i].addEventListener('mouseleave', unhighlightTestMethods);
}

/* History charts */
var renderChart = function (chart) {
    // Remove current children (e.g. PNG placeholder)
    while (chart.firstChild) {
        chart.firstChild.remove();
    }

    var chartData = window[chart.getAttribute('data-data')];
    var options = {
        axisY: {
            type: undefined,
            onlyInteger: true
        },
        lineSmooth: false,
        low: 0,
        high: 100,
        scaleMinSpace: 20,
        onlyInteger: true,
        fullWidth: true
    };
    var lineChart = new Chartist.Line(chart, {
        labels: [],
        series: chartData.series
    }, options);

    /* Zoom */
    var zoomButtonDiv = document.createElement("div");
    zoomButtonDiv.className = "toggleZoom";
    var zoomButtonLink = document.createElement("a");
    zoomButtonLink.setAttribute("href", "");
    var zoomButtonText = document.createElement("i");
    zoomButtonText.className = "icon-search-plus";

    zoomButtonLink.appendChild(zoomButtonText);
    zoomButtonDiv.appendChild(zoomButtonLink);

    chart.appendChild(zoomButtonDiv);

    zoomButtonDiv.addEventListener('click', function (event) {
        event.preventDefault();

        if (options.axisY.type === undefined) {
            options.axisY.type = Chartist.AutoScaleAxis;
            zoomButtonText.className = "icon-search-minus";
        } else {
            options.axisY.type = undefined;
            zoomButtonText.className = "icon-search-plus";
        }

        lineChart.update(null, options);
    });

    var tooltip = document.createElement("div");
    tooltip.className = "tooltip";

    chart.appendChild(tooltip);

    /* Tooltips */
    var showToolTip = function () {
        var index = this.getAttribute('ct:meta');

        tooltip.innerHTML = chartData.tooltips[index];
        tooltip.style.display = 'block';
    };

    var moveToolTip = function (event) {
        var box = chart.getBoundingClientRect();
        var left = event.pageX - box.left - window.pageXOffset;
        var top = event.pageY - box.top - window.pageYOffset;

        left = left + 20;
        top = top - tooltip.offsetHeight / 2;

        if (left + tooltip.offsetWidth > box.width) {
            left -= tooltip.offsetWidth + 40;
        }

        if (top < 0) {
            top = 0;
        }

        if (top + tooltip.offsetHeight > box.height) {
            top = box.height - tooltip.offsetHeight;
        }

        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';
    };

    var hideToolTip = function () {
        tooltip.style.display = 'none';
    };
    chart.addEventListener('mousemove', moveToolTip);

    lineChart.on('created', function () {
        var chartPoints = chart.getElementsByClassName('ct-point');
        for (i = 0, l = chartPoints.length; i < l; i++) {
            chartPoints[i].addEventListener('mousemove', showToolTip);
            chartPoints[i].addEventListener('mouseout', hideToolTip);
        }
    });
};

var charts = document.getElementsByClassName('historychart');
for (i = 0, l = charts.length; i < l; i++) {
    renderChart(charts[i]);
}

var assemblies = [
  {
    "name": "Alicres.SerialPort",
    "classes": [
      { "name": "Alicres.SerialPort.Exceptions.SerialPortConfigurationException", "rp": "Alicres.SerialPort_SerialPortConfigurationException.html", "cl": 15, "ucl": 0, "cal": 15, "tl": 204, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Exceptions.SerialPortConnectionException", "rp": "Alicres.SerialPort_SerialPortConnectionException.html", "cl": 15, "ucl": 0, "cal": 15, "tl": 204, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Exceptions.SerialPortDataException", "rp": "Alicres.SerialPort_SerialPortDataException.html", "cl": 15, "ucl": 0, "cal": 15, "tl": 204, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Exceptions.SerialPortException", "rp": "Alicres.SerialPort_SerialPortException.html", "cl": 18, "ucl": 0, "cal": 18, "tl": 204, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Extensions.GlobalReconnectOptions", "rp": "Alicres.SerialPort_GlobalReconnectOptions.html", "cl": 3, "ucl": 0, "cal": 3, "tl": 96, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Extensions.SerialPortServiceOptions", "rp": "Alicres.SerialPort_SerialPortServiceOptions.html", "cl": 4, "ucl": 0, "cal": 4, "tl": 96, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Extensions.ServiceCollectionExtensions", "rp": "Alicres.SerialPort_ServiceCollectionExtensions.html", "cl": 13, "ucl": 0, "cal": 13, "tl": 96, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs", "rp": "Alicres.SerialPort_SerialPortAddedEventArgs.html", "cl": 5, "ucl": 0, "cal": 5, "tl": 159, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs", "rp": "Alicres.SerialPort_SerialPortRemovedEventArgs.html", "cl": 5, "ucl": 0, "cal": 5, "tl": 159, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.BufferOverflowEventArgs", "rp": "Alicres.SerialPort_BufferOverflowEventArgs.html", "cl": 7, "ucl": 7, "cal": 14, "tl": 190, "cb": 1, "tb": 2, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.BufferStatistics", "rp": "Alicres.SerialPort_BufferStatistics.html", "cl": 75, "ucl": 28, "cal": 103, "tl": 307, "cb": 22, "tb": 44, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.BufferWarningEventArgs", "rp": "Alicres.SerialPort_BufferWarningEventArgs.html", "cl": 7, "ucl": 7, "cal": 14, "tl": 190, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.CongestionDetectedEventArgs", "rp": "Alicres.SerialPort_CongestionDetectedEventArgs.html", "cl": 0, "ucl": 14, "cal": 14, "tl": 190, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.FlowControlStatistics", "rp": "Alicres.SerialPort_FlowControlStatistics.html", "cl": 79, "ucl": 24, "cal": 103, "tl": 307, "cb": 20, "tb": 40, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.FlowControlStatusChangedEventArgs", "rp": "Alicres.SerialPort_FlowControlStatusChangedEventArgs.html", "cl": 7, "ucl": 5, "cal": 12, "tl": 190, "cb": 0, "tb": 0, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.SerialPortConfiguration", "rp": "Alicres.SerialPort_SerialPortConfiguration.html", "cl": 40, "ucl": 0, "cal": 40, "tl": 155, "cb": 18, "tb": 18, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.SerialPortData", "rp": "Alicres.SerialPort_SerialPortData.html", "cl": 56, "ucl": 0, "cal": 56, "tl": 167, "cb": 18, "tb": 20, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.SerialPortDataReceivedEventArgs", "rp": "Alicres.SerialPort_SerialPortDataReceivedEventArgs.html", "cl": 8, "ucl": 0, "cal": 8, "tl": 130, "cb": 2, "tb": 2, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.SerialPortErrorEventArgs", "rp": "Alicres.SerialPort_SerialPortErrorEventArgs.html", "cl": 14, "ucl": 0, "cal": 14, "tl": 130, "cb": 6, "tb": 6, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.SerialPortStatus", "rp": "Alicres.SerialPort_SerialPortStatus.html", "cl": 46, "ucl": 0, "cal": 46, "tl": 175, "cb": 6, "tb": 6, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Models.SerialPortStatusChangedEventArgs", "rp": "Alicres.SerialPort_SerialPortStatusChangedEventArgs.html", "cl": 14, "ucl": 0, "cal": 14, "tl": 130, "cb": 2, "tb": 2, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Services.AdvancedBufferManager", "rp": "Alicres.SerialPort_AdvancedBufferManager.html", "cl": 133, "ucl": 18, "cal": 151, "tl": 310, "cb": 50, "tb": 76, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Services.FlowControlManager", "rp": "Alicres.SerialPort_FlowControlManager.html", "cl": 134, "ucl": 39, "cal": 173, "tl": 353, "cb": 76, "tb": 118, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Services.SerialPortManager", "rp": "Alicres.SerialPort_SerialPortManager.html", "cl": 159, "ucl": 38, "cal": 197, "tl": 445, "cb": 25, "tb": 38, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
      { "name": "Alicres.SerialPort.Services.SerialPortService", "rp": "Alicres.SerialPort_SerialPortService.html", "cl": 239, "ucl": 278, "cal": 517, "tl": 980, "cb": 41, "tb": 140, "cm": 0, "fcm": 0, "tm": 0, "lch": [], "bch": [], "mch": [], "mfch": [], "hc": [], "metrics": { } },
    ]},
];

var metrics = [{ "name": "Crap Score", "abbreviation": "crp", "explanationUrl": "https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" }, { "name": "Cyclomatic complexity", "abbreviation": "cc", "explanationUrl": "https://en.wikipedia.org/wiki/Cyclomatic_complexity" }, { "name": "Line coverage", "abbreviation": "cov", "explanationUrl": "https://en.wikipedia.org/wiki/Code_coverage" }, { "name": "Branch coverage", "abbreviation": "bcov", "explanationUrl": "https://en.wikipedia.org/wiki/Code_coverage" }];

var historicCoverageExecutionTimes = [];

var riskHotspotMetrics = [
      { "name": "Crap Score", "explanationUrl": "https://googletesting.blogspot.de/2011/02/this-code-is-crap.html" },
      { "name": "Cyclomatic complexity", "explanationUrl": "https://en.wikipedia.org/wiki/Cyclomatic_complexity" },
];

var riskHotspots = [
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Models.BufferStatistics", "reportPath": "Alicres.SerialPort_BufferStatistics.html", "methodName": "GetPerformanceSuggestions()", "methodShortName": "GetPerformanceSuggestions()", "fileIndex": 0, "line": 117,
    "metrics": [
      { "value": 156, "exceeded": true },
      { "value": 12, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Models.FlowControlStatistics", "reportPath": "Alicres.SerialPort_FlowControlStatistics.html", "methodName": "GetPerformanceSuggestions()", "methodShortName": "GetPerformanceSuggestions()", "fileIndex": 0, "line": 260,
    "metrics": [
      { "value": 156, "exceeded": true },
      { "value": 12, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.FlowControlManager", "reportPath": "Alicres.SerialPort_FlowControlManager.html", "methodName": "MonitorFlowControl(System.Object)", "methodShortName": "MonitorFlowControl(...)", "fileIndex": 0, "line": 303,
    "metrics": [
      { "value": 110, "exceeded": true },
      { "value": 10, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.SerialPortService", "reportPath": "Alicres.SerialPort_SerialPortService.html", "methodName": "OnDataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)", "methodShortName": "OnDataReceived(...)", "fileIndex": 0, "line": 618,
    "metrics": [
      { "value": 110, "exceeded": true },
      { "value": 10, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Models.BufferStatistics", "reportPath": "Alicres.SerialPort_BufferStatistics.html", "methodName": "GetHealthStatus()", "methodShortName": "GetHealthStatus()", "fileIndex": 0, "line": 96,
    "metrics": [
      { "value": 72, "exceeded": true },
      { "value": 8, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.SerialPortService", "reportPath": "Alicres.SerialPort_SerialPortService.html", "methodName": "ReconnectAsync()", "methodShortName": "ReconnectAsync()", "fileIndex": 0, "line": 738,
    "metrics": [
      { "value": 72, "exceeded": true },
      { "value": 8, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.SerialPortService", "reportPath": "Alicres.SerialPort_SerialPortService.html", "methodName": "SendAsync()", "methodShortName": "SendAsync()", "fileIndex": 0, "line": 255,
    "metrics": [
      { "value": 54, "exceeded": true },
      { "value": 10, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Models.FlowControlStatistics", "reportPath": "Alicres.SerialPort_FlowControlStatistics.html", "methodName": "GetDetailedReport()", "methodShortName": "GetDetailedReport()", "fileIndex": 0, "line": 233,
    "metrics": [
      { "value": 42, "exceeded": true },
      { "value": 6, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.FlowControlManager", "reportPath": "Alicres.SerialPort_FlowControlManager.html", "methodName": "RecordReceive(System.Int32)", "methodShortName": "RecordReceive(...)", "fileIndex": 0, "line": 164,
    "metrics": [
      { "value": 42, "exceeded": true },
      { "value": 6, "exceeded": false },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.AdvancedBufferManager", "reportPath": "Alicres.SerialPort_AdvancedBufferManager.html", "methodName": "HandleBufferOverflow(Alicres.SerialPort.Models.SerialPortData)", "methodShortName": "HandleBufferOverflow(...)", "fileIndex": 0, "line": 204,
    "metrics": [
      { "value": 37, "exceeded": true },
      { "value": 20, "exceeded": true },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.FlowControlManager", "reportPath": "Alicres.SerialPort_FlowControlManager.html", "methodName": "CanSend(System.Int32)", "methodShortName": "CanSend(...)", "fileIndex": 0, "line": 103,
    "metrics": [
      { "value": 26, "exceeded": false },
      { "value": 26, "exceeded": true },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.FlowControlManager", "reportPath": "Alicres.SerialPort_FlowControlManager.html", "methodName": "ProcessFlowControlData(System.Byte[])", "methodShortName": "ProcessFlowControlData(...)", "fileIndex": 0, "line": 177,
    "metrics": [
      { "value": 26, "exceeded": false },
      { "value": 26, "exceeded": true },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Models.SerialPortConfiguration", "reportPath": "Alicres.SerialPort_SerialPortConfiguration.html", "methodName": "IsValid()", "methodShortName": "IsValid()", "fileIndex": 0, "line": 131,
    "metrics": [
      { "value": 18, "exceeded": false },
      { "value": 18, "exceeded": true },
    ]},
  {
    "assembly": "Alicres.SerialPort", "class": "Alicres.SerialPort.Services.FlowControlManager", "reportPath": "Alicres.SerialPort_FlowControlManager.html", "methodName": "SetRtsFlowControl(System.Boolean)", "methodShortName": "SetRtsFlowControl(...)", "fileIndex": 0, "line": 215,
    "metrics": [
      { "value": 17, "exceeded": false },
      { "value": 16, "exceeded": true },
    ]},
];

var branchCoverageAvailable = true;
var methodCoverageAvailable = false;
var maximumDecimalPlacesForCoverageQuotas = 1;


var translations = {
'top': 'Top:',
'all': 'All',
'assembly': 'Assembly',
'class': 'Class',
'method': 'Method',
'lineCoverage': 'Line coverage',
'noGrouping': 'No grouping',
'byAssembly': 'By assembly',
'byNamespace': 'By namespace, Level:',
'all': 'All',
'collapseAll': 'Collapse all',
'expandAll': 'Expand all',
'grouping': 'Grouping:',
'filter': 'Filter:',
'name': 'Name',
'covered': 'Covered',
'uncovered': 'Uncovered',
'coverable': 'Coverable',
'total': 'Total',
'coverage': 'Line coverage',
'branchCoverage': 'Branch coverage',
'methodCoverage': 'Method coverage',
'fullMethodCoverage': 'Full method coverage',
'percentage': 'Percentage',
'history': 'Coverage history',
'compareHistory': 'Compare with:',
'date': 'Date',
'allChanges': 'All changes',
'selectCoverageTypes': 'Select coverage types',
'selectCoverageTypesAndMetrics': 'Select coverage types & metrics',
'coverageTypes': 'Coverage types',
'metrics': 'Metrics',
'methodCoverageProVersion': 'Feature is only available for sponsors',
'lineCoverageIncreaseOnly': 'Line coverage: Increase only',
'lineCoverageDecreaseOnly': 'Line coverage: Decrease only',
'branchCoverageIncreaseOnly': 'Branch coverage: Increase only',
'branchCoverageDecreaseOnly': 'Branch coverage: Decrease only',
'methodCoverageIncreaseOnly': 'Method coverage: Increase only',
'methodCoverageDecreaseOnly': 'Method coverage: Decrease only',
'fullMethodCoverageIncreaseOnly': 'Full method coverage: Increase only',
'fullMethodCoverageDecreaseOnly': 'Full method coverage: Decrease only'
};


(()=>{"use strict";var e,_={},p={};function n(e){var a=p[e];if(void 0!==a)return a.exports;var r=p[e]={exports:{}};return _[e](r,r.exports,n),r.exports}n.m=_,e=[],n.O=(a,r,u,l)=>{if(!r){var o=1/0;for(f=0;f<e.length;f++){for(var[r,u,l]=e[f],v=!0,t=0;t<r.length;t++)(!1&l||o>=l)&&Object.keys(n.O).every(h=>n.O[h](r[t]))?r.splice(t--,1):(v=!1,l<o&&(o=l));if(v){e.splice(f--,1);var c=u();void 0!==c&&(a=c)}}return a}l=l||0;for(var f=e.length;f>0&&e[f-1][2]>l;f--)e[f]=e[f-1];e[f]=[r,u,l]},n.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return n.d(a,{a}),a},n.d=(e,a)=>{for(var r in a)n.o(a,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:a[r]})},n.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),(()=>{var e={121:0};n.O.j=u=>0===e[u];var a=(u,l)=>{var t,c,[f,o,v]=l,s=0;if(f.some(d=>0!==e[d])){for(t in o)n.o(o,t)&&(n.m[t]=o[t]);if(v)var b=v(n)}for(u&&u(l);s<f.length;s++)n.o(e,c=f[s])&&e[c]&&e[c][0](),e[c]=0;return n.O(b)},r=self.webpackChunkcoverage_app=self.webpackChunkcoverage_app||[];r.forEach(a.bind(null,0)),r.push=a.bind(null,r.push.bind(r))})()})();

"use strict";(self.webpackChunkcoverage_app=self.webpackChunkcoverage_app||[]).push([[461],{50:(te,Q,ve)=>{ve(935)},935:()=>{const te=globalThis;function Q(e){return(te.__Zone_symbol_prefix||"__zone_symbol__")+e}const Te=Object.getOwnPropertyDescriptor,Le=Object.defineProperty,Ie=Object.getPrototypeOf,_t=Object.create,Et=Array.prototype.slice,Me="addEventListener",Ze="removeEventListener",Ae=Q(Me),je=Q(Ze),ae="true",le="false",Pe=Q("");function He(e,r){return Zone.current.wrap(e,r)}function xe(e,r,c,t,i){return Zone.current.scheduleMacroTask(e,r,c,t,i)}const j=Q,Ce=typeof window<"u",ge=Ce?window:void 0,$=Ce&&ge||globalThis;function Ve(e,r){for(let c=e.length-1;c>=0;c--)"function"==typeof e[c]&&(e[c]=He(e[c],r+"_"+c));return e}function We(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&typeof e.set>"u")}const qe=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,De=!("nw"in $)&&typeof $.process<"u"&&"[object process]"===$.process.toString(),Ge=!De&&!qe&&!(!Ce||!ge.HTMLElement),Xe=typeof $.process<"u"&&"[object process]"===$.process.toString()&&!qe&&!(!Ce||!ge.HTMLElement),Se={},pt=j("enable_beforeunload"),Ye=function(e){if(!(e=e||$.event))return;let r=Se[e.type];r||(r=Se[e.type]=j("ON_PROPERTY"+e.type));const c=this||e.target||$,t=c[r];let i;return Ge&&c===ge&&"error"===e.type?(i=t&&t.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===i&&e.preventDefault()):(i=t&&t.apply(this,arguments),"beforeunload"===e.type&&$[pt]&&"string"==typeof i?e.returnValue=i:null!=i&&!i&&e.preventDefault()),i};function $e(e,r,c){let t=Te(e,r);if(!t&&c&&Te(c,r)&&(t={enumerable:!0,configurable:!0}),!t||!t.configurable)return;const i=j("on"+r+"patched");if(e.hasOwnProperty(i)&&e[i])return;delete t.writable,delete t.value;const u=t.get,E=t.set,T=r.slice(2);let y=Se[T];y||(y=Se[T]=j("ON_PROPERTY"+T)),t.set=function(D){let d=this;!d&&e===$&&(d=$),d&&("function"==typeof d[y]&&d.removeEventListener(T,Ye),E&&E.call(d,null),d[y]=D,"function"==typeof D&&d.addEventListener(T,Ye,!1))},t.get=function(){let D=this;if(!D&&e===$&&(D=$),!D)return null;const d=D[y];if(d)return d;if(u){let w=u.call(this);if(w)return t.set.call(this,w),"function"==typeof D.removeAttribute&&D.removeAttribute(r),w}return null},Le(e,r,t),e[i]=!0}function Ke(e,r,c){if(r)for(let t=0;t<r.length;t++)$e(e,"on"+r[t],c);else{const t=[];for(const i in e)"on"==i.slice(0,2)&&t.push(i);for(let i=0;i<t.length;i++)$e(e,t[i],c)}}const re=j("originalInstance");function we(e){const r=$[e];if(!r)return;$[j(e)]=r,$[e]=function(){const i=Ve(arguments,e);switch(i.length){case 0:this[re]=new r;break;case 1:this[re]=new r(i[0]);break;case 2:this[re]=new r(i[0],i[1]);break;case 3:this[re]=new r(i[0],i[1],i[2]);break;case 4:this[re]=new r(i[0],i[1],i[2],i[3]);break;default:throw new Error("Arg list too long.")}},fe($[e],r);const c=new r(function(){});let t;for(t in c)"XMLHttpRequest"===e&&"responseBlob"===t||function(i){"function"==typeof c[i]?$[e].prototype[i]=function(){return this[re][i].apply(this[re],arguments)}:Le($[e].prototype,i,{set:function(u){"function"==typeof u?(this[re][i]=He(u,e+"."+i),fe(this[re][i],u)):this[re][i]=u},get:function(){return this[re][i]}})}(t);for(t in r)"prototype"!==t&&r.hasOwnProperty(t)&&($[e][t]=r[t])}function ue(e,r,c){let t=e;for(;t&&!t.hasOwnProperty(r);)t=Ie(t);!t&&e[r]&&(t=e);const i=j(r);let u=null;if(t&&(!(u=t[i])||!t.hasOwnProperty(i))&&(u=t[i]=t[r],We(t&&Te(t,r)))){const T=c(u,i,r);t[r]=function(){return T(this,arguments)},fe(t[r],u)}return u}function yt(e,r,c){let t=null;function i(u){const E=u.data;return E.args[E.cbIdx]=function(){u.invoke.apply(this,arguments)},t.apply(E.target,E.args),u}t=ue(e,r,u=>function(E,T){const y=c(E,T);return y.cbIdx>=0&&"function"==typeof T[y.cbIdx]?xe(y.name,T[y.cbIdx],y,i):u.apply(E,T)})}function fe(e,r){e[j("OriginalDelegate")]=r}let Je=!1,Be=!1;function kt(){if(Je)return Be;Je=!0;try{const e=ge.navigator.userAgent;(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/"))&&(Be=!0)}catch{}return Be}function Qe(e){return"function"==typeof e}function et(e){return"number"==typeof e}let pe=!1;if(typeof window<"u")try{const e=Object.defineProperty({},"passive",{get:function(){pe=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch{pe=!1}const vt={useG:!0},ne={},tt={},nt=new RegExp("^"+Pe+"(\\w+)(true|false)$"),rt=j("propagationStopped");function ot(e,r){const c=(r?r(e):e)+le,t=(r?r(e):e)+ae,i=Pe+c,u=Pe+t;ne[e]={},ne[e][le]=i,ne[e][ae]=u}function bt(e,r,c,t){const i=t&&t.add||Me,u=t&&t.rm||Ze,E=t&&t.listeners||"eventListeners",T=t&&t.rmAll||"removeAllListeners",y=j(i),D="."+i+":",d="prependListener",w="."+d+":",Z=function(k,h,H){if(k.isRemoved)return;const V=k.callback;let Y;"object"==typeof V&&V.handleEvent&&(k.callback=g=>V.handleEvent(g),k.originalDelegate=V);try{k.invoke(k,h,[H])}catch(g){Y=g}const G=k.options;return G&&"object"==typeof G&&G.once&&h[u].call(h,H.type,k.originalDelegate?k.originalDelegate:k.callback,G),Y};function x(k,h,H){if(!(h=h||e.event))return;const V=k||h.target||e,Y=V[ne[h.type][H?ae:le]];if(Y){const G=[];if(1===Y.length){const g=Z(Y[0],V,h);g&&G.push(g)}else{const g=Y.slice();for(let z=0;z<g.length&&(!h||!0!==h[rt]);z++){const O=Z(g[z],V,h);O&&G.push(O)}}if(1===G.length)throw G[0];for(let g=0;g<G.length;g++){const z=G[g];r.nativeScheduleMicroTask(()=>{throw z})}}}const U=function(k){return x(this,k,!1)},K=function(k){return x(this,k,!0)};function J(k,h){if(!k)return!1;let H=!0;h&&void 0!==h.useG&&(H=h.useG);const V=h&&h.vh;let Y=!0;h&&void 0!==h.chkDup&&(Y=h.chkDup);let G=!1;h&&void 0!==h.rt&&(G=h.rt);let g=k;for(;g&&!g.hasOwnProperty(i);)g=Ie(g);if(!g&&k[i]&&(g=k),!g||g[y])return!1;const z=h&&h.eventNameToString,O={},R=g[y]=g[i],b=g[j(u)]=g[u],S=g[j(E)]=g[E],ee=g[j(T)]=g[T];let W;h&&h.prepend&&(W=g[j(h.prepend)]=g[h.prepend]);const q=H?function(s){if(!O.isExisting)return R.call(O.target,O.eventName,O.capture?K:U,O.options)}:function(s){return R.call(O.target,O.eventName,s.invoke,O.options)},A=H?function(s){if(!s.isRemoved){const l=ne[s.eventName];let v;l&&(v=l[s.capture?ae:le]);const C=v&&s.target[v];if(C)for(let p=0;p<C.length;p++)if(C[p]===s){C.splice(p,1),s.isRemoved=!0,s.removeAbortListener&&(s.removeAbortListener(),s.removeAbortListener=null),0===C.length&&(s.allRemoved=!0,s.target[v]=null);break}}if(s.allRemoved)return b.call(s.target,s.eventName,s.capture?K:U,s.options)}:function(s){return b.call(s.target,s.eventName,s.invoke,s.options)},he=h&&h.diff?h.diff:function(s,l){const v=typeof l;return"function"===v&&s.callback===l||"object"===v&&s.originalDelegate===l},de=Zone[j("UNPATCHED_EVENTS")],oe=e[j("PASSIVE_EVENTS")],a=function(s,l,v,C,p=!1,L=!1){return function(){const I=this||e;let M=arguments[0];h&&h.transferEventName&&(M=h.transferEventName(M));let B=arguments[1];if(!B)return s.apply(this,arguments);if(De&&"uncaughtException"===M)return s.apply(this,arguments);let F=!1;if("function"!=typeof B){if(!B.handleEvent)return s.apply(this,arguments);F=!0}if(V&&!V(s,B,I,arguments))return;const Ee=pe&&!!oe&&-1!==oe.indexOf(M),ie=function f(s){if("object"==typeof s&&null!==s){const l={...s};return s.signal&&(l.signal=s.signal),l}return s}(function N(s,l){return!pe&&"object"==typeof s&&s?!!s.capture:pe&&l?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?{...s,passive:!0}:s:{passive:!0}:s}(arguments[2],Ee)),me=ie?.signal;if(me?.aborted)return;if(de)for(let ce=0;ce<de.length;ce++)if(M===de[ce])return Ee?s.call(I,M,B,ie):s.apply(this,arguments);const Ue=!!ie&&("boolean"==typeof ie||ie.capture),lt=!(!ie||"object"!=typeof ie)&&ie.once,At=Zone.current;let ze=ne[M];ze||(ot(M,z),ze=ne[M]);const ut=ze[Ue?ae:le];let Ne,ke=I[ut],ft=!1;if(ke){if(ft=!0,Y)for(let ce=0;ce<ke.length;ce++)if(he(ke[ce],B))return}else ke=I[ut]=[];const ht=I.constructor.name,dt=tt[ht];dt&&(Ne=dt[M]),Ne||(Ne=ht+l+(z?z(M):M)),O.options=ie,lt&&(O.options.once=!1),O.target=I,O.capture=Ue,O.eventName=M,O.isExisting=ft;const Re=H?vt:void 0;Re&&(Re.taskData=O),me&&(O.options.signal=void 0);const se=At.scheduleEventTask(Ne,B,Re,v,C);if(me){O.options.signal=me;const ce=()=>se.zone.cancelTask(se);s.call(me,"abort",ce,{once:!0}),se.removeAbortListener=()=>me.removeEventListener("abort",ce)}return O.target=null,Re&&(Re.taskData=null),lt&&(O.options.once=!0),!pe&&"boolean"==typeof se.options||(se.options=ie),se.target=I,se.capture=Ue,se.eventName=M,F&&(se.originalDelegate=B),L?ke.unshift(se):ke.push(se),p?I:void 0}};return g[i]=a(R,D,q,A,G),W&&(g[d]=a(W,w,function(s){return W.call(O.target,O.eventName,s.invoke,O.options)},A,G,!0)),g[u]=function(){const s=this||e;let l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));const v=arguments[2],C=!!v&&("boolean"==typeof v||v.capture),p=arguments[1];if(!p)return b.apply(this,arguments);if(V&&!V(b,p,s,arguments))return;const L=ne[l];let I;L&&(I=L[C?ae:le]);const M=I&&s[I];if(M)for(let B=0;B<M.length;B++){const F=M[B];if(he(F,p))return M.splice(B,1),F.isRemoved=!0,0!==M.length||(F.allRemoved=!0,s[I]=null,C||"string"!=typeof l)||(s[Pe+"ON_PROPERTY"+l]=null),F.zone.cancelTask(F),G?s:void 0}return b.apply(this,arguments)},g[E]=function(){const s=this||e;let l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));const v=[],C=st(s,z?z(l):l);for(let p=0;p<C.length;p++){const L=C[p];v.push(L.originalDelegate?L.originalDelegate:L.callback)}return v},g[T]=function(){const s=this||e;let l=arguments[0];if(l){h&&h.transferEventName&&(l=h.transferEventName(l));const v=ne[l];if(v){const L=s[v[le]],I=s[v[ae]];if(L){const M=L.slice();for(let B=0;B<M.length;B++){const F=M[B];this[u].call(this,l,F.originalDelegate?F.originalDelegate:F.callback,F.options)}}if(I){const M=I.slice();for(let B=0;B<M.length;B++){const F=M[B];this[u].call(this,l,F.originalDelegate?F.originalDelegate:F.callback,F.options)}}}}else{const v=Object.keys(s);for(let C=0;C<v.length;C++){const L=nt.exec(v[C]);let I=L&&L[1];I&&"removeListener"!==I&&this[T].call(this,I)}this[T].call(this,"removeListener")}if(G)return this},fe(g[i],R),fe(g[u],b),ee&&fe(g[T],ee),S&&fe(g[E],S),!0}let X=[];for(let k=0;k<c.length;k++)X[k]=J(c[k],t);return X}function st(e,r){if(!r){const u=[];for(let E in e){const T=nt.exec(E);let y=T&&T[1];if(y&&(!r||y===r)){const D=e[E];if(D)for(let d=0;d<D.length;d++)u.push(D[d])}}return u}let c=ne[r];c||(ot(r),c=ne[r]);const t=e[c[le]],i=e[c[ae]];return t?i?t.concat(i):t.slice():i?i.slice():[]}function Pt(e,r){const c=e.Event;c&&c.prototype&&r.patchMethod(c.prototype,"stopImmediatePropagation",t=>function(i,u){i[rt]=!0,t&&t.apply(i,u)})}const Oe=j("zoneTask");function ye(e,r,c,t){let i=null,u=null;c+=t;const E={};function T(D){const d=D.data;d.args[0]=function(){return D.invoke.apply(this,arguments)};const w=i.apply(e,d.args);return et(w)?d.handleId=w:(d.handle=w,d.isRefreshable=Qe(w.refresh)),D}function y(D){const{handle:d,handleId:w}=D.data;return u.call(e,d??w)}i=ue(e,r+=t,D=>function(d,w){if(Qe(w[0])){const Z={isRefreshable:!1,isPeriodic:"Interval"===t,delay:"Timeout"===t||"Interval"===t?w[1]||0:void 0,args:w},x=w[0];w[0]=function(){try{return x.apply(this,arguments)}finally{const{handle:H,handleId:V,isPeriodic:Y,isRefreshable:G}=Z;!Y&&!G&&(V?delete E[V]:H&&(H[Oe]=null))}};const U=xe(r,w[0],Z,T,y);if(!U)return U;const{handleId:K,handle:J,isRefreshable:X,isPeriodic:k}=U.data;if(K)E[K]=U;else if(J&&(J[Oe]=U,X&&!k)){const h=J.refresh;J.refresh=function(){const{zone:H,state:V}=U;return"notScheduled"===V?(U._state="scheduled",H._updateTaskCount(U,1)):"running"===V&&(U._state="scheduling"),h.call(this)}}return J??K??U}return D.apply(e,w)}),u=ue(e,c,D=>function(d,w){const Z=w[0];let x;et(Z)?(x=E[Z],delete E[Z]):(x=Z?.[Oe],x?Z[Oe]=null:x=Z),x?.type?x.cancelFn&&x.zone.cancelTask(x):D.apply(e,w)})}function it(e,r,c){if(!c||0===c.length)return r;const t=c.filter(u=>u.target===e);if(!t||0===t.length)return r;const i=t[0].ignoreProperties;return r.filter(u=>-1===i.indexOf(u))}function ct(e,r,c,t){e&&Ke(e,it(e,r,c),t)}function Fe(e){return Object.getOwnPropertyNames(e).filter(r=>r.startsWith("on")&&r.length>2).map(r=>r.substring(2))}function It(e,r,c,t,i){const u=Zone.__symbol__(t);if(r[u])return;const E=r[u]=r[t];r[t]=function(T,y,D){return y&&y.prototype&&i.forEach(function(d){const w=`${c}.${t}::`+d,Z=y.prototype;try{if(Z.hasOwnProperty(d)){const x=e.ObjectGetOwnPropertyDescriptor(Z,d);x&&x.value?(x.value=e.wrapWithCurrentZone(x.value,w),e._redefineProperty(y.prototype,d,x)):Z[d]&&(Z[d]=e.wrapWithCurrentZone(Z[d],w))}else Z[d]&&(Z[d]=e.wrapWithCurrentZone(Z[d],w))}catch{}}),E.call(r,T,y,D)},e.attachOriginToPatched(r[t],E)}const at=function be(){const e=globalThis,r=!0===e[Q("forceDuplicateZoneCheck")];if(e.Zone&&(r||"function"!=typeof e.Zone.__symbol__))throw new Error("Zone already loaded.");return e.Zone??=function ve(){const e=te.performance;function r(N){e&&e.mark&&e.mark(N)}function c(N,_){e&&e.measure&&e.measure(N,_)}r("Zone");let t=(()=>{class N{static{this.__symbol__=Q}static assertZonePatched(){if(te.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let n=N.current;for(;n.parent;)n=n.parent;return n}static get current(){return b.zone}static get currentTask(){return S}static __load_patch(n,o,m=!1){if(O.hasOwnProperty(n)){const P=!0===te[Q("forceDuplicateZoneCheck")];if(!m&&P)throw Error("Already loaded patch: "+n)}else if(!te["__Zone_disable_"+n]){const P="Zone:"+n;r(P),O[n]=o(te,N,R),c(P,P)}}get parent(){return this._parent}get name(){return this._name}constructor(n,o){this._parent=n,this._name=o?o.name||"unnamed":"<root>",this._properties=o&&o.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,o)}get(n){const o=this.getZoneWith(n);if(o)return o._properties[n]}getZoneWith(n){let o=this;for(;o;){if(o._properties.hasOwnProperty(n))return o;o=o._parent}return null}fork(n){if(!n)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,n)}wrap(n,o){if("function"!=typeof n)throw new Error("Expecting function got: "+n);const m=this._zoneDelegate.intercept(this,n,o),P=this;return function(){return P.runGuarded(m,this,arguments,o)}}run(n,o,m,P){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,n,o,m,P)}finally{b=b.parent}}runGuarded(n,o=null,m,P){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,n,o,m,P)}catch(q){if(this._zoneDelegate.handleError(this,q))throw q}}finally{b=b.parent}}runTask(n,o,m){if(n.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(n.zone||J).name+"; Execution: "+this.name+")");const P=n,{type:q,data:{isPeriodic:A=!1,isRefreshable:_e=!1}={}}=n;if(n.state===X&&(q===z||q===g))return;const he=n.state!=H;he&&P._transitionTo(H,h);const de=S;S=P,b={parent:b,zone:this};try{q==g&&n.data&&!A&&!_e&&(n.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,P,o,m)}catch(oe){if(this._zoneDelegate.handleError(this,oe))throw oe}}finally{const oe=n.state;if(oe!==X&&oe!==Y)if(q==z||A||_e&&oe===k)he&&P._transitionTo(h,H,k);else{const f=P._zoneDelegates;this._updateTaskCount(P,-1),he&&P._transitionTo(X,H,X),_e&&(P._zoneDelegates=f)}b=b.parent,S=de}}scheduleTask(n){if(n.zone&&n.zone!==this){let m=this;for(;m;){if(m===n.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${n.zone.name}`);m=m.parent}}n._transitionTo(k,X);const o=[];n._zoneDelegates=o,n._zone=this;try{n=this._zoneDelegate.scheduleTask(this,n)}catch(m){throw n._transitionTo(Y,k,X),this._zoneDelegate.handleError(this,m),m}return n._zoneDelegates===o&&this._updateTaskCount(n,1),n.state==k&&n._transitionTo(h,k),n}scheduleMicroTask(n,o,m,P){return this.scheduleTask(new E(G,n,o,m,P,void 0))}scheduleMacroTask(n,o,m,P,q){return this.scheduleTask(new E(g,n,o,m,P,q))}scheduleEventTask(n,o,m,P,q){return this.scheduleTask(new E(z,n,o,m,P,q))}cancelTask(n){if(n.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(n.zone||J).name+"; Execution: "+this.name+")");if(n.state===h||n.state===H){n._transitionTo(V,h,H);try{this._zoneDelegate.cancelTask(this,n)}catch(o){throw n._transitionTo(Y,V),this._zoneDelegate.handleError(this,o),o}return this._updateTaskCount(n,-1),n._transitionTo(X,V),n.runCount=-1,n}}_updateTaskCount(n,o){const m=n._zoneDelegates;-1==o&&(n._zoneDelegates=null);for(let P=0;P<m.length;P++)m[P]._updateTaskCount(n.type,o)}}return N})();const i={name:"",onHasTask:(N,_,n,o)=>N.hasTask(n,o),onScheduleTask:(N,_,n,o)=>N.scheduleTask(n,o),onInvokeTask:(N,_,n,o,m,P)=>N.invokeTask(n,o,m,P),onCancelTask:(N,_,n,o)=>N.cancelTask(n,o)};class u{get zone(){return this._zone}constructor(_,n,o){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=_,this._parentDelegate=n,this._forkZS=o&&(o&&o.onFork?o:n._forkZS),this._forkDlgt=o&&(o.onFork?n:n._forkDlgt),this._forkCurrZone=o&&(o.onFork?this._zone:n._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:n._interceptZS),this._interceptDlgt=o&&(o.onIntercept?n:n._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this._zone:n._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:n._invokeZS),this._invokeDlgt=o&&(o.onInvoke?n:n._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this._zone:n._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:n._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?n:n._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this._zone:n._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:n._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?n:n._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this._zone:n._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:n._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?n:n._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this._zone:n._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:n._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?n:n._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this._zone:n._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const m=o&&o.onHasTask;(m||n&&n._hasTaskZS)&&(this._hasTaskZS=m?o:i,this._hasTaskDlgt=n,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,o.onScheduleTask||(this._scheduleTaskZS=i,this._scheduleTaskDlgt=n,this._scheduleTaskCurrZone=this._zone),o.onInvokeTask||(this._invokeTaskZS=i,this._invokeTaskDlgt=n,this._invokeTaskCurrZone=this._zone),o.onCancelTask||(this._cancelTaskZS=i,this._cancelTaskDlgt=n,this._cancelTaskCurrZone=this._zone))}fork(_,n){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,_,n):new t(_,n)}intercept(_,n,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,_,n,o):n}invoke(_,n,o,m,P){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,_,n,o,m,P):n.apply(o,m)}handleError(_,n){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,_,n)}scheduleTask(_,n){let o=n;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,_,n),o||(o=n);else if(n.scheduleFn)n.scheduleFn(n);else{if(n.type!=G)throw new Error("Task is missing scheduleFn.");U(n)}return o}invokeTask(_,n,o,m){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,_,n,o,m):n.callback.apply(o,m)}cancelTask(_,n){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,_,n);else{if(!n.cancelFn)throw Error("Task is not cancelable");o=n.cancelFn(n)}return o}hasTask(_,n){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,_,n)}catch(o){this.handleError(_,o)}}_updateTaskCount(_,n){const o=this._taskCounts,m=o[_],P=o[_]=m+n;if(P<0)throw new Error("More tasks executed then were scheduled.");0!=m&&0!=P||this.hasTask(this._zone,{microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:_})}}class E{constructor(_,n,o,m,P,q){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=_,this.source=n,this.data=m,this.scheduleFn=P,this.cancelFn=q,!o)throw new Error("callback is not defined");this.callback=o;const A=this;this.invoke=_===z&&m&&m.useG?E.invokeTask:function(){return E.invokeTask.call(te,A,this,arguments)}}static invokeTask(_,n,o){_||(_=this),ee++;try{return _.runCount++,_.zone.runTask(_,n,o)}finally{1==ee&&K(),ee--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(X,k)}_transitionTo(_,n,o){if(this._state!==n&&this._state!==o)throw new Error(`${this.type} '${this.source}': can not transition to '${_}', expecting state '${n}'${o?" or '"+o+"'":""}, was '${this._state}'.`);this._state=_,_==X&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const T=Q("setTimeout"),y=Q("Promise"),D=Q("then");let Z,d=[],w=!1;function x(N){if(Z||te[y]&&(Z=te[y].resolve(0)),Z){let _=Z[D];_||(_=Z.then),_.call(Z,N)}else te[T](N,0)}function U(N){0===ee&&0===d.length&&x(K),N&&d.push(N)}function K(){if(!w){for(w=!0;d.length;){const N=d;d=[];for(let _=0;_<N.length;_++){const n=N[_];try{n.zone.runTask(n,null,null)}catch(o){R.onUnhandledError(o)}}}R.microtaskDrainDone(),w=!1}}const J={name:"NO ZONE"},X="notScheduled",k="scheduling",h="scheduled",H="running",V="canceling",Y="unknown",G="microTask",g="macroTask",z="eventTask",O={},R={symbol:Q,currentZoneFrame:()=>b,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:U,showUncaughtError:()=>!t[Q("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:x};let b={parent:null,zone:new t(null,null)},S=null,ee=0;function W(){}return c("Zone","Zone"),t}(),e.Zone}();(function Zt(e){(function Nt(e){e.__load_patch("ZoneAwarePromise",(r,c,t)=>{const i=Object.getOwnPropertyDescriptor,u=Object.defineProperty,T=t.symbol,y=[],D=!1!==r[T("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],d=T("Promise"),w=T("then");t.onUnhandledError=f=>{if(t.showUncaughtError()){const a=f&&f.rejection;a?console.error("Unhandled Promise rejection:",a instanceof Error?a.message:a,"; Zone:",f.zone.name,"; Task:",f.task&&f.task.source,"; Value:",a,a instanceof Error?a.stack:void 0):console.error(f)}},t.microtaskDrainDone=()=>{for(;y.length;){const f=y.shift();try{f.zone.runGuarded(()=>{throw f.throwOriginal?f.rejection:f})}catch(a){U(a)}}};const x=T("unhandledPromiseRejectionHandler");function U(f){t.onUnhandledError(f);try{const a=c[x];"function"==typeof a&&a.call(this,f)}catch{}}function K(f){return f&&f.then}function J(f){return f}function X(f){return A.reject(f)}const k=T("state"),h=T("value"),H=T("finally"),V=T("parentPromiseValue"),Y=T("parentPromiseState"),g=null,z=!0,O=!1;function b(f,a){return s=>{try{N(f,a,s)}catch(l){N(f,!1,l)}}}const S=function(){let f=!1;return function(s){return function(){f||(f=!0,s.apply(null,arguments))}}},ee="Promise resolved with itself",W=T("currentTaskTrace");function N(f,a,s){const l=S();if(f===s)throw new TypeError(ee);if(f[k]===g){let v=null;try{("object"==typeof s||"function"==typeof s)&&(v=s&&s.then)}catch(C){return l(()=>{N(f,!1,C)})(),f}if(a!==O&&s instanceof A&&s.hasOwnProperty(k)&&s.hasOwnProperty(h)&&s[k]!==g)n(s),N(f,s[k],s[h]);else if(a!==O&&"function"==typeof v)try{v.call(s,l(b(f,a)),l(b(f,!1)))}catch(C){l(()=>{N(f,!1,C)})()}else{f[k]=a;const C=f[h];if(f[h]=s,f[H]===H&&a===z&&(f[k]=f[Y],f[h]=f[V]),a===O&&s instanceof Error){const p=c.currentTask&&c.currentTask.data&&c.currentTask.data.__creationTrace__;p&&u(s,W,{configurable:!0,enumerable:!1,writable:!0,value:p})}for(let p=0;p<C.length;)o(f,C[p++],C[p++],C[p++],C[p++]);if(0==C.length&&a==O){f[k]=0;let p=s;try{throw new Error("Uncaught (in promise): "+function E(f){return f&&f.toString===Object.prototype.toString?(f.constructor&&f.constructor.name||"")+": "+JSON.stringify(f):f?f.toString():Object.prototype.toString.call(f)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(L){p=L}D&&(p.throwOriginal=!0),p.rejection=s,p.promise=f,p.zone=c.current,p.task=c.currentTask,y.push(p),t.scheduleMicroTask()}}}return f}const _=T("rejectionHandledHandler");function n(f){if(0===f[k]){try{const a=c[_];a&&"function"==typeof a&&a.call(this,{rejection:f[h],promise:f})}catch{}f[k]=O;for(let a=0;a<y.length;a++)f===y[a].promise&&y.splice(a,1)}}function o(f,a,s,l,v){n(f);const C=f[k],p=C?"function"==typeof l?l:J:"function"==typeof v?v:X;a.scheduleMicroTask("Promise.then",()=>{try{const L=f[h],I=!!s&&H===s[H];I&&(s[V]=L,s[Y]=C);const M=a.run(p,void 0,I&&p!==X&&p!==J?[]:[L]);N(s,!0,M)}catch(L){N(s,!1,L)}},s)}const P=function(){},q=r.AggregateError;class A{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(a){return a instanceof A?a:N(new this(null),z,a)}static reject(a){return N(new this(null),O,a)}static withResolvers(){const a={};return a.promise=new A((s,l)=>{a.resolve=s,a.reject=l}),a}static any(a){if(!a||"function"!=typeof a[Symbol.iterator])return Promise.reject(new q([],"All promises were rejected"));const s=[];let l=0;try{for(let p of a)l++,s.push(A.resolve(p))}catch{return Promise.reject(new q([],"All promises were rejected"))}if(0===l)return Promise.reject(new q([],"All promises were rejected"));let v=!1;const C=[];return new A((p,L)=>{for(let I=0;I<s.length;I++)s[I].then(M=>{v||(v=!0,p(M))},M=>{C.push(M),l--,0===l&&(v=!0,L(new q(C,"All promises were rejected")))})})}static race(a){let s,l,v=new this((L,I)=>{s=L,l=I});function C(L){s(L)}function p(L){l(L)}for(let L of a)K(L)||(L=this.resolve(L)),L.then(C,p);return v}static all(a){return A.allWithCallback(a)}static allSettled(a){return(this&&this.prototype instanceof A?this:A).allWithCallback(a,{thenCallback:l=>({status:"fulfilled",value:l}),errorCallback:l=>({status:"rejected",reason:l})})}static allWithCallback(a,s){let l,v,C=new this((M,B)=>{l=M,v=B}),p=2,L=0;const I=[];for(let M of a){K(M)||(M=this.resolve(M));const B=L;try{M.then(F=>{I[B]=s?s.thenCallback(F):F,p--,0===p&&l(I)},F=>{s?(I[B]=s.errorCallback(F),p--,0===p&&l(I)):v(F)})}catch(F){v(F)}p++,L++}return p-=2,0===p&&l(I),C}constructor(a){const s=this;if(!(s instanceof A))throw new Error("Must be an instanceof Promise.");s[k]=g,s[h]=[];try{const l=S();a&&a(l(b(s,z)),l(b(s,O)))}catch(l){N(s,!1,l)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return A}then(a,s){let l=this.constructor?.[Symbol.species];(!l||"function"!=typeof l)&&(l=this.constructor||A);const v=new l(P),C=c.current;return this[k]==g?this[h].push(C,v,a,s):o(this,C,v,a,s),v}catch(a){return this.then(null,a)}finally(a){let s=this.constructor?.[Symbol.species];(!s||"function"!=typeof s)&&(s=A);const l=new s(P);l[H]=H;const v=c.current;return this[k]==g?this[h].push(v,l,a,a):o(this,v,l,a,a),l}}A.resolve=A.resolve,A.reject=A.reject,A.race=A.race,A.all=A.all;const _e=r[d]=r.Promise;r.Promise=A;const he=T("thenPatched");function de(f){const a=f.prototype,s=i(a,"then");if(s&&(!1===s.writable||!s.configurable))return;const l=a.then;a[w]=l,f.prototype.then=function(v,C){return new A((L,I)=>{l.call(this,L,I)}).then(v,C)},f[he]=!0}return t.patchThen=de,_e&&(de(_e),ue(r,"fetch",f=>function oe(f){return function(a,s){let l=f.apply(a,s);if(l instanceof A)return l;let v=l.constructor;return v[he]||de(v),l}}(f))),Promise[c.__symbol__("uncaughtPromiseErrors")]=y,A})})(e),function Lt(e){e.__load_patch("toString",r=>{const c=Function.prototype.toString,t=j("OriginalDelegate"),i=j("Promise"),u=j("Error"),E=function(){if("function"==typeof this){const d=this[t];if(d)return"function"==typeof d?c.call(d):Object.prototype.toString.call(d);if(this===Promise){const w=r[i];if(w)return c.call(w)}if(this===Error){const w=r[u];if(w)return c.call(w)}}return c.call(this)};E[t]=c,Function.prototype.toString=E;const T=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":T.call(this)}})}(e),function Mt(e){e.__load_patch("util",(r,c,t)=>{const i=Fe(r);t.patchOnProperties=Ke,t.patchMethod=ue,t.bindArguments=Ve,t.patchMacroTask=yt;const u=c.__symbol__("BLACK_LISTED_EVENTS"),E=c.__symbol__("UNPATCHED_EVENTS");r[E]&&(r[u]=r[E]),r[u]&&(c[u]=c[E]=r[u]),t.patchEventPrototype=Pt,t.patchEventTarget=bt,t.isIEOrEdge=kt,t.ObjectDefineProperty=Le,t.ObjectGetOwnPropertyDescriptor=Te,t.ObjectCreate=_t,t.ArraySlice=Et,t.patchClass=we,t.wrapWithCurrentZone=He,t.filterProperties=it,t.attachOriginToPatched=fe,t._redefineProperty=Object.defineProperty,t.patchCallbacks=It,t.getGlobalObjects=()=>({globalSources:tt,zoneSymbolEventNames:ne,eventNames:i,isBrowser:Ge,isMix:Xe,isNode:De,TRUE_STR:ae,FALSE_STR:le,ZONE_SYMBOL_PREFIX:Pe,ADD_EVENT_LISTENER_STR:Me,REMOVE_EVENT_LISTENER_STR:Ze})})}(e)})(at),function Ot(e){e.__load_patch("legacy",r=>{const c=r[e.__symbol__("legacyPatch")];c&&c()}),e.__load_patch("timers",r=>{const c="set",t="clear";ye(r,c,t,"Timeout"),ye(r,c,t,"Interval"),ye(r,c,t,"Immediate")}),e.__load_patch("requestAnimationFrame",r=>{ye(r,"request","cancel","AnimationFrame"),ye(r,"mozRequest","mozCancel","AnimationFrame"),ye(r,"webkitRequest","webkitCancel","AnimationFrame")}),e.__load_patch("blocking",(r,c)=>{const t=["alert","prompt","confirm"];for(let i=0;i<t.length;i++)ue(r,t[i],(E,T,y)=>function(D,d){return c.current.run(E,r,d,y)})}),e.__load_patch("EventTarget",(r,c,t)=>{(function Dt(e,r){r.patchEventPrototype(e,r)})(r,t),function Ct(e,r){if(Zone[r.symbol("patchEventTarget")])return;const{eventNames:c,zoneSymbolEventNames:t,TRUE_STR:i,FALSE_STR:u,ZONE_SYMBOL_PREFIX:E}=r.getGlobalObjects();for(let y=0;y<c.length;y++){const D=c[y],Z=E+(D+u),x=E+(D+i);t[D]={},t[D][u]=Z,t[D][i]=x}const T=e.EventTarget;T&&T.prototype&&r.patchEventTarget(e,r,[T&&T.prototype])}(r,t);const i=r.XMLHttpRequestEventTarget;i&&i.prototype&&t.patchEventTarget(r,t,[i.prototype])}),e.__load_patch("MutationObserver",(r,c,t)=>{we("MutationObserver"),we("WebKitMutationObserver")}),e.__load_patch("IntersectionObserver",(r,c,t)=>{we("IntersectionObserver")}),e.__load_patch("FileReader",(r,c,t)=>{we("FileReader")}),e.__load_patch("on_property",(r,c,t)=>{!function St(e,r){if(De&&!Xe||Zone[e.symbol("patchEvents")])return;const c=r.__Zone_ignore_on_properties;let t=[];if(Ge){const i=window;t=t.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const u=function mt(){try{const e=ge.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch{}return!1}()?[{target:i,ignoreProperties:["error"]}]:[];ct(i,Fe(i),c&&c.concat(u),Ie(i))}t=t.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let i=0;i<t.length;i++){const u=r[t[i]];u&&u.prototype&&ct(u.prototype,Fe(u.prototype),c)}}(t,r)}),e.__load_patch("customElements",(r,c,t)=>{!function Rt(e,r){const{isBrowser:c,isMix:t}=r.getGlobalObjects();(c||t)&&e.customElements&&"customElements"in e&&r.patchCallbacks(r,e.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"])}(r,t)}),e.__load_patch("XHR",(r,c)=>{!function D(d){const w=d.XMLHttpRequest;if(!w)return;const Z=w.prototype;let U=Z[Ae],K=Z[je];if(!U){const R=d.XMLHttpRequestEventTarget;if(R){const b=R.prototype;U=b[Ae],K=b[je]}}const J="readystatechange",X="scheduled";function k(R){const b=R.data,S=b.target;S[E]=!1,S[y]=!1;const ee=S[u];U||(U=S[Ae],K=S[je]),ee&&K.call(S,J,ee);const W=S[u]=()=>{if(S.readyState===S.DONE)if(!b.aborted&&S[E]&&R.state===X){const _=S[c.__symbol__("loadfalse")];if(0!==S.status&&_&&_.length>0){const n=R.invoke;R.invoke=function(){const o=S[c.__symbol__("loadfalse")];for(let m=0;m<o.length;m++)o[m]===R&&o.splice(m,1);!b.aborted&&R.state===X&&n.call(R)},_.push(R)}else R.invoke()}else!b.aborted&&!1===S[E]&&(S[y]=!0)};return U.call(S,J,W),S[t]||(S[t]=R),z.apply(S,b.args),S[E]=!0,R}function h(){}function H(R){const b=R.data;return b.aborted=!0,O.apply(b.target,b.args)}const V=ue(Z,"open",()=>function(R,b){return R[i]=0==b[2],R[T]=b[1],V.apply(R,b)}),G=j("fetchTaskAborting"),g=j("fetchTaskScheduling"),z=ue(Z,"send",()=>function(R,b){if(!0===c.current[g]||R[i])return z.apply(R,b);{const S={target:R,url:R[T],isPeriodic:!1,args:b,aborted:!1},ee=xe("XMLHttpRequest.send",h,S,k,H);R&&!0===R[y]&&!S.aborted&&ee.state===X&&ee.invoke()}}),O=ue(Z,"abort",()=>function(R,b){const S=function x(R){return R[t]}(R);if(S&&"string"==typeof S.type){if(null==S.cancelFn||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(!0===c.current[G])return O.apply(R,b)})}(r);const t=j("xhrTask"),i=j("xhrSync"),u=j("xhrListener"),E=j("xhrScheduled"),T=j("xhrURL"),y=j("xhrErrorBeforeScheduled")}),e.__load_patch("geolocation",r=>{r.navigator&&r.navigator.geolocation&&function gt(e,r){const c=e.constructor.name;for(let t=0;t<r.length;t++){const i=r[t],u=e[i];if(u){if(!We(Te(e,i)))continue;e[i]=(T=>{const y=function(){return T.apply(this,Ve(arguments,c+"."+i))};return fe(y,T),y})(u)}}}(r.navigator.geolocation,["getCurrentPosition","watchPosition"])}),e.__load_patch("PromiseRejectionEvent",(r,c)=>{function t(i){return function(u){st(r,i).forEach(T=>{const y=r.PromiseRejectionEvent;if(y){const D=new y(i,{promise:u.promise,reason:u.rejection});T.invoke(D)}})}}r.PromiseRejectionEvent&&(c[j("unhandledPromiseRejectionHandler")]=t("unhandledrejection"),c[j("rejectionHandledHandler")]=t("rejectionhandled"))}),e.__load_patch("queueMicrotask",(r,c,t)=>{!function wt(e,r){r.patchMethod(e,"queueMicrotask",c=>function(t,i){Zone.current.scheduleMicroTask("queueMicrotask",i[0])})}(r,t)})}(at)}},te=>{te(te.s=50)}]);

"use strict";(self.webpackChunkcoverage_app=self.webpackChunkcoverage_app||[]).push([[792],{663:()=>{function Zs(e,n){return Object.is(e,n)}let Pe=null,Cr=!1,Lc=1;const tt=Symbol("SIGNAL");function J(e){const n=Pe;return Pe=e,n}const io={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Qs(e){if(Cr)throw new Error("");if(null===Pe)return;Pe.consumerOnSignalRead(e);const n=Pe.nextProducerIndex++;ea(Pe),n<Pe.producerNode.length&&Pe.producerNode[n]!==e&&br(Pe)&&Js(Pe.producerNode[n],Pe.producerIndexOfThis[n]),Pe.producerNode[n]!==e&&(Pe.producerNode[n]=e,Pe.producerIndexOfThis[n]=br(Pe)?Hp(e,Pe,n):0),Pe.producerLastReadVersion[n]=e.version}function Dr(e){if((!br(e)||e.dirty)&&(e.dirty||e.lastCleanEpoch!==Lc)){if(!e.producerMustRecompute(e)&&!Ks(e))return void Ys(e);e.producerRecomputeValue(e),Ys(e)}}function Pp(e){if(void 0===e.liveConsumerNode)return;const n=Cr;Cr=!0;try{for(const t of e.liveConsumerNode)t.dirty||RM(t)}finally{Cr=n}}function Vp(){return!1!==Pe?.consumerAllowSignalWrites}function RM(e){e.dirty=!0,Pp(e),e.consumerMarkedDirty?.(e)}function Ys(e){e.dirty=!1,e.lastCleanEpoch=Lc}function oo(e){return e&&(e.nextProducerIndex=0),J(e)}function wr(e,n){if(J(n),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(br(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)Js(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Ks(e){ea(e);for(let n=0;n<e.producerNode.length;n++){const t=e.producerNode[n],i=e.producerLastReadVersion[n];if(i!==t.version||(Dr(t),i!==t.version))return!0}return!1}function Xs(e){if(ea(e),br(e))for(let n=0;n<e.producerNode.length;n++)Js(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Hp(e,n,t){if(Bp(e),0===e.liveConsumerNode.length&&jp(e))for(let i=0;i<e.producerNode.length;i++)e.producerIndexOfThis[i]=Hp(e.producerNode[i],e,i);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function Js(e,n){if(Bp(e),1===e.liveConsumerNode.length&&jp(e))for(let i=0;i<e.producerNode.length;i++)Js(e.producerNode[i],e.producerIndexOfThis[i]);const t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){const i=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];ea(o),o.producerIndexOfThis[i]=n}}function br(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function ea(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Bp(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function jp(e){return void 0!==e.producerNode}const _i=Symbol("UNSET"),ro=Symbol("COMPUTING"),Zn=Symbol("ERRORED"),PM={...io,value:_i,dirty:!0,error:null,equal:Zs,kind:"computed",producerMustRecompute:e=>e.value===_i||e.value===ro,producerRecomputeValue(e){if(e.value===ro)throw new Error("Detected cycle in computations.");const n=e.value;e.value=ro;const t=oo(e);let i,o=!1;try{i=e.computation(),J(null),o=n!==_i&&n!==Zn&&i!==Zn&&e.equal(n,i)}catch(r){i=Zn,e.error=r}finally{wr(e,t)}o?e.value=n:(e.value=i,e.version++)}};let Up=function VM(){throw new Error};function $p(e){Up(e)}function Vc(e,n){Vp()||$p(e),e.equal(e.value,n)||(e.value=n,function jM(e){e.version++,function FM(){Lc++}(),Pp(e)}(e))}const Gp={...io,equal:Zs,value:void 0,kind:"signal"};let Hc;function na(){return Hc}function Qn(e){const n=Hc;return Hc=e,n}const Bc=Symbol("NotFound");function Be(e){return"function"==typeof e}function Wp(e){const t=e(i=>{Error.call(i),i.stack=(new Error).stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}Error;const jc=Wp(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:\n${t.map((i,o)=>`${o+1}) ${i.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=t});function ia(e,n){if(e){const t=e.indexOf(n);0<=t&&e.splice(t,1)}}class Tt{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;const{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(const r of t)r.remove(this);else t.remove(this);const{initialTeardown:i}=this;if(Be(i))try{i()}catch(r){n=r instanceof jc?r.errors:[r]}const{_finalizers:o}=this;if(o){this._finalizers=null;for(const r of o)try{Yp(r)}catch(s){n=n??[],s instanceof jc?n=[...n,...s.errors]:n.push(s)}}if(n)throw new jc(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Yp(n);else{if(n instanceof Tt){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=null!==(t=this._finalizers)&&void 0!==t?t:[]).push(n)}}_hasParent(n){const{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){const{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){const{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&ia(t,n)}remove(n){const{_finalizers:t}=this;t&&ia(t,n),n instanceof Tt&&n._removeParent(this)}}Tt.EMPTY=(()=>{const e=new Tt;return e.closed=!0,e})();const Zp=Tt.EMPTY;function Qp(e){return e instanceof Tt||e&&"closed"in e&&Be(e.remove)&&Be(e.add)&&Be(e.unsubscribe)}function Yp(e){Be(e)?e():e.unsubscribe()}const yi={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},oa={setTimeout(e,n,...t){const{delegate:i}=oa;return i?.setTimeout?i.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){const{delegate:n}=oa;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Kp(e){oa.setTimeout(()=>{const{onUnhandledError:n}=yi;if(!n)throw e;n(e)})}function Xp(){}const WM=Uc("C",void 0,void 0);function Uc(e,n,t){return{kind:e,value:n,error:t}}let Ci=null;function ra(e){if(yi.useDeprecatedSynchronousErrorHandling){const n=!Ci;if(n&&(Ci={errorThrown:!1,error:null}),e(),n){const{errorThrown:t,error:i}=Ci;if(Ci=null,t)throw i}}else e()}class $c extends Tt{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,Qp(n)&&n.add(this)):this.destination=eI}static create(n,t,i){return new Gc(n,t,i)}next(n){this.isStopped?qc(function QM(e){return Uc("N",e,void 0)}(n),this):this._next(n)}error(n){this.isStopped?qc(function ZM(e){return Uc("E",void 0,e)}(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?qc(WM,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const KM=Function.prototype.bind;function zc(e,n){return KM.call(e,n)}class XM{constructor(n){this.partialObserver=n}next(n){const{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(i){sa(i)}}error(n){const{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(i){sa(i)}else sa(n)}complete(){const{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){sa(t)}}}class Gc extends $c{constructor(n,t,i){let o;if(super(),Be(n)||!n)o={next:n??void 0,error:t??void 0,complete:i??void 0};else{let r;this&&yi.useDeprecatedNextContext?(r=Object.create(n),r.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&zc(n.next,r),error:n.error&&zc(n.error,r),complete:n.complete&&zc(n.complete,r)}):o=n}this.destination=new XM(o)}}function sa(e){yi.useDeprecatedSynchronousErrorHandling?function YM(e){yi.useDeprecatedSynchronousErrorHandling&&Ci&&(Ci.errorThrown=!0,Ci.error=e)}(e):Kp(e)}function qc(e,n){const{onStoppedNotification:t}=yi;t&&oa.setTimeout(()=>t(e,n))}const eI={closed:!0,next:Xp,error:function JM(e){throw e},complete:Xp},Wc="function"==typeof Symbol&&Symbol.observable||"@@observable";function Zc(e){return e}let St=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){const i=new e;return i.source=this,i.operator=t,i}subscribe(t,i,o){const r=function nI(e){return e&&e instanceof $c||function tI(e){return e&&Be(e.next)&&Be(e.error)&&Be(e.complete)}(e)&&Qp(e)}(t)?t:new Gc(t,i,o);return ra(()=>{const{operator:s,source:a}=this;r.add(s?s.call(r,a):a?this._subscribe(r):this._trySubscribe(r))}),r}_trySubscribe(t){try{return this._subscribe(t)}catch(i){t.error(i)}}forEach(t,i){return new(i=eg(i))((o,r)=>{const s=new Gc({next:a=>{try{t(a)}catch(l){r(l),s.unsubscribe()}},error:r,complete:o});this.subscribe(s)})}_subscribe(t){var i;return null===(i=this.source)||void 0===i?void 0:i.subscribe(t)}[Wc](){return this}pipe(...t){return function Jp(e){return 0===e.length?Zc:1===e.length?e[0]:function(t){return e.reduce((i,o)=>o(i),t)}}(t)(this)}toPromise(t){return new(t=eg(t))((i,o)=>{let r;this.subscribe(s=>r=s,s=>o(s),()=>i(r))})}}return e.create=n=>new e(n),e})();function eg(e){var n;return null!==(n=e??yi.Promise)&&void 0!==n?n:Promise}const iI=Wp(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let un=(()=>{class e extends St{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){const i=new tg(this,this);return i.operator=t,i}_throwIfClosed(){if(this.closed)throw new iI}next(t){ra(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const i of this.currentObservers)i.next(t)}})}error(t){ra(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;const{observers:i}=this;for(;i.length;)i.shift().error(t)}})}complete(){ra(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return(null===(t=this.observers)||void 0===t?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){const{hasError:i,isStopped:o,observers:r}=this;return i||o?Zp:(this.currentObservers=null,r.push(t),new Tt(()=>{this.currentObservers=null,ia(r,t)}))}_checkFinalizedStatuses(t){const{hasError:i,thrownError:o,isStopped:r}=this;i?t.error(o):r&&t.complete()}asObservable(){const t=new St;return t.source=this,t}}return e.create=(n,t)=>new tg(n,t),e})();class tg extends un{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,i;null===(i=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===i||i.call(t,n)}error(n){var t,i;null===(i=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===i||i.call(t,n)}complete(){var n,t;null===(t=null===(n=this.destination)||void 0===n?void 0:n.complete)||void 0===t||t.call(n)}_subscribe(n){var t,i;return null!==(i=null===(t=this.source)||void 0===t?void 0:t.subscribe(n))&&void 0!==i?i:Zp}}class oI extends un{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){const t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){const{hasError:n,thrownError:t,_value:i}=this;if(n)throw t;return this._throwIfClosed(),i}next(n){super.next(this._value=n)}}function Di(e){return n=>{if(function rI(e){return Be(e?.lift)}(n))return n.lift(function(t){try{return e(t,this)}catch(i){this.error(i)}});throw new TypeError("Unable to lift unknown Observable type")}}function Yn(e,n,t,i,o){return new sI(e,n,t,i,o)}class sI extends $c{constructor(n,t,i,o,r,s){super(n),this.onFinalize=r,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(l){n.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){n.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=i?function(){try{i()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:t}=this;super.unsubscribe(),!t&&(null===(n=this.onFinalize)||void 0===n||n.call(this))}}}function Qc(e,n){return Di((t,i)=>{let o=0;t.subscribe(Yn(i,r=>{i.next(e.call(n,r,o++))}))})}const ng="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss";class x extends Error{code;constructor(n,t){super(function Mn(e,n){return`${function aI(e){return`NG0${Math.abs(e)}`}(e)}${n?": "+n:""}`}(n,t)),this.code=n}}function In(e){return{toString:e}.toString()}const ao="__parameters__";function co(e,n,t){return In(()=>{const i=function Yc(e){return function(...t){if(e){const i=e(...t);for(const o in i)this[o]=i[o]}}}(n);function o(...r){if(this instanceof o)return i.apply(this,r),this;const s=new o(...r);return a.annotation=s,a;function a(l,c,u){const d=l.hasOwnProperty(ao)?l[ao]:Object.defineProperty(l,ao,{value:[]})[ao];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}const Oe=globalThis;function pe(e){for(let n in e)if(e[n]===pe)return n;throw Error("Could not find renamed property on target object.")}function lI(e,n){for(const t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function ze(e){if("string"==typeof e)return e;if(Array.isArray(e))return`[${e.map(ze).join(", ")}]`;if(null==e)return""+e;const n=e.overriddenName||e.name;if(n)return`${n}`;const t=e.toString();if(null==t)return""+t;const i=t.indexOf("\n");return i>=0?t.slice(0,i):t}function Kc(e,n){return e?n?`${e} ${n}`:e:n||""}const cI=pe({__forward_ref__:pe});function ye(e){return e.__forward_ref__=ye,e.toString=function(){return ze(this())},e}function q(e){return la(e)?e():e}function la(e){return"function"==typeof e&&e.hasOwnProperty(cI)&&e.__forward_ref__===ye}function te(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Tn(e){return{providers:e.providers||[],imports:e.imports||[]}}function ca(e){return sg(e,da)||sg(e,ag)}function sg(e,n){return e.hasOwnProperty(n)?e[n]:null}function ua(e){return e&&(e.hasOwnProperty(Xc)||e.hasOwnProperty(pI))?e[Xc]:null}const da=pe({\u0275prov:pe}),Xc=pe({\u0275inj:pe}),ag=pe({ngInjectableDef:pe}),pI=pe({ngInjectorDef:pe});class R{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,"number"==typeof t?this.__NG_ELEMENT_ID__=t:void 0!==t&&(this.\u0275prov=te({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}function tu(e){return e&&!!e.\u0275providers}const uo=pe({\u0275cmp:pe}),nu=pe({\u0275dir:pe}),iu=pe({\u0275pipe:pe}),cg=pe({\u0275mod:pe}),Sn=pe({\u0275fac:pe}),Er=pe({__NG_ELEMENT_ID__:pe}),ug=pe({__NG_ENV_ID__:pe});function Q(e){return"string"==typeof e?e:null==e?"":String(e)}function dg(e,n){throw new x(-200,e)}function ou(e,n){throw new x(-201,!1)}var ie=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(ie||{});let ru;function fg(){return ru}function _t(e){const n=ru;return ru=e,n}function hg(e,n,t){const i=ca(e);return i&&"root"==i.providedIn?void 0===i.value?i.value=i.factory():i.value:t&ie.Optional?null:void 0!==n?n:void ou()}const bi={},su="__NG_DI_FLAG__";class pg{injector;constructor(n){this.injector=n}retrieve(n,t){return this.injector.get(n,t.optional?Bc:bi,t)}}const pa="ngTempTokenPath",yI=/\n/gm,gg="__source";function DI(e,n=ie.Default){if(void 0===na())throw new x(-203,!1);if(null===na())return hg(e,void 0,n);{const t=na();let i;return i=t instanceof pg?t.injector:t,i.get(e,n&ie.Optional?null:void 0,n)}}function re(e,n=ie.Default){return(fg()||DI)(q(e),n)}function k(e,n=ie.Default){return re(e,ga(n))}function ga(e){return typeof e>"u"||"number"==typeof e?e:(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function au(e){const n=[];for(let t=0;t<e.length;t++){const i=q(e[t]);if(Array.isArray(i)){if(0===i.length)throw new x(900,!1);let o,r=ie.Default;for(let s=0;s<i.length;s++){const a=i[s],l=wI(a);"number"==typeof l?-1===l?o=a.token:r|=l:o=a}n.push(re(o,r))}else n.push(re(i))}return n}function Mr(e,n){return e[su]=n,e.prototype[su]=n,e}function wI(e){return e[su]}const lu=Mr(co("Optional"),8),cu=Mr(co("SkipSelf"),4);function Ei(e,n){return e.hasOwnProperty(Sn)?e[Sn]:null}function fo(e,n){e.forEach(t=>Array.isArray(t)?fo(t,n):n(t))}function vg(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function ma(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function Nt(e,n,t){let i=Ir(e,n);return i>=0?e[1|i]=t:(i=~i,function yg(e,n,t,i){let o=e.length;if(o==n)e.push(t,i);else if(1===o)e.push(i,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;)e[o]=e[o-2],o--;e[n]=t,e[n+1]=i}}(e,i,n,t)),i}function uu(e,n){const t=Ir(e,n);if(t>=0)return e[1|t]}function Ir(e,n){return function xI(e,n,t){let i=0,o=e.length>>t;for(;o!==i;){const r=i+(o-i>>1),s=e[r<<t];if(n===s)return r<<t;s>n?o=r:i=r+1}return~(o<<t)}(e,n,1)}const Zt={},le=[],Qt=new R(""),Cg=new R("",-1),du=new R("");class _a{get(n,t=bi){if(t===bi){const i=new Error(`NullInjectorError: No provider for ${ze(n)}!`);throw i.name="NullInjectorError",i}return t}}function ne(e){return e[uo]||null}function At(e){return e[iu]||null}function NI(...e){return{\u0275providers:fu(0,e),\u0275fromNgModule:!0}}function fu(e,...n){const t=[],i=new Set;let o;const r=s=>{t.push(s)};return fo(n,s=>{const a=s;ya(a,r,[],i)&&(o||=[],o.push(a))}),void 0!==o&&Dg(o,r),t}function Dg(e,n){for(let t=0;t<e.length;t++){const{ngModule:i,providers:o}=e[t];hu(o,r=>{n(r,i)})}}function ya(e,n,t,i){if(!(e=q(e)))return!1;let o=null,r=ua(e);const s=!r&&ne(e);if(r||s){if(s&&!s.standalone)return!1;o=e}else{const l=e.ngModule;if(r=ua(l),!r)return!1;o=l}const a=i.has(o);if(s){if(a)return!1;if(i.add(o),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)ya(c,n,t,i)}}else{if(!r)return!1;{if(null!=r.imports&&!a){let c;i.add(o);try{fo(r.imports,u=>{ya(u,n,t,i)&&(c||=[],c.push(u))})}finally{}void 0!==c&&Dg(c,n)}if(!a){const c=Ei(o)||(()=>new o);n({provide:o,useFactory:c,deps:le},o),n({provide:du,useValue:o,multi:!0},o),n({provide:Qt,useValue:()=>re(o),multi:!0},o)}const l=r.providers;if(null!=l&&!a){const c=e;hu(l,u=>{n(u,c)})}}}return o!==e&&void 0!==e.providers}function hu(e,n){for(let t of e)tu(t)&&(t=t.\u0275providers),Array.isArray(t)?hu(t,n):n(t)}const OI=pe({provide:String,useValue:pe});function pu(e){return null!==e&&"object"==typeof e&&OI in e}function Ii(e){return"function"==typeof e}const gu=new R(""),Ca={},Eg={};let mu;function Da(){return void 0===mu&&(mu=new _a),mu}class Yt{}class Ti extends Yt{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,i,o){super(),this.parent=t,this.source=i,this.scopes=o,_u(n,s=>this.processProvider(s)),this.records.set(Cg,po(void 0,this)),o.has("environment")&&this.records.set(Yt,po(void 0,this));const r=this.records.get(gu);null!=r&&"string"==typeof r.value&&this.scopes.add(r.value),this.injectorDefTypes=new Set(this.get(du,le,ie.Self))}retrieve(n,t){return this.get(n,t.optional?Bc:bi,t)}destroy(){Tr(this),this._destroyed=!0;const n=J(null);try{for(const i of this._ngOnDestroyHooks)i.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const i of t)i()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),J(n)}}onDestroy(n){return Tr(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){Tr(this);const t=Qn(this),i=_t(void 0);try{return n()}finally{Qn(t),_t(i)}}get(n,t=bi,i=ie.Default){if(Tr(this),n.hasOwnProperty(ug))return n[ug](this);i=ga(i);const r=Qn(this),s=_t(void 0);try{if(!(i&ie.SkipSelf)){let l=this.records.get(n);if(void 0===l){const c=function PI(e){return"function"==typeof e||"object"==typeof e&&e instanceof R}(n)&&ca(n);l=c&&this.injectableDefInScope(c)?po(vu(n),Ca):null,this.records.set(n,l)}if(null!=l)return this.hydrate(n,l)}return(i&ie.Self?Da():this.parent).get(n,t=i&ie.Optional&&t===bi?null:t)}catch(a){if("NullInjectorError"===a.name){if((a[pa]=a[pa]||[]).unshift(ze(n)),r)throw a;return function bI(e,n,t,i){const o=e[pa];throw n[gg]&&o.unshift(n[gg]),e.message=function EI(e,n,t,i=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=ze(n);if(Array.isArray(n))o=n.map(ze).join(" -> ");else if("object"==typeof n){let r=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];r.push(s+":"+("string"==typeof a?JSON.stringify(a):ze(a)))}o=`{${r.join(", ")}}`}return`${t}${i?"("+i+")":""}[${o}]: ${e.replace(yI,"\n  ")}`}("\n"+e.message,o,t,i),e.ngTokenPath=o,e[pa]=null,e}(a,n,"R3InjectorError",this.source)}throw a}finally{_t(s),Qn(r)}}resolveInjectorInitializers(){const n=J(null),t=Qn(this),i=_t(void 0);try{const r=this.get(Qt,le,ie.Self);for(const s of r)s()}finally{Qn(t),_t(i),J(n)}}toString(){const n=[],t=this.records;for(const i of t.keys())n.push(ze(i));return`R3Injector[${n.join(", ")}]`}processProvider(n){let t=Ii(n=q(n))?n:q(n&&n.provide);const i=function FI(e){return pu(e)?po(void 0,e.useValue):po(Mg(e),Ca)}(n);if(!Ii(n)&&!0===n.multi){let o=this.records.get(t);o||(o=po(void 0,Ca,!0),o.factory=()=>au(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,i)}hydrate(n,t){const i=J(null);try{return t.value===Eg?dg(ze(n)):t.value===Ca&&(t.value=Eg,t.value=t.factory()),"object"==typeof t.value&&t.value&&function LI(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{J(i)}}injectableDefInScope(n){if(!n.providedIn)return!1;const t=q(n.providedIn);return"string"==typeof t?"any"===t||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){const t=this._onDestroyHooks.indexOf(n);-1!==t&&this._onDestroyHooks.splice(t,1)}}function vu(e){const n=ca(e),t=null!==n?n.factory:Ei(e);if(null!==t)return t;if(e instanceof R)throw new x(204,!1);if(e instanceof Function)return function kI(e){if(e.length>0)throw new x(204,!1);const t=function hI(e){return e&&(e[da]||e[ag])||null}(e);return null!==t?()=>t.factory(e):()=>new e}(e);throw new x(204,!1)}function Mg(e,n,t){let i;if(Ii(e)){const o=q(e);return Ei(o)||vu(o)}if(pu(e))i=()=>q(e.useValue);else if(function bg(e){return!(!e||!e.useFactory)}(e))i=()=>e.useFactory(...au(e.deps||[]));else if(function wg(e){return!(!e||!e.useExisting)}(e))i=()=>re(q(e.useExisting));else{const o=q(e&&(e.useClass||e.provide));if(!function RI(e){return!!e.deps}(e))return Ei(o)||vu(o);i=()=>new o(...au(e.deps))}return i}function Tr(e){if(e.destroyed)throw new x(205,!1)}function po(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function _u(e,n){for(const t of e)Array.isArray(t)?_u(t,n):t&&tu(t)?_u(t.\u0275providers,n):n(t)}function Ig(e,n){let t;e instanceof Ti?(Tr(e),t=e):t=new pg(e);const o=Qn(t),r=_t(void 0);try{return n()}finally{Qn(o),_t(r)}}const U=11,A=26;function Ae(e){return Array.isArray(e)&&"object"==typeof e[1]}function qe(e){return Array.isArray(e)&&!0===e[1]}function Du(e){return!!(4&e.flags)}function Xt(e){return e.componentOffset>-1}function Ia(e){return!(1&~e.flags)}function Ft(e){return!!e.template}function Nn(e){return!!(512&e[2])}function On(e){return!(256&~e[2])}class QI{previousValue;currentValue;firstChange;constructor(n,t,i){this.previousValue=n,this.currentValue=t,this.firstChange=i}isFirstChange(){return this.firstChange}}function Rg(e,n,t,i){null!==n?n.applyValueToInputSignal(n,i):e[t]=i}const An=(()=>{const e=()=>Lg;return e.ngInherit=!0,e})();function Lg(e){return e.type.prototype.ngOnChanges&&(e.setInput=KI),YI}function YI(){const e=Vg(this),n=e?.current;if(n){const t=e.previous;if(t===Zt)e.previous=n;else for(let i in n)t[i]=n[i];e.current=null,this.ngOnChanges(n)}}function KI(e,n,t,i,o){const r=this.declaredInputs[i],s=Vg(e)||function XI(e,n){return e[Pg]=n}(e,{previous:Zt,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[r];a[r]=new QI(c&&c.currentValue,t,l===Zt),Rg(e,n,o,t)}const Pg="__ngSimpleChanges__";function Vg(e){return e[Pg]||null}function se(e){for(;Array.isArray(e);)e=e[0];return e}function wo(e,n){return se(n[e])}function at(e,n){return se(n[e.index])}function Oi(e,n){return e.data[n]}function lt(e,n){const t=n[e];return Ae(t)?t:t[0]}function Eu(e){return!(128&~e[2])}function Bt(e,n){return null==n?null:e[n]}function jg(e){e[17]=0}function Mu(e){1024&e[2]||(e[2]|=1024,Eu(e)&&bo(e))}function Nr(e){return!!(9216&e[2]||e[24]?.dirty)}function Iu(e){e[10].changeDetectionScheduler?.notify(8),64&e[2]&&(e[2]|=1024),Nr(e)&&bo(e)}function bo(e){e[10].changeDetectionScheduler?.notify(0);let n=kn(e);for(;null!==n&&!(8192&n[2])&&(n[2]|=8192,Eu(n));)n=kn(n)}function Sa(e,n){if(On(e))throw new x(911,!1);null===e[21]&&(e[21]=[]),e[21].push(n)}function kn(e){const n=e[3];return qe(n)?n[3]:n}function Su(e){return e[7]??=[]}function xu(e){return e.cleanup??=[]}const W={lFrame:Kg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};let Ou=!1;function Au(){return W.bindingsEnabled}function D(){return W.lFrame.lView}function G(){return W.lFrame.tView}function H(e){return W.lFrame.contextLView=e,e[8]}function B(e){return W.lFrame.contextLView=null,e}function ee(){let e=$g();for(;null!==e&&64===e.type;)e=e.parent;return e}function $g(){return W.lFrame.currentTNode}function Jt(e,n){const t=W.lFrame;t.currentTNode=e,t.isParent=n}function ku(){return W.lFrame.isParent}function Fu(){W.lFrame.isParent=!1}function qg(){return Ou}function Na(e){const n=Ou;return Ou=e,n}function ct(){const e=W.lFrame;let n=e.bindingRootIndex;return-1===n&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function jt(){return W.lFrame.bindingIndex++}function Rn(e){const n=W.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function p0(e,n){const t=W.lFrame;t.bindingIndex=t.bindingRootIndex=e,Ru(n)}function Ru(e){W.lFrame.currentDirectiveIndex=e}function Pu(){return W.lFrame.currentQueryIndex}function Oa(e){W.lFrame.currentQueryIndex=e}function m0(e){const n=e[1];return 2===n.type?n.declTNode:1===n.type?e[5]:null}function Qg(e,n,t){if(t&ie.SkipSelf){let o=n,r=e;for(;!(o=o.parent,null!==o||t&ie.Host||(o=m0(r),null===o||(r=r[14],10&o.type))););if(null===o)return!1;n=o,e=r}const i=W.lFrame=Yg();return i.currentTNode=n,i.lView=e,!0}function Vu(e){const n=Yg(),t=e[1];W.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function Yg(){const e=W.lFrame,n=null===e?null:e.child;return null===n?Kg(e):n}function Kg(e){const n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=n),n}function Xg(){const e=W.lFrame;return W.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Jg=Xg;function Hu(){const e=Xg();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Je(){return W.lFrame.selectedIndex}function Fi(e){W.lFrame.selectedIndex=e}function ve(){const e=W.lFrame;return Oi(e.tView,e.selectedIndex)}let nm=!0;function Ar(){return nm}function hn(e){nm=e}function Bu(e,n){for(let t=n.directiveStart,i=n.directiveEnd;t<i;t++){const r=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=r;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),l&&(e.viewHooks??=[]).push(-t,l),c&&((e.viewHooks??=[]).push(t,c),(e.viewCheckHooks??=[]).push(t,c)),null!=u&&(e.destroyHooks??=[]).push(t,u)}}function Aa(e,n,t){im(e,n,3,t)}function ka(e,n,t,i){(3&e[2])===t&&im(e,n,t,i)}function ju(e,n){let t=e[2];(3&t)===n&&(t&=16383,t+=1,e[2]=t)}function im(e,n,t,i){const r=i??-1,s=n.length-1;let a=0;for(let l=void 0!==i?65535&e[17]:0;l<s;l++)if("number"==typeof n[l+1]){if(a=n[l],null!=i&&a>=i)break}else n[l]<0&&(e[17]+=65536),(a<r||-1==r)&&(w0(e,t,n,l),e[17]=(**********&e[17])+l+2),l++}function om(e,n){const t=J(null);try{n.call(e)}finally{J(t)}}function w0(e,n,t,i){const o=t[i]<0,r=t[i+1],a=e[o?-t[i]:t[i]];o?e[2]>>14<e[17]>>16&&(3&e[2])===n&&(e[2]+=16384,om(a,r)):om(a,r)}class kr{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,i){this.factory=n,this.canSeeViewProviders=t,this.injectImpl=i}}function rm(e){return 3===e||4===e||6===e}function sm(e){return 64===e.charCodeAt(0)}function Mo(e,n){if(null!==n&&0!==n.length)if(null===e||0===e.length)e=n.slice();else{let t=-1;for(let i=0;i<n.length;i++){const o=n[i];"number"==typeof o?t=o:0===t||am(e,t,o,0,-1===t||2===t?n[++i]:null)}}return e}function am(e,n,t,i,o){let r=0,s=e.length;if(-1===n)s=-1;else for(;r<e.length;){const a=e[r++];if("number"==typeof a){if(a===n){s=-1;break}if(a>n){s=r-1;break}}}for(;r<e.length;){const a=e[r];if("number"==typeof a)break;if(a===t)return void(null!==o&&(e[r+1]=o));r++,null!==o&&r++}-1!==s&&(e.splice(s,0,n),r=s+1),e.splice(r++,0,t),null!==o&&e.splice(r++,0,o)}function zu(e){return-1!==e}function Fr(e){return 32767&e}function Rr(e,n){let t=function T0(e){return e>>16}(e),i=n;for(;t>0;)i=i[14],t--;return i}let Gu=!0;function Fa(e){const n=Gu;return Gu=e,n}let S0=0;const pn={};function Ra(e,n){const t=um(e,n);if(-1!==t)return t;const i=n[1];i.firstCreatePass&&(e.injectorIndex=n.length,qu(i.data,e),qu(n,null),qu(i.blueprint,null));const o=La(e,n),r=e.injectorIndex;if(zu(o)){const s=Fr(o),a=Rr(o,n),l=a[1].data;for(let c=0;c<8;c++)n[r+c]=a[s+c]|l[s+c]}return n[r+8]=o,r}function qu(e,n){e.push(0,0,0,0,0,0,0,0,n)}function um(e,n){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===n[e.injectorIndex+8]?-1:e.injectorIndex}function La(e,n){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let t=0,i=null,o=n;for(;null!==o;){if(i=vm(o),null===i)return-1;if(t++,o=o[14],-1!==i.injectorIndex)return i.injectorIndex|t<<16}return-1}function Wu(e,n,t){!function x0(e,n,t){let i;"string"==typeof t?i=t.charCodeAt(0)||0:t.hasOwnProperty(Er)&&(i=t[Er]),null==i&&(i=t[Er]=S0++);const o=255&i;n.data[e+(o>>5)]|=1<<o}(e,n,t)}function dm(e,n,t){if(t&ie.Optional||void 0!==e)return e;ou()}function fm(e,n,t,i){if(t&ie.Optional&&void 0===i&&(i=null),!(t&(ie.Self|ie.Host))){const o=e[9],r=_t(void 0);try{return o?o.get(n,i,t&ie.Optional):hg(n,i,t&ie.Optional)}finally{_t(r)}}return dm(i,0,t)}function hm(e,n,t,i=ie.Default,o){if(null!==e){if(2048&n[2]&&!(i&ie.Self)){const s=function F0(e,n,t,i,o){let r=e,s=n;for(;null!==r&&null!==s&&2048&s[2]&&!Nn(s);){const a=pm(r,s,t,i|ie.Self,pn);if(a!==pn)return a;let l=r.parent;if(!l){const c=s[20];if(c){const u=c.get(t,pn,i);if(u!==pn)return u}l=vm(s),s=s[14]}r=l}return o}(e,n,t,i,pn);if(s!==pn)return s}const r=pm(e,n,t,i,pn);if(r!==pn)return r}return fm(n,t,i,o)}function pm(e,n,t,i,o){const r=function A0(e){if("string"==typeof e)return e.charCodeAt(0)||0;const n=e.hasOwnProperty(Er)?e[Er]:void 0;return"number"==typeof n?n>=0?255&n:k0:n}(t);if("function"==typeof r){if(!Qg(n,e,i))return i&ie.Host?dm(o,0,i):fm(n,t,i,o);try{let s;if(s=r(i),null!=s||i&ie.Optional)return s;ou()}finally{Jg()}}else if("number"==typeof r){let s=null,a=um(e,n),l=-1,c=i&ie.Host?n[15][5]:null;for((-1===a||i&ie.SkipSelf)&&(l=-1===a?La(e,n):n[a+8],-1!==l&&mm(i,!1)?(s=n[1],a=Fr(l),n=Rr(l,n)):a=-1);-1!==a;){const u=n[1];if(gm(r,a,u.data)){const d=O0(a,n,t,s,i,c);if(d!==pn)return d}l=n[a+8],-1!==l&&mm(i,n[1].data[a+8]===c)&&gm(r,a,n)?(s=u,a=Fr(l),n=Rr(l,n)):a=-1}}return o}function O0(e,n,t,i,o,r){const s=n[1],a=s.data[e+8],u=Pa(a,s,t,null==i?Xt(a)&&Gu:i!=s&&!!(3&a.type),o&ie.Host&&r===a);return null!==u?Lr(n,s,u,a):pn}function Pa(e,n,t,i,o){const r=e.providerIndexes,s=n.data,a=1048575&r,l=e.directiveStart,u=r>>20,p=o?a+u:e.directiveEnd;for(let h=i?a:a+u;h<p;h++){const m=s[h];if(h<l&&t===m||h>=l&&m.type===t)return h}if(o){const h=s[l];if(h&&Ft(h)&&h.type===t)return l}return null}function Lr(e,n,t,i){let o=e[t];const r=n.data;if(o instanceof kr){const s=o;s.resolving&&dg(function ce(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():Q(e)}(r[t]));const a=Fa(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?_t(s.injectImpl):null;Qg(e,i,ie.Default);try{o=e[t]=s.factory(void 0,r,e,i),n.firstCreatePass&&t>=i.directiveStart&&function D0(e,n,t){const{ngOnChanges:i,ngOnInit:o,ngDoCheck:r}=n.type.prototype;if(i){const s=Lg(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),r&&((t.preOrderHooks??=[]).push(e,r),(t.preOrderCheckHooks??=[]).push(e,r))}(t,r[t],n)}finally{null!==c&&_t(c),Fa(a),s.resolving=!1,Jg()}}return o}function gm(e,n,t){return!!(t[n+(e>>5)]&1<<e)}function mm(e,n){return!(e&ie.Self||e&ie.Host&&n)}class ke{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,i){return hm(this._tNode,this._lView,n,ga(i),t)}}function k0(){return new ke(ee(),D())}function it(e){return In(()=>{const n=e.prototype.constructor,t=n[Sn]||Zu(n),i=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==i;){const r=o[Sn]||Zu(o);if(r&&r!==t)return r;o=Object.getPrototypeOf(o)}return r=>new r})}function Zu(e){return la(e)?()=>{const n=Zu(q(e));return n&&n()}:Ei(e)}function vm(e){const n=e[1],t=n.type;return 2===t?n.declTNode:1===t?e[5]:null}function wm(e,n=null,t=null,i){const o=bm(e,n,t,i);return o.resolveInjectorInitializers(),o}function bm(e,n=null,t=null,i,o=new Set){const r=[t||le,NI(e)];return i=i||("object"==typeof e?void 0:ze(e)),new Ti(r,n||Da(),i||null,o)}class et{static THROW_IF_NOT_FOUND=bi;static NULL=new _a;static create(n,t){if(Array.isArray(n))return wm({name:""},t,n,"");{const i=n.name??"";return wm({name:i},n.parent,n.providers,i)}}static \u0275prov=te({token:et,providedIn:"any",factory:()=>re(Cg)});static __NG_ELEMENT_ID__=-1}new R("").__NG_ELEMENT_ID__=e=>{const n=ee();if(null===n)throw new x(204,!1);if(2&n.type)return n.value;if(e&ie.Optional)return null;throw new x(204,!1)};const Em=!1;let Jn=(()=>class e{static __NG_ELEMENT_ID__=$0;static __NG_ENV_ID__=t=>t})();class Mm extends Jn{_lView;constructor(n){super(),this._lView=n}onDestroy(n){const t=this._lView;return On(t)?(n(),()=>{}):(Sa(t,n),()=>function Tu(e,n){if(null===e[21])return;const t=e[21].indexOf(n);-1!==t&&e[21].splice(t,1)}(t,n))}}function $0(){return new Mm(D())}class Ln{}const Pr=new R("",{providedIn:"root",factory:()=>!1}),Im=new R(""),Yu=new R("");let Li=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new oI(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);const t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),0===this.pendingTasks.size&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=te({token:e,providedIn:"root",factory:()=>new e})}return e})();const De=class G0 extends un{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,function Tg(){return void 0!==fg()||null!=na()}()&&(this.destroyRef=k(Jn,{optional:!0})??void 0,this.pendingTasks=k(Li,{optional:!0})??void 0)}emit(n){const t=J(null);try{super.next(n)}finally{J(t)}}subscribe(n,t,i){let o=n,r=t||(()=>null),s=i;if(n&&"object"==typeof n){const l=n;o=l.next?.bind(l),r=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(r=this.wrapInTimeout(r),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));const a=super.subscribe({next:o,error:r,complete:s});return n instanceof Tt&&n.add(a),a}wrapInTimeout(n){return t=>{const i=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{void 0!==i&&this.pendingTasks?.remove(i)}})}}};function Vr(...e){}function Tm(e){let n,t;function i(){e=Vr;try{void 0!==t&&"function"==typeof cancelAnimationFrame&&cancelAnimationFrame(t),void 0!==n&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),i()}),"function"==typeof requestAnimationFrame&&(t=requestAnimationFrame(()=>{e(),i()})),()=>i()}function Sm(e){return queueMicrotask(()=>e()),()=>{e=Vr}}const Ku="isAngularZone",Ba=Ku+"_ID";let q0=0;class he{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new De(!1);onMicrotaskEmpty=new De(!1);onStable=new De(!1);onError=new De(!1);constructor(n){const{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:i=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:r=Em}=n;if(typeof Zone>"u")throw new x(908,!1);Zone.assertZonePatched();const s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&i,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=r,function Q0(e){const n=()=>{!function Z0(e){function n(){Tm(()=>{e.callbackScheduled=!1,Ju(e),e.isCheckStableRunning=!0,Xu(e),e.isCheckStableRunning=!1})}e.isCheckStableRunning||e.callbackScheduled||(e.callbackScheduled=!0,e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),Ju(e))}(e)},t=q0++;e._inner=e._inner.fork({name:"angular",properties:{[Ku]:!0,[Ba]:t,[Ba+t]:!0},onInvokeTask:(i,o,r,s,a,l)=>{if(function Y0(e){return Om(e,"__ignore_ng_zone__")}(l))return i.invokeTask(r,s,a,l);try{return xm(e),i.invokeTask(r,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===s.type||e.shouldCoalesceRunChangeDetection)&&n(),Nm(e)}},onInvoke:(i,o,r,s,a,l,c)=>{try{return xm(e),i.invoke(r,s,a,l,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!function K0(e){return Om(e,"__scheduler_tick__")}(l)&&n(),Nm(e)}},onHasTask:(i,o,r,s)=>{i.hasTask(r,s),o===r&&("microTask"==s.change?(e._hasPendingMicrotasks=s.microTask,Ju(e),Xu(e)):"macroTask"==s.change&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(i,o,r,s)=>(i.handleError(r,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}(s)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get(Ku)}static assertInAngularZone(){if(!he.isInAngularZone())throw new x(909,!1)}static assertNotInAngularZone(){if(he.isInAngularZone())throw new x(909,!1)}run(n,t,i){return this._inner.run(n,t,i)}runTask(n,t,i,o){const r=this._inner,s=r.scheduleEventTask("NgZoneEvent: "+o,n,W0,Vr,Vr);try{return r.runTask(s,t,i)}finally{r.cancelTask(s)}}runGuarded(n,t,i){return this._inner.runGuarded(n,t,i)}runOutsideAngular(n){return this._outer.run(n)}}const W0={};function Xu(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ju(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&!0===e.callbackScheduled)}function xm(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Nm(e){e._nesting--,Xu(e)}class ed{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new De;onMicrotaskEmpty=new De;onStable=new De;onError=new De;run(n,t,i){return n.apply(t,i)}runGuarded(n,t,i){return n.apply(t,i)}runOutsideAngular(n){return n()}runTask(n,t,i,o){return n.apply(t,i)}}function Om(e,n){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0]?.data?.[n]}class en{_console=console;handleError(n){this._console.error("ERROR",n)}}const J0=new R("",{providedIn:"root",factory:()=>{const e=k(he),n=k(en);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function eT(){return Io(ee(),D())}function Io(e,n){return new ut(at(e,n))}let ut=(()=>class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=eT})();function km(e){return e instanceof ut?e.nativeElement:e}function gn(e,n){const t=function BM(e,n){const t=Object.create(Gp);t.value=e,void 0!==n&&(t.equal=n);const i=()=>(Qs(t),t.value);return i[tt]=t,i}(e,n?.equal),i=t[tt];return t.set=o=>Vc(i,o),t.update=o=>function zp(e,n){Vp()||$p(e),Vc(e,n(e.value))}(i,o),t.asReadonly=ja.bind(t),t}function ja(){const e=this[tt];if(void 0===e.readonlyFn){const n=()=>this();n[tt]=e,e.readonlyFn=n}return e.readonlyFn}function Rm(e){return function Fm(e){return"function"==typeof e&&void 0!==e[tt]}(e)&&"function"==typeof e.set}function tT(){return this._results[Symbol.iterator]()}class nT{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new un}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;const i=function yt(e){return e.flat(Number.POSITIVE_INFINITY)}(n);(this._changesDetected=!function SI(e,n,t){if(e.length!==n.length)return!1;for(let i=0;i<e.length;i++){let o=e[i],r=n[i];if(t&&(o=t(o),r=t(r)),r!==o)return!1}return!0}(this._results,i,t))&&(this._results=i,this.length=i.length,this.last=i[this.length-1],this.first=i[0])}notifyOnChanges(){void 0!==this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){void 0!==this._changes&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=tT}function Br(e){return!(128&~e.flags)}var Ua=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ua||{});const $a=new Map;let oT=0;function nd(e){$a.delete(e[19])}const od="__ngContext__";function Dt(e,n){Ae(n)?(e[od]=n[19],function sT(e){$a.set(e[19],e)}(n)):e[od]=n}function zm(e){return qm(e[12])}function Gm(e){return qm(e[4])}function qm(e){for(;null!==e&&!qe(e);)e=e[4];return e}let sd;function mn(){if(void 0!==sd)return sd;if(typeof document<"u")return document;throw new x(210,!1)}const Pn=new R("",{providedIn:"root",factory:()=>MT}),MT="ng",ad=new R(""),ld=new R("",{providedIn:"platform",factory:()=>"unknown"}),Jm=new R("",{providedIn:"root",factory:()=>mn().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null}),iv=new R("",{providedIn:"root",factory:()=>!1});var yd=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(yd||{});const So=new R(""),av=new Set;let Cd=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=te({token:e,providedIn:"root",factory:()=>new e})}return e})();function Pv(e,n){const t=e.contentQueries;if(null!==t){const i=J(null);try{for(let o=0;o<t.length;o+=2){const s=t[o+1];if(-1!==s){const a=e.data[s];Oa(t[o]),a.contentQueries(2,n[s],s)}}}finally{J(i)}}}function kd(e,n,t){Oa(0);const i=J(null);try{n(e,t)}finally{J(i)}}function Fd(e,n,t){if(Du(n)){const i=J(null);try{const r=n.directiveEnd;for(let s=n.directiveStart;s<r;s++){const a=e.data[s];a.contentQueries&&a.contentQueries(1,t[s],s)}}finally{J(i)}}}var Ut=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ut||{});let ol,rl;function Po(e){return function Vv(){if(void 0===ol&&(ol=null,Oe.trustedTypes))try{ol=Oe.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ol}()?.createHTML(e)||e}function Hv(e){return function Rd(){if(void 0===rl&&(rl=null,Oe.trustedTypes))try{rl=Oe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return rl}()?.createHTML(e)||e}class Uv{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${ng})`}}function ti(e){return e instanceof Uv?e.changingThisBreaksApplicationSecurity:e}function Jr(e,n){const t=function _1(e){return e instanceof Uv&&e.getTypeName()||null}(e);if(null!=t&&t!==n){if("ResourceURL"===t&&"URL"===n)return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${ng})`)}return t===n}class y1{inertDocumentHelper;constructor(n){this.inertDocumentHelper=n}getInertBodyElement(n){n="<body><remove></remove>"+n;try{const t=(new window.DOMParser).parseFromString(Po(n),"text/html").body;return null===t?this.inertDocumentHelper.getInertBodyElement(n):(t.firstChild?.remove(),t)}catch{return null}}}class C1{defaultDoc;inertDocument;constructor(n){this.defaultDoc=n,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(n){const t=this.inertDocument.createElement("template");return t.innerHTML=Po(n),t}}const w1=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Ld(e){return(e=String(e)).match(w1)?e:"unsafe:"+e}function Bn(e){const n={};for(const t of e.split(","))n[t]=!0;return n}function es(...e){const n={};for(const t of e)for(const i in t)t.hasOwnProperty(i)&&(n[i]=!0);return n}const zv=Bn("area,br,col,hr,img,wbr"),Gv=Bn("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),qv=Bn("rp,rt"),Pd=es(zv,es(Gv,Bn("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),es(qv,Bn("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),es(qv,Gv)),Vd=Bn("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Wv=es(Vd,Bn("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Bn("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext")),b1=Bn("script,style,template");class E1{sanitizedSomething=!1;buf=[];sanitizeChildren(n){let t=n.firstChild,i=!0,o=[];for(;t;)if(t.nodeType===Node.ELEMENT_NODE?i=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,i&&t.firstChild)o.push(t),t=T1(t);else for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);let r=I1(t);if(r){t=r;break}t=o.pop()}return this.buf.join("")}startElement(n){const t=Zv(n).toLowerCase();if(!Pd.hasOwnProperty(t))return this.sanitizedSomething=!0,!b1.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);const i=n.attributes;for(let o=0;o<i.length;o++){const r=i.item(o),s=r.name,a=s.toLowerCase();if(!Wv.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=r.value;Vd[a]&&(l=Ld(l)),this.buf.push(" ",s,'="',Yv(l),'"')}return this.buf.push(">"),!0}endElement(n){const t=Zv(n).toLowerCase();Pd.hasOwnProperty(t)&&!zv.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))}chars(n){this.buf.push(Yv(n))}}function I1(e){const n=e.nextSibling;if(n&&e!==n.previousSibling)throw Qv(n);return n}function T1(e){const n=e.firstChild;if(n&&function M1(e,n){return(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}(e,n))throw Qv(n);return n}function Zv(e){const n=e.nodeName;return"string"==typeof n?n:"FORM"}function Qv(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}const S1=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,x1=/([^\#-~ |!])/g;function Yv(e){return e.replace(/&/g,"&amp;").replace(S1,function(n){return"&#"+(1024*(n.charCodeAt(0)-55296)+(n.charCodeAt(1)-56320)+65536)+";"}).replace(x1,function(n){return"&#"+n.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}let sl;function Hd(e){return"content"in e&&function O1(e){return e.nodeType===Node.ELEMENT_NODE&&"TEMPLATE"===e.nodeName}(e)?e.content:null}var Vo=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Vo||{});function Kv(e){const n=ts();return n?Hv(n.sanitize(Vo.HTML,e)||""):Jr(e,"HTML")?Hv(ti(e)):function N1(e,n){let t=null;try{sl=sl||function $v(e){const n=new C1(e);return function D1(){try{return!!(new window.DOMParser).parseFromString(Po(""),"text/html")}catch{return!1}}()?new y1(n):n}(e);let i=n?String(n):"";t=sl.getInertBodyElement(i);let o=5,r=i;do{if(0===o)throw new Error("Failed to sanitize html because the input is unstable");o--,i=r,r=t.innerHTML,t=sl.getInertBodyElement(i)}while(i!==r);return Po((new E1).sanitizeChildren(Hd(t)||t))}finally{if(t){const i=Hd(t)||t;for(;i.firstChild;)i.firstChild.remove()}}}(mn(),Q(e))}function ni(e){const n=ts();return n?n.sanitize(Vo.URL,e)||"":Jr(e,"URL")?ti(e):Ld(Q(e))}function ts(){const e=D();return e&&e[10].sanitizer}const V1=/^>|^->|<!--|-->|--!>|<!-$/g,H1=/(<|>)/g;function cl(e){return e.ownerDocument.defaultView}function Y1(e,n,t){let i=e.length;for(;;){const o=e.indexOf(n,t);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const r=n.length;if(o+r===i||e.charCodeAt(o+r)<=32)return o}t=o+1}}const a_="ng-template";function K1(e,n,t,i){let o=0;if(i){for(;o<n.length&&"string"==typeof n[o];o+=2)if("class"===n[o]&&-1!==Y1(n[o+1].toLowerCase(),t,0))return!0}else if(Ud(e))return!1;if(o=n.indexOf(1,o),o>-1){let r;for(;++o<n.length&&"string"==typeof(r=n[o]);)if(r.toLowerCase()===t)return!0}return!1}function Ud(e){return 4===e.type&&e.value!==a_}function X1(e,n,t){return n===(4!==e.type||t?e.value:a_)}function J1(e,n,t){let i=4;const o=e.attrs,r=null!==o?function nS(e){for(let n=0;n<e.length;n++)if(rm(e[n]))return n;return e.length}(o):0;let s=!1;for(let a=0;a<n.length;a++){const l=n[a];if("number"!=typeof l){if(!s)if(4&i){if(i=2|1&i,""!==l&&!X1(e,l,t)||""===l&&1===n.length){if(tn(i))return!1;s=!0}}else if(8&i){if(null===o||!K1(e,o,l,t)){if(tn(i))return!1;s=!0}}else{const c=n[++a],u=eS(l,o,Ud(e),t);if(-1===u){if(tn(i))return!1;s=!0;continue}if(""!==c){let d;if(d=u>r?"":o[u+1].toLowerCase(),2&i&&c!==d){if(tn(i))return!1;s=!0}}}}else{if(!s&&!tn(i)&&!tn(l))return!1;if(s&&tn(l))continue;s=!1,i=l|1&i}}return tn(i)||s}function tn(e){return!(1&e)}function eS(e,n,t,i){if(null===n)return-1;let o=0;if(i||!t){let r=!1;for(;o<n.length;){const s=n[o];if(s===e)return o;if(3===s||6===s)r=!0;else{if(1===s||2===s){let a=n[++o];for(;"string"==typeof a;)a=n[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=r?1:2}return-1}return function iS(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){const i=e[t];if("number"==typeof i)return-1;if(i===n)return t;t++}return-1}(n,e)}function l_(e,n,t=!1){for(let i=0;i<n.length;i++)if(J1(e,n[i],t))return!0;return!1}function c_(e,n){return e?":not("+n.trim()+")":n}function rS(e){let n=e[0],t=1,i=2,o="",r=!1;for(;t<e.length;){let s=e[t];if("string"==typeof s)if(2&i){const a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&i?o+="."+s:4&i&&(o+=" "+s);else""!==o&&!tn(s)&&(n+=c_(r,o),o=""),i=s,r=r||!tn(i);t++}return""!==o&&(n+=c_(r,o)),n}const Y={};function zd(e,n){return e.createComment(function Jv(e){return e.replace(V1,n=>n.replace(H1,"\u200b$1\u200b"))}(n))}function ul(e,n,t){return e.createElement(n,t)}function Hi(e,n,t,i,o){e.insertBefore(n,t,i,o)}function d_(e,n,t){e.appendChild(n,t)}function f_(e,n,t,i,o){null!==i?Hi(e,n,t,i,o):d_(e,n,t)}function p_(e,n,t){const{mergedAttrs:i,classes:o,styles:r}=t;null!==i&&function I0(e,n,t){let i=0;for(;i<t.length;){const o=t[i];if("number"==typeof o){if(0!==o)break;i++;const r=t[i++],s=t[i++],a=t[i++];e.setAttribute(n,s,a,r)}else{const r=o,s=t[++i];sm(r)?e.setProperty(n,r,s):e.setAttribute(n,r,s),i++}}}(e,n,i),null!==o&&function cS(e,n,t){""===t?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}(e,n,o),null!==r&&function lS(e,n,t){e.setAttribute(n,"style",t)}(e,n,r)}function Gd(e,n,t,i,o,r,s,a,l,c,u){const d=A+i,p=d+o,h=function uS(e,n){const t=[];for(let i=0;i<n;i++)t.push(i<e?null:Y);return t}(d,p),m="function"==typeof c?c():c;return h[1]={type:e,blueprint:h,template:t,queries:null,viewQuery:a,declTNode:n,data:h.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof r?r():r,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:m,incompleteFirstPass:!1,ssrId:u}}function dl(e,n,t,i,o,r,s,a,l,c,u){const d=n.blueprint.slice();return d[0]=o,d[2]=1228|i,(null!==c||e&&2048&e[2])&&(d[2]|=2048),jg(d),d[3]=d[14]=e,d[8]=t,d[10]=s||e&&e[10],d[U]=a||e&&e[U],d[9]=l||e&&e[9]||null,d[5]=r,d[19]=function rT(){return oT++}(),d[6]=u,d[20]=c,d[15]=2==n.type?e[15]:d,d}function qd(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function is(e,n,t,i){if(0===t)return-1;const o=n.length;for(let r=0;r<t;r++)n.push(i),e.blueprint.push(i),e.data.push(null);return o}function Wd(e,n){return e[12]?e[13][4]=n:e[12]=n,e[13]=n,n}function f(e=1){m_(G(),D(),Je()+e,!1)}function m_(e,n,t,i){if(!i)if(3&~n[2]){const r=e.preOrderHooks;null!==r&&ka(n,r,0,t)}else{const r=e.preOrderCheckHooks;null!==r&&Aa(n,r,t)}Fi(t)}var fl=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(fl||{});function Zd(e,n,t,i){const o=J(null);try{const[r,s,a]=e.inputs[t];let l=null;!!(s&fl.SignalBased)&&(l=n[r][tt]),null!==l&&void 0!==l.transformFn?i=l.transformFn(i):null!==a&&(i=a.call(n,i)),null!==e.setInput?e.setInput(n,l,i,t,r):Rg(n,l,r,i)}finally{J(o)}}function v_(e,n,t,i,o){const r=Je(),s=2&i;try{Fi(-1),s&&n.length>A&&m_(e,n,A,!1),t(i,o)}finally{Fi(r)}}function hl(e,n,t){(function _S(e,n,t){const i=t.directiveStart,o=t.directiveEnd;Xt(t)&&function dS(e,n,t){const i=at(n,e),o=function g_(e){const n=e.tView;return null===n||n.incompleteFirstPass?e.tView=Gd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}(t),r=e[10].rendererFactory,s=Wd(e,dl(e,o,null,qd(t),i,n,null,r.createRenderer(i,t),null,null,null));e[n.index]=s}(n,t,e.data[i+t.componentOffset]),e.firstCreatePass||Ra(t,n);const r=t.initialInputs;for(let s=i;s<o;s++){const a=e.data[s],l=Lr(n,e,s,t);Dt(l,n),null!==r&&DS(0,s-i,l,a,0,r),Ft(a)&&(lt(t.index,n)[8]=Lr(n,e,s,t))}})(e,n,t),!(64&~t.flags)&&function yS(e,n,t){const i=t.directiveStart,o=t.directiveEnd,r=t.index,s=function g0(){return W.lFrame.currentDirectiveIndex}();try{Fi(r);for(let a=i;a<o;a++){const l=e.data[a],c=n[a];Ru(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&CS(l,c)}}finally{Fi(-1),Ru(s)}}(e,n,t)}function Qd(e,n,t=at){const i=n.localNames;if(null!==i){let o=n.index+1;for(let r=0;r<i.length;r+=2){const s=i[r+1],a=-1===s?t(n,e):e[s];e[o++]=a}}}let __=()=>null;function wt(e,n,t,i,o,r,s,a){if(a||!Jd(n,e,t,i,o)){if(3&n.type){const l=at(n,t);i=function mS(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(i),o=null!=s?s(o,n.value||"",i):o,r.setProperty(l,i,o)}}else Xt(n)&&function vS(e,n){const t=lt(n,e);16&t[2]||(t[2]|=64)}(t,n.index)}function CS(e,n){null!==e.hostBindings&&e.hostBindings(1,n)}function Yd(e,n){const t=e.directiveRegistry;let i=null;if(t)for(let o=0;o<t.length;o++){const r=t[o];l_(n,r.selectors,!1)&&(i??=[],Ft(r)?i.unshift(r):i.push(r))}return i}function _n(e,n,t,i,o,r){const s=at(e,n);!function Kd(e,n,t,i,o,r,s){if(null==r)e.removeAttribute(n,o,t);else{const a=null==s?Q(r):s(r,i||"",o);e.setAttribute(n,o,a,t)}}(n[U],s,r,e.value,t,i,o)}function DS(e,n,t,i,o,r){const s=r[n];if(null!==s)for(let a=0;a<s.length;a+=2)Zd(i,t,s[a],s[a+1])}function Jd(e,n,t,i,o){const r=e.inputs?.[i],s=e.hostDirectiveInputs?.[i];let a=!1;if(s)for(let l=0;l<s.length;l+=2){const c=s[l];Zd(n.data[c],t[c],s[l+1],o),a=!0}if(r)for(const l of r)Zd(n.data[l],t[l],i,o),a=!0;return a}function wS(e,n){const t=lt(n,e),i=t[1];!function bS(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}(i,t);const o=t[0];null!==o&&null===t[6]&&(t[6]=null),pl(i,t,t[8])}function pl(e,n,t){Vu(n);try{const i=e.viewQuery;null!==i&&kd(1,i,t);const o=e.template;null!==o&&v_(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[18]?.finishViewCreation(e),e.staticContentQueries&&Pv(e,n),e.staticViewQueries&&kd(2,e.viewQuery,t);const r=e.components;null!==r&&function ES(e,n){for(let t=0;t<n.length;t++)wS(e,n[t])}(n,r)}catch(i){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),i}finally{n[2]&=-5,Hu()}}function Bi(e,n){return!n||null===n.firstChild||Br(e)}function tf(e,n){return undefined(e,n)}var ii=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(ii||{});function oi(e){return!(32&~e.flags)}function jo(e,n,t,i,o){if(null!=i){let r,s=!1;qe(i)?r=i:Ae(i)&&(s=!0,i=i[0]);const a=se(i);0===e&&null!==t?null==o?d_(n,t,a):Hi(n,t,a,o||null,!0):1===e&&null!==t?Hi(n,t,a,o||null,!0):2===e?function ns(e,n,t){e.removeChild(null,n,t)}(n,a,s):3===e&&n.destroyNode(a),null!=r&&function OS(e,n,t,i,o){const r=t[7];r!==se(t)&&jo(n,e,i,r,o);for(let a=10;a<t.length;a++){const l=t[a];ml(l[1],l,e,n,i,r)}}(n,e,r,t,o)}}function w_(e,n){n[10].changeDetectionScheduler?.notify(9),ml(e,n,n[U],2,null,null)}function nf(e,n){const t=e[9],i=t.indexOf(n);t.splice(i,1)}function os(e,n){if(On(n))return;const t=n[U];t.destroyNode&&ml(e,n,t,3,null,null),function TS(e){let n=e[12];if(!n)return of(e[1],e);for(;n;){let t=null;if(Ae(n))t=n[12];else{const i=n[10];i&&(t=i)}if(!t){for(;n&&!n[4]&&n!==e;)Ae(n)&&of(n[1],n),n=n[3];null===n&&(n=e),Ae(n)&&of(n[1],n),t=n&&n[4]}n=t}}(n)}function of(e,n){if(On(n))return;const t=J(null);try{n[2]&=-129,n[2]|=256,n[24]&&Xs(n[24]),function xS(e,n){let t;if(null!=e&&null!=(t=e.destroyHooks))for(let i=0;i<t.length;i+=2){const o=n[t[i]];if(!(o instanceof kr)){const r=t[i+1];if(Array.isArray(r))for(let s=0;s<r.length;s+=2){const a=o[r[s]],l=r[s+1];try{l.call(a)}finally{}}else try{r.call(o)}finally{}}}}(e,n),function SS(e,n){const t=e.cleanup,i=n[7];if(null!==t)for(let s=0;s<t.length-1;s+=2)if("string"==typeof t[s]){const a=t[s+3];a>=0?i[a]():i[-a].unsubscribe(),s+=2}else t[s].call(i[t[s+1]]);null!==i&&(n[7]=null);const o=n[21];if(null!==o){n[21]=null;for(let s=0;s<o.length;s++)(0,o[s])()}const r=n[23];if(null!==r){n[23]=null;for(const s of r)s.destroy()}}(e,n),1===n[1].type&&n[U].destroy();const i=n[16];if(null!==i&&qe(n[3])){i!==n[3]&&nf(i,n);const o=n[18];null!==o&&o.detachView(e)}nd(n)}finally{J(t)}}function rf(e,n,t){return function b_(e,n,t){let i=n;for(;null!==i&&168&i.type;)i=(n=i).parent;if(null===i)return t[0];if(Xt(i)){const{encapsulation:o}=e.data[i.directiveStart+i.componentOffset];if(o===Ut.None||o===Ut.Emulated)return null}return at(i,t)}(e,n.parent,t)}let I_=function M_(e,n,t){return 40&e.type?at(e,t):null};function gl(e,n,t,i){const o=rf(e,i,n),r=n[U],a=function E_(e,n,t){return I_(e,n,t)}(i.parent||n[5],i,n);if(null!=o)if(Array.isArray(t))for(let l=0;l<t.length;l++)f_(r,o,t[l],a,!1);else f_(r,o,t,a,!1)}function ji(e,n){if(null!==n){const t=n.type;if(3&t)return at(n,e);if(4&t)return af(-1,e[n.index]);if(8&t){const i=n.child;if(null!==i)return ji(e,i);{const o=e[n.index];return qe(o)?af(-1,o):se(o)}}if(128&t)return ji(e,n.next);if(32&t)return tf(n,e)()||se(e[n.index]);{const i=S_(e,n);return null!==i?Array.isArray(i)?i[0]:ji(kn(e[15]),i):ji(e,n.next)}}return null}function S_(e,n){return null!==n?e[15][5].projection[n.projection]:null}function af(e,n){const t=10+e+1;if(t<n.length){const i=n[t],o=i[1].firstChild;if(null!==o)return ji(i,o)}return n[7]}function lf(e,n,t,i,o,r,s){for(;null!=t;){if(128===t.type){t=t.next;continue}const a=i[t.index],l=t.type;if(s&&0===n&&(a&&Dt(se(a),i),t.flags|=2),!oi(t))if(8&l)lf(e,n,t.child,i,o,r,!1),jo(n,e,o,a,r);else if(32&l){const c=tf(t,i);let u;for(;u=c();)jo(n,e,o,u,r);jo(n,e,o,a,r)}else 16&l?x_(e,n,i,t,o,r):jo(n,e,o,a,r);t=s?t.projectionNext:t.next}}function ml(e,n,t,i,o,r){lf(t,i,e.firstChild,n,o,r,!1)}function x_(e,n,t,i,o,r){const s=t[15],l=s[5].projection[i.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)jo(n,e,o,l[c],r);else{let c=l;const u=s[3];Br(i)&&(c.flags|=128),lf(e,n,c,u,o,r,!0)}}function Uo(e,n,t,i,o=!1){for(;null!==t;){if(128===t.type){t=o?t.projectionNext:t.next;continue}const r=n[t.index];null!==r&&i.push(se(r)),qe(r)&&cf(r,i);const s=t.type;if(8&s)Uo(e,n,t.child,i);else if(32&s){const a=tf(t,n);let l;for(;l=a();)i.push(l)}else if(16&s){const a=S_(n,t);if(Array.isArray(a))i.push(...a);else{const l=kn(n[15]);Uo(l[1],l,a,i,!0)}}t=o?t.projectionNext:t.next}return i}function cf(e,n){for(let t=10;t<e.length;t++){const i=e[t],o=i[1].firstChild;null!==o&&Uo(i[1],i,o,n)}e[7]!==e[0]&&n.push(e[7])}function N_(e){if(null!==e[25]){for(const n of e[25])n.impl.addSequence(n);e[25].length=0}}let O_=[];const LS={...io,consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{bo(e.lView)},consumerOnSignalRead(){this.lView[24]=this}},VS={...io,consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=kn(e.lView);for(;n&&!A_(n[1]);)n=kn(n);n&&Mu(n)},consumerOnSignalRead(){this.lView[24]=this}};function A_(e){return 2!==e.type}function k_(e){if(null===e[23])return;let n=!0;for(;n;){let t=!1;for(const i of e[23])i.dirty&&(t=!0,null===i.zone||Zone.current===i.zone?i.run():i.zone.run(()=>i.run()));n=t&&!!(8192&e[2])}}function vl(e,n=!0,t=0){const o=e[10].rendererFactory;o.begin?.();try{!function BS(e,n){const t=qg();try{Na(!0),uf(e,n);let i=0;for(;Nr(e);){if(100===i)throw new x(103,!1);i++,uf(e,1)}}finally{Na(t)}}(e,t)}catch(s){throw n&&function Xd(e,n){const t=e[9],i=t?t.get(en,null):null;i&&i.handleError(n)}(e,s),s}finally{o.end?.()}}function R_(e,n,t,i){if(On(n))return;const o=n[2];Vu(n);let a=!0,l=null,c=null;A_(e)?(c=function kS(e){return e[24]??function FS(e){const n=O_.pop()??Object.create(LS);return n.lView=e,n}(e)}(n),l=oo(c)):null===function Pc(){return Pe}()?(a=!1,c=function PS(e){const n=e[24]??Object.create(VS);return n.lView=e,n}(n),l=oo(c)):n[24]&&(Xs(n[24]),n[24]=null);try{jg(n),function Wg(e){return W.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==t&&v_(e,n,t,2,i);const u=!(3&~o);if(u){const h=e.preOrderCheckHooks;null!==h&&Aa(n,h,null)}else{const h=e.preOrderHooks;null!==h&&ka(n,h,0,null),ju(n,0)}if(function jS(e){for(let n=zm(e);null!==n;n=Gm(n)){if(!(2&n[2]))continue;const t=n[9];for(let i=0;i<t.length;i++)Mu(t[i])}}(n),k_(n),L_(n,0),null!==e.contentQueries&&Pv(e,n),u){const h=e.contentCheckHooks;null!==h&&Aa(n,h)}else{const h=e.contentHooks;null!==h&&ka(n,h,1),ju(n,1)}!function $S(e,n){const t=e.hostBindingOpCodes;if(null!==t)try{for(let i=0;i<t.length;i++){const o=t[i];if(o<0)Fi(~o);else{const r=o,s=t[++i],a=t[++i];p0(s,r);const l=n[r];a(2,l)}}}finally{Fi(-1)}}(e,n);const d=e.components;null!==d&&V_(n,d,0);const p=e.viewQuery;if(null!==p&&kd(2,p,i),u){const h=e.viewCheckHooks;null!==h&&Aa(n,h)}else{const h=e.viewHooks;null!==h&&ka(n,h,2),ju(n,2)}if(!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),n[22]){for(const h of n[22])h();n[22]=null}N_(n),n[2]&=-73}catch(u){throw bo(n),u}finally{null!==c&&(wr(c,l),a&&function RS(e){e.lView[24]!==e&&(e.lView=null,O_.push(e))}(c)),Hu()}}function L_(e,n){for(let t=zm(e);null!==t;t=Gm(t))for(let i=10;i<t.length;i++)P_(t[i],n)}function US(e,n,t){const i=lt(n,e);P_(i,t)}function P_(e,n){Eu(e)&&uf(e,n)}function uf(e,n){const i=e[1],o=e[2],r=e[24];let s=!!(0===n&&16&o);if(s||=!!(64&o&&0===n),s||=!!(1024&o),s||=!(!r?.dirty||!Ks(r)),s||=!1,r&&(r.dirty=!1),e[2]&=-9217,s)R_(i,e,i.template,e[8]);else if(8192&o){k_(e),L_(e,1);const a=i.components;null!==a&&V_(e,a,1),N_(e)}}function V_(e,n,t){for(let i=0;i<n.length;i++)US(e,n[i],t)}function rs(e,n){const t=qg()?64:1088;for(e[10].changeDetectionScheduler?.notify(n);e;){e[2]|=t;const i=kn(e);if(Nn(e)&&!i)return e;e=i}return null}function H_(e,n,t,i){return[e,!0,0,n,null,i,null,t,null,null]}function ss(e,n){if(e.length<=10)return;const t=10+n,i=e[t];if(i){const o=i[16];null!==o&&o!==e&&nf(o,i),n>0&&(e[t-1][4]=i[4]);const r=ma(e,10+n);!function D_(e,n){w_(e,n),n[0]=null,n[5]=null}(i[1],i);const s=r[18];null!==s&&s.detachView(r[1]),i[3]=null,i[4]=null,i[2]&=-129}return i}function j_(e,n){const t=e[9],i=n[3];(Ae(i)||n[15]!==i[3][15])&&(e[2]|=2),null===t?e[9]=[n]:t.push(n)}let as=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){const n=this._lView,t=n[1];return Uo(t,n,t.firstChild,[])}constructor(n,t,i=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=i}get context(){return this._lView[8]}set context(n){this._lView[8]=n}get destroyed(){return On(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const n=this._lView[3];if(qe(n)){const t=n[8],i=t?t.indexOf(this):-1;i>-1&&(ss(n,i),ma(t,i))}this._attachedToViewContainer=!1}os(this._lView[1],this._lView)}onDestroy(n){Sa(this._lView,n)}markForCheck(){rs(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[2]&=-129}reattach(){Iu(this._lView),this._lView[2]|=128}detectChanges(){this._lView[2]|=1024,vl(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new x(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;const n=Nn(this._lView),t=this._lView[16];null!==t&&!n&&nf(t,this._lView),w_(this._lView[1],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new x(902,!1);this._appRef=n;const t=Nn(this._lView),i=this._lView[16];null!==i&&!t&&j_(i,this._lView),Iu(this._lView)}},jn=(()=>class e{static __NG_ELEMENT_ID__=WS})();const GS=jn,qS=class extends GS{_declarationLView;_declarationTContainer;elementRef;constructor(n,t,i){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=i}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,i){const o=function Bo(e,n,t,i){const o=J(null);try{const r=n.tView,l=dl(e,r,t,4096&e[2]?4096:16,null,n,null,null,i?.injector??null,i?.embeddedViewInjector??null,i?.dehydratedView??null);l[16]=e[n.index];const u=e[18];return null!==u&&(l[18]=u.createEmbeddedView(r)),pl(r,l,t),l}finally{J(o)}}(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:i});return new as(o)}};function WS(){return _l(ee(),D())}function _l(e,n){return 4&e.type?new qS(n,e,Io(e,n)):null}function zo(e,n,t,i,o){let r=e.data[n];if(null===r)r=function gf(e,n,t,i,o){const r=$g(),s=ku(),l=e.data[n]=function ix(e,n,t,i,o,r){let s=n?n.injectorIndex:-1,a=0;return function ki(){return null!==W.skipHydrationRootTNode}()&&(a|=128),{type:t,index:i,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:r,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?r:r&&r.parent,t,n,i,o);return function nx(e,n,t,i){null===e.firstChild&&(e.firstChild=n),null!==t&&(i?null==t.child&&null!==n.parent&&(t.child=n):null===t.next&&(t.next=n,n.prev=t))}(e,l,r,s),l}(e,n,t,i,o),function h0(){return W.lFrame.inI18n}()&&(r.flags|=32);else if(64&r.type){r.type=t,r.value=i,r.attrs=o;const s=function Or(){const e=W.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}();r.injectorIndex=null===s?-1:s.injectorIndex}return Jt(r,!0),r}let $x=class{},uy=class{};class zx{resolveComponentFactory(n){throw Error(`No component factory found for ${ze(n)}.`)}}let Ml=class{static NULL=new zx};class bf{}let nn=(()=>class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>function Gx(){const e=D(),t=lt(ee().index,e);return(Ae(t)?t:e)[U]}()})(),qx=(()=>{class e{static \u0275prov=te({token:e,providedIn:"root",factory:()=>null})}return e})();const Mf={};class Zo{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,i){i=ga(i);const o=this.injector.get(n,Mf,i);return o!==Mf||t===Mf?o:this.parentInjector.get(n,t,i)}}function If(e,n,t){let i=t?e.styles:null,o=t?e.classes:null,r=0;if(null!==n)for(let s=0;s<n.length;s++){const a=n[s];"number"==typeof a?r=a:1==r?o=Kc(o,a):2==r&&(i=Kc(i,a+": "+n[++s]+";"))}t?e.styles=i:e.stylesWithoutHost=i,t?e.classes=o:e.classesWithoutHost=o}function T(e,n=ie.Default){const t=D();return null===t?re(e,n):hm(ee(),t,q(e),n)}function Tf(e,n,t,i,o){const r=null===i?null:{"":-1},s=o(e,t);if(null!==s){let a,l=null,c=null;const u=function Xx(e){let n=null,t=!1;for(let s=0;s<e.length;s++){const a=e[s];if(0===s&&Ft(a)&&(n=a),null!==a.findHostDirectiveDefs){t=!0;break}}if(!t)return null;let i=null,o=null,r=null;for(const s of e)null!==s.findHostDirectiveDefs&&(i??=[],o??=new Map,r??=new Map,Jx(s,i,r,o)),s===n&&(i??=[],i.push(s));return null!==i?(i.push(...null===n?e:e.slice(1)),[i,o,r]):null}(s);null===u?a=s:[a,l,c]=u,function tN(e,n,t,i,o,r,s){const a=i.length;let l=!1;for(let p=0;p<a;p++){const h=i[p];!l&&Ft(h)&&(l=!0,eN(e,t,p)),Wu(Ra(t,n),e,h.type)}!function aN(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}(t,e.data.length,a);for(let p=0;p<a;p++){const h=i[p];h.providersResolver&&h.providersResolver(h)}let c=!1,u=!1,d=is(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let p=0;p<a;p++){const h=i[p];if(t.mergedAttrs=Mo(t.mergedAttrs,h.hostAttrs),iN(e,t,n,d,h),sN(d,h,o),null!==s&&s.has(h)){const[C,E]=s.get(h);t.directiveToIndex.set(h.type,[d,C+t.directiveStart,E+t.directiveStart])}else(null===r||!r.has(h))&&t.directiveToIndex.set(h.type,d);null!==h.contentQueries&&(t.flags|=4),(null!==h.hostBindings||null!==h.hostAttrs||0!==h.hostVars)&&(t.flags|=64);const m=h.type.prototype;!c&&(m.ngOnChanges||m.ngOnInit||m.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),c=!0),!u&&(m.ngOnChanges||m.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),u=!0),d++}!function nN(e,n,t){for(let i=n.directiveStart;i<n.directiveEnd;i++){const o=e.data[i];if(null!==t&&t.has(o)){const r=t.get(o);hy(0,n,r,i),hy(1,n,r,i),gy(n,i,!0)}else fy(0,n,o,i),fy(1,n,o,i),gy(n,i,!1)}}(e,t,r)}(e,n,t,a,r,l,c)}null!==r&&null!==i&&function Kx(e,n,t){const i=e.localNames=[];for(let o=0;o<n.length;o+=2){const r=t[n[o+1]];if(null==r)throw new x(-301,!1);i.push(n[o],r)}}(t,i,r)}function Jx(e,n,t,i){const o=n.length;e.findHostDirectiveDefs(e,n,i),t.set(e,[o,n.length-1])}function eN(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function fy(e,n,t,i){const o=0===e?t.inputs:t.outputs;for(const r in o)if(o.hasOwnProperty(r)){let s;s=0===e?n.inputs??={}:n.outputs??={},s[r]??=[],s[r].push(i),py(n,r)}}function hy(e,n,t,i){const o=0===e?t.inputs:t.outputs;for(const r in o)if(o.hasOwnProperty(r)){const s=o[r];let a;a=0===e?n.hostDirectiveInputs??={}:n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(i,r),py(n,s)}}function py(e,n){"class"===n?e.flags|=8:"style"===n&&(e.flags|=16)}function gy(e,n,t){const{attrs:i,inputs:o,hostDirectiveInputs:r}=e;if(null===i||!t&&null===o||t&&null===r||Ud(e))return e.initialInputs??=[],void e.initialInputs.push(null);let s=null,a=0;for(;a<i.length;){const l=i[a];if(0!==l)if(5!==l){if("number"==typeof l)break;if(!t&&o.hasOwnProperty(l)){const c=o[l];for(const u of c)if(u===n){s??=[],s.push(l,i[a+1]);break}}else if(t&&r.hasOwnProperty(l)){const c=r[l];for(let u=0;u<c.length;u+=2)if(c[u]===n){s??=[],s.push(c[u+1],i[a+1]);break}}a+=2}else a+=2;else a+=4}e.initialInputs??=[],e.initialInputs.push(s)}function iN(e,n,t,i,o){e.data[i]=o;const r=o.factory||(o.factory=Ei(o.type)),s=new kr(r,Ft(o),T);e.blueprint[i]=s,t[i]=s,function oN(e,n,t,i,o){const r=o.hostBindings;if(r){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~n.index;(function rN(e){let n=e.length;for(;n>0;){const t=e[--n];if("number"==typeof t&&t<0)return t}return 0})(s)!=a&&s.push(a),s.push(t,i,r)}}(e,n,i,is(e,t,o.hostVars,Y),o)}function sN(e,n,t){if(t){if(n.exportAs)for(let i=0;i<n.exportAs.length;i++)t[n.exportAs[i]]=e;Ft(n)&&(t[""]=e)}}function my(e,n,t,i,o,r,s,a){const l=n.consts,u=zo(n,e,2,i,Bt(l,s));return r&&Tf(n,t,u,Bt(l,a),o),u.mergedAttrs=Mo(u.mergedAttrs,u.attrs),null!==u.attrs&&If(u,u.attrs,!1),null!==u.mergedAttrs&&If(u,u.mergedAttrs,!0),null!==n.queries&&n.queries.elementStart(n,u),u}function vy(e,n){Bu(e,n),Du(n)&&e.queries.elementEnd(n)}class _y extends Ml{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){const t=ne(n);return new vs(t,this.ngModule)}}class vs extends uy{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=function lN(e){return Object.keys(e).map(n=>{const[t,i,o]=e[n],r={propName:t,templateName:n,isSignal:!!(i&fl.SignalBased)};return o&&(r.transform=o),r})}(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=function cN(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=function sS(e){return e.map(rS).join(",")}(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,i,o){const r=J(null);try{const s=this.componentDef,a=i?["ng-version","19.2.9"]:function aS(e){const n=[],t=[];let i=1,o=2;for(;i<e.length;){let r=e[i];if("string"==typeof r)2===o?""!==r&&n.push(r,e[++i]):8===o&&t.push(r);else{if(!tn(o))break;o=r}i++}return t.length&&n.push(1,...t),n}(this.componentDef.selectors[0]),l=Gd(0,null,null,1,0,null,null,null,null,[a],null),c=function uN(e,n,t){let i=n instanceof Yt?n:n?.injector;return i&&null!==e.getStandaloneInjector&&(i=e.getStandaloneInjector(i)||i),i?new Zo(t,i):t}(s,o||this.ngModule,n),u=function dN(e){const n=e.get(bf,null);if(null===n)throw new x(407,!1);return{rendererFactory:n,sanitizer:e.get(qx,null),changeDetectionScheduler:e.get(Ln,null)}}(c),d=u.rendererFactory.createRenderer(null,s),p=i?function fS(e,n,t,i){const r=i.get(iv,!1)||t===Ut.ShadowDom,s=e.selectRootElement(n,r);return function hS(e){__(e)}(s),s}(d,i,s.encapsulation,c):function fN(e,n){const t=(e.selectors[0][0]||"div").toLowerCase();return ul(n,t,"svg"===t?"svg":"math"===t?"math":null)}(s,d),h=dl(null,l,null,512|qd(s),null,null,u,d,c,null,null);h[A]=p,Vu(h);let m=null;try{const C=my(A,l,h,"#host",()=>[this.componentDef],!0,0);p&&(p_(d,p,C),Dt(p,h)),hl(l,h,C),Fd(l,C,h),vy(l,C),void 0!==t&&function pN(e,n,t){const i=e.projection=[];for(let o=0;o<n.length;o++){const r=t[o];i.push(null!=r&&r.length?Array.from(r):null)}}(C,this.ngContentSelectors,t),m=lt(C.index,h),h[8]=m[8],pl(l,h,null)}catch(C){throw null!==m&&nd(m),nd(h),C}finally{Hu()}return new hN(this.componentType,h)}finally{J(r)}}}class hN extends $x{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t){super(),this._rootLView=t,this._tNode=Oi(t[1],A),this.location=Io(this._tNode,t),this.instance=lt(this._tNode.index,t)[8],this.hostView=this.changeDetectorRef=new as(t,void 0,!1),this.componentType=n}setInput(n,t){const i=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;const o=this._rootLView;Jd(i,o[1],o,n,t),this.previousInputValues.set(n,t),rs(lt(i.index,o),1)}get injector(){return new ke(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}}let yn=(()=>class e{static __NG_ELEMENT_ID__=gN})();function gN(){return Dy(ee(),D())}const mN=yn,yy=class extends mN{_lContainer;_hostTNode;_hostLView;constructor(n,t,i){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=i}get element(){return Io(this._hostTNode,this._hostLView)}get injector(){return new ke(this._hostTNode,this._hostLView)}get parentInjector(){const n=La(this._hostTNode,this._hostLView);if(zu(n)){const t=Rr(n,this._hostLView),i=Fr(n);return new ke(t[1].data[i+8],t)}return new ke(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){const t=Cy(this._lContainer);return null!==t&&t[n]||null}get length(){return this._lContainer.length-10}createEmbeddedView(n,t,i){let o,r;"number"==typeof i?o=i:null!=i&&(o=i.index,r=i.injector);const a=n.createEmbeddedViewImpl(t||{},r,null);return this.insertImpl(a,o,Bi(this._hostTNode,null)),a}createComponent(n,t,i,o,r){const s=n&&!function Sr(e){return"function"==typeof e}(n);let a;if(s)a=t;else{const m=t||{};a=m.index,i=m.injector,o=m.projectableNodes,r=m.environmentInjector||m.ngModuleRef}const l=s?n:new vs(ne(n)),c=i||this.parentInjector;if(!r&&null==l.ngModule){const C=(s?c:this.parentInjector).get(Yt,null);C&&(r=C)}ne(l.componentType??{});const h=l.create(c,o,null,r);return this.insertImpl(h.hostView,a,Bi(this._hostTNode,null)),h}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,i){const o=n._lView;if(function n0(e){return qe(e[3])}(o)){const a=this.indexOf(n);if(-1!==a)this.detach(a);else{const l=o[3],c=new yy(l,l[5],l[3]);c.detach(c.indexOf(n))}}const r=this._adjustIndex(t),s=this._lContainer;return function $o(e,n,t,i=!0){const o=n[1];if(function zS(e,n,t,i){const o=10+i,r=t.length;i>0&&(t[o-1][4]=n),i<r-10?(n[4]=t[o],vg(t,10+i,n)):(t.push(n),n[4]=null),n[3]=t;const s=n[16];null!==s&&t!==s&&j_(s,n);const a=n[18];null!==a&&a.insertView(e),Iu(n),n[2]|=128}(o,n,e,t),i){const s=af(t,e),a=n[U],l=a.parentNode(e[7]);null!==l&&function IS(e,n,t,i,o,r){i[0]=o,i[5]=n,ml(e,i,t,1,o,r)}(o,e[5],a,n,l,s)}const r=n[6];null!==r&&null!==r.firstChild&&(r.firstChild=null)}(s,o,r,i),n.attachToViewContainerRef(),vg(Sf(s),r,n),n}move(n,t){return this.insert(n,t)}indexOf(n){const t=Cy(this._lContainer);return null!==t?t.indexOf(n):-1}remove(n){const t=this._adjustIndex(n,-1),i=ss(this._lContainer,t);i&&(ma(Sf(this._lContainer),t),os(i[1],i))}detach(n){const t=this._adjustIndex(n,-1),i=ss(this._lContainer,t);return i&&null!=ma(Sf(this._lContainer),t)?new as(i):null}_adjustIndex(n,t=0){return n??this.length+t}};function Cy(e){return e[8]}function Sf(e){return e[8]||(e[8]=[])}function Dy(e,n){let t;const i=n[e.index];return qe(i)?t=i:(t=H_(i,n,null,e),n[e.index]=t,Wd(n,t)),wy(t,n,e,i),new yy(t,e,n)}let wy=function Ey(e,n,t,i){if(e[7])return;let o;o=8&t.type?se(i):function vN(e,n){const t=e[U],i=t.createComment(""),o=at(n,e),r=t.parentNode(o);return Hi(t,r,i,t.nextSibling(o),!1),i}(n,t),e[7]=o};class Nf{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new Nf(this.queryList)}setDirty(){this.queryList.setDirty()}}class Of{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){const t=n.queries;if(null!==t){const i=null!==n.contentQueries?n.contentQueries[0]:t.length,o=[];for(let r=0;r<i;r++){const s=t.getByIndex(r);o.push(this.queries[s.indexInDeclarationView].clone())}return new Of(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)null!==Lf(n,t).matches&&this.queries[t].setDirty()}}class My{flags;read;predicate;constructor(n,t,i=null){this.flags=t,this.read=i,this.predicate="string"==typeof n?function MN(e){return e.split(",").map(n=>n.trim())}(n):n}}class Af{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let i=0;i<this.queries.length;i++)this.queries[i].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let i=0;i<this.length;i++){const o=null!==t?t.length:0,r=this.getByIndex(i).embeddedTView(n,o);r&&(r.indexInDeclarationView=i,null!==t?t.push(r):t=[r])}return null!==t?new Af(t):null}template(n,t){for(let i=0;i<this.queries.length;i++)this.queries[i].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}}class kf{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new kf(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&1&~this.metadata.flags){const t=this._declarationNodeIndex;let i=n.parent;for(;null!==i&&8&i.type&&i.index!==t;)i=i.parent;return t===(null!==i?i.index:-1)}return this._appliesToNextNode}matchTNode(n,t){const i=this.metadata.predicate;if(Array.isArray(i))for(let o=0;o<i.length;o++){const r=i[o];this.matchTNodeWithReadOption(n,t,DN(t,r)),this.matchTNodeWithReadOption(n,t,Pa(t,n,r,!1,!1))}else i===jn?4&t.type&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,Pa(t,n,i,!1,!1))}matchTNodeWithReadOption(n,t,i){if(null!==i){const o=this.metadata.read;if(null!==o)if(o===ut||o===yn||o===jn&&4&t.type)this.addMatch(t.index,-2);else{const r=Pa(t,n,o,!1,!1);null!==r&&this.addMatch(t.index,r)}else this.addMatch(t.index,i)}}addMatch(n,t){null===this.matches?this.matches=[n,t]:this.matches.push(n,t)}}function DN(e,n){const t=e.localNames;if(null!==t)for(let i=0;i<t.length;i+=2)if(t[i]===n)return t[i+1];return null}function bN(e,n,t,i){return-1===t?function wN(e,n){return 11&e.type?Io(e,n):4&e.type?_l(e,n):null}(n,e):-2===t?function EN(e,n,t){return t===ut?Io(n,e):t===jn?_l(n,e):t===yn?Dy(n,e):void 0}(e,n,i):Lr(e,e[1],t,n)}function Iy(e,n,t,i){const o=n[18].queries[i];if(null===o.matches){const r=e.data,s=t.matches,a=[];for(let l=0;null!==s&&l<s.length;l+=2){const c=s[l];a.push(c<0?null:bN(n,r[c],s[l+1],t.metadata.read))}o.matches=a}return o.matches}function Ff(e,n,t,i){const o=e.queries.getByIndex(t),r=o.matches;if(null!==r){const s=Iy(e,n,o,t);for(let a=0;a<r.length;a+=2){const l=r[a];if(l>0)i.push(s[a/2]);else{const c=r[a+1],u=n[-l];for(let d=10;d<u.length;d++){const p=u[d];p[16]===p[3]&&Ff(p[1],p,c,i)}if(null!==u[9]){const d=u[9];for(let p=0;p<d.length;p++){const h=d[p];Ff(h[1],h,c,i)}}}}}return i}function Ty(e,n,t){const i=new nT(!(4&~t));return function o0(e,n,t,i){const o=Su(n);o.push(t),e.firstCreatePass&&xu(e).push(i,o.length-1)}(e,n,i,i.destroy),(n[18]??=new Of).queries.push(new Nf(i))-1}function Ny(e,n,t){null===e.queries&&(e.queries=new Af),e.queries.track(new kf(n,t))}function Lf(e,n){return e.queries.getByIndex(n)}function Oy(e,n){const t=e[1],i=Lf(t,n);return i.crossesNgTemplate?Ff(t,e,n,[]):Iy(t,e,i,n)}let $i=class{},LN=class{};class Hf extends $i{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new _y(this);constructor(n,t,i,o=!0){super(),this.ngModuleType=n,this._parent=t;const r=function Ot(e,n){const t=e[cg]||null;if(!t&&!0===n)throw new Error(`Type ${ze(e)} does not have '\u0275mod' property.`);return t}(n);this._bootstrapComponents=function vn(e){return e instanceof Function?e():e}(r.bootstrap),this._r3Injector=bm(n,t,[{provide:$i,useValue:this},{provide:Ml,useValue:this.componentFactoryResolver},...i],ze(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){const n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}}class Bf extends LN{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new Hf(this.moduleType,n,[])}}class Uy extends $i{injector;componentFactoryResolver=new _y(this);instance=null;constructor(n){super();const t=new Ti([...n.providers,{provide:$i,useValue:this},{provide:Ml,useValue:this.componentFactoryResolver}],n.parent||Da(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}}let HN=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){const i=fu(0,t.type),o=i.length>0?function $y(e,n,t=null){return new Uy({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}([i],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(const t of this.cachedInjectors.values())null!==t&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=te({token:e,providedIn:"environment",factory:()=>new e(re(Yt))})}return e})();function on(e){return In(()=>{const n=zy(e),t={...n,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ua.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(HN).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ut.Emulated,styles:e.styles||le,_:null,schemas:e.schemas||null,tView:null,id:""};n.standalone&&function Rt(e){av.has(e)||(av.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}("NgStandalone"),Gy(t);const i=e.dependencies;return t.directiveDefs=Tl(i,!1),t.pipeDefs=Tl(i,!0),t.id=function GN(e){let n=0;const i=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,"function"==typeof e.consts?"":e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(const r of i.join("|"))n=Math.imul(31,n)+r.charCodeAt(0)|0;return n+=2147483648,"c"+n}(t),t})}function BN(e){return ne(e)||function Xe(e){return e[nu]||null}(e)}function jN(e){return null!==e}function si(e){return In(()=>({type:e.type,bootstrap:e.bootstrap||le,declarations:e.declarations||le,imports:e.imports||le,exports:e.exports||le,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function UN(e,n){if(null==e)return Zt;const t={};for(const i in e)if(e.hasOwnProperty(i)){const o=e[i];let r,s,a,l;Array.isArray(o)?(a=o[0],r=o[1],s=o[2]??r,l=o[3]||null):(r=o,s=o,a=fl.None,l=null),t[r]=[i,a,l],n[r]=s}return t}function $N(e){if(null==e)return Zt;const n={};for(const t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function Z(e){return In(()=>{const n=zy(e);return Gy(n),n})}function bt(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function zy(e){const n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||Zt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:!0===e.signals,selectors:e.selectors||le,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:UN(e.inputs,n),outputs:$N(e.outputs),debugInfo:null}}function Gy(e){e.features?.forEach(n=>n(e))}function Tl(e,n){if(!e)return null;const t=n?At:BN;return()=>("function"==typeof e?e():e).map(i=>t(i)).filter(jN)}function ue(e){let n=function qy(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),t=!0;const i=[e];for(;n;){let o;if(Ft(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new x(903,!1);o=n.\u0275dir}if(o){if(t){i.push(o);const s=e;s.inputs=jf(e.inputs),s.declaredInputs=jf(e.declaredInputs),s.outputs=jf(e.outputs);const a=o.hostBindings;a&&YN(e,a);const l=o.viewQuery,c=o.contentQueries;if(l&&ZN(e,l),c&&QN(e,c),qN(e,o),lI(e.outputs,o.outputs),Ft(o)&&o.data.animation){const u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}const r=o.features;if(r)for(let s=0;s<r.length;s++){const a=r[s];a&&a.ngInherit&&a(e),a===ue&&(t=!1)}}n=Object.getPrototypeOf(n)}!function WN(e){let n=0,t=null;for(let i=e.length-1;i>=0;i--){const o=e[i];o.hostVars=n+=o.hostVars,o.hostAttrs=Mo(o.hostAttrs,t=Mo(t,o.hostAttrs))}}(i)}function qN(e,n){for(const t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;const i=n.inputs[t];void 0!==i&&(e.inputs[t]=i,e.declaredInputs[t]=n.declaredInputs[t])}}function jf(e){return e===Zt?{}:e===le?[]:e}function ZN(e,n){const t=e.viewQuery;e.viewQuery=t?(i,o)=>{n(i,o),t(i,o)}:n}function QN(e,n){const t=e.contentQueries;e.contentQueries=t?(i,o,r)=>{n(i,o,r),t(i,o,r)}:n}function YN(e,n){const t=e.hostBindings;e.hostBindings=t?(i,o)=>{n(i,o),t(i,o)}:n}function Sl(e){return!!$f(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function $f(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function Cn(e,n,t){return e[n]=t}function Fe(e,n,t){return!Object.is(e[n],t)&&(e[n]=t,!0)}function zi(e,n,t,i){const o=Fe(e,n,t);return Fe(e,n+1,i)||o}function L(e,n,t,i,o,r,s,a){const l=D(),c=G();return function Cs(e,n,t,i,o,r,s,a,l,c){const u=t+A,d=n.firstCreatePass?function rO(e,n,t,i,o,r,s,a,l){const c=n.consts,u=zo(n,e,4,s||null,a||null);Au()&&Tf(n,t,u,Bt(c,l),Yd),u.mergedAttrs=Mo(u.mergedAttrs,u.attrs),Bu(n,u);const d=u.tView=Gd(2,u,i,o,r,n.directiveRegistry,n.pipeRegistry,null,n.schemas,c,null);return null!==n.queries&&(n.queries.template(n,u),d.queries=n.queries.embeddedTView(u)),u}(u,n,e,i,o,r,s,a,l):n.data[u];Jt(d,!1);const p=Ky(n,e,d,t);Ar()&&gl(n,e,p,d),Dt(p,e);const h=H_(p,e,p,d);return e[u]=h,Wd(e,h),Ia(d)&&hl(n,e,d),null!=l&&Qd(e,d,c),d}(l,c,e,n,t,i,o,Bt(c.consts,r),s,a),L}let Ky=function Xy(e,n,t,i){return hn(!0),n[U].createComment("")};const fC=new R(""),kl=new R("");let Kf,Qf=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];taskTrackingZone=null;constructor(t,i,o){this._ngZone=t,this.registry=i,Kf||(function iA(e){Kf=e}(o),o.addToWindow(i)),this._watchAngularEvents(),t.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{he.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let t=this._callbacks.pop();clearTimeout(t.timeoutId),t.doneCb()}});else{let t=this.getPendingTasks();this._callbacks=this._callbacks.filter(i=>!i.updateCb||!i.updateCb(t)||(clearTimeout(i.timeoutId),!1))}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(t=>({source:t.source,creationLocation:t.creationLocation,data:t.data})):[]}addCallback(t,i,o){let r=-1;i&&i>0&&(r=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==r),t()},i)),this._callbacks.push({doneCb:t,timeoutId:r,updateCb:o})}whenStable(t,i,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(t,i,o),this._runCallbacksIfReady()}registerApplication(t){this.registry.registerApplication(t,this)}unregisterApplication(t){this.registry.unregisterApplication(t)}findProviders(t,i,o){return[]}static \u0275fac=function(i){return new(i||e)(re(he),re(Yf),re(kl))};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})(),Yf=(()=>{class e{_applications=new Map;registerApplication(t,i){this._applications.set(t,i)}unregisterApplication(t){this._applications.delete(t)}unregisterAllApplications(){this._applications.clear()}getTestability(t){return this._applications.get(t)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(t,i=!0){return Kf?.findTestabilityInTree(this,t,i)??null}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),hC=(()=>{class e{static \u0275prov=te({token:e,providedIn:"root",factory:()=>new oA})}return e})();class oA{queuedEffectCount=0;queues=new Map;schedule(n){this.enqueue(n)}remove(n){const i=this.queues.get(n.zone);i.has(n)&&(i.delete(n),this.queuedEffectCount--)}enqueue(n){const t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);const i=this.queues.get(t);i.has(n)||(this.queuedEffectCount++,i.add(n))}flush(){for(;this.queuedEffectCount>0;)for(const[n,t]of this.queues)null===n?this.flushQueue(t):n.run(()=>this.flushQueue(t))}flushQueue(n){for(const t of n)n.delete(t),this.queuedEffectCount--,t.run()}}function Fl(e){return!!e&&"function"==typeof e.then}function pC(e){return!!e&&"function"==typeof e.subscribe}const gC=new R("");let mC=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,i)=>{this.resolve=t,this.reject=i});appInits=k(gC,{optional:!0})??[];injector=k(et);constructor(){}runInitializers(){if(this.initialized)return;const t=[];for(const o of this.appInits){const r=Ig(this.injector,o);if(Fl(r))t.push(r);else if(pC(r)){const s=new Promise((a,l)=>{r.subscribe({complete:a,error:l})});t.push(s)}}const i=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{i()}).catch(o=>{this.reject(o)}),0===t.length&&i(),this.initialized=!0}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const Rl=new R("");function _C(e,n){return Array.isArray(n)?n.reduce(_C,e):{...e,...n}}let zt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=k(J0);afterRenderManager=k(Cd);zonelessEnabled=k(Pr);rootEffectScheduler=k(hC);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new un;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=k(Li).hasPendingTasks.pipe(Qc(t=>!t));constructor(){k(So,{optional:!0})}whenStable(){let t;return new Promise(i=>{t=this.isStable.subscribe({next:o=>{o&&i()}})}).finally(()=>{t.unsubscribe()})}_injector=k(Yt);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,i){return this.bootstrapImpl(t,i)}bootstrapImpl(t,i,o=et.NULL){const r=t instanceof uy;if(!this._injector.get(mC).done)throw new x(405,"");let a;a=r?t:this._injector.get(Ml).resolveComponentFactory(t),this.componentTypes.push(a.componentType);const l=function rA(e){return e.isBoundToModule}(a)?void 0:this._injector.get($i),u=a.create(o,[],i||a.selector,l),d=u.location.nativeElement,p=u.injector.get(fC,null);return p?.registerApplication(d),u.onDestroy(()=>{this.detachView(u.hostView),Ll(this.components,u),p?.unregisterApplication(d)}),this._loadComponent(u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){null!==this.tracingSnapshot?this.tracingSnapshot.run(yd.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new x(101,!1);const t=J(null);try{this._runningTick=!0,this.synchronize()}catch(i){this.internalErrorHandler(i)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,J(t),this.afterTick.next()}};synchronize(){null===this._rendererFactory&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(bf,null,{optional:!0}));let t=0;for(;0!==this.dirtyFlags&&t++<10;)this.synchronizeOnce()}synchronizeOnce(){if(16&this.dirtyFlags&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),7&this.dirtyFlags){const t=!!(1&this.dirtyFlags);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:i,notifyErrorHandler:o}of this.allViews)aA(i,o,t,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),23&this.dirtyFlags)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();8&this.dirtyFlags&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){this.allViews.some(({_lView:t})=>Nr(t))?this.dirtyFlags|=2:this.dirtyFlags&=-8}attachView(t){const i=t;this._views.push(i),i.attachToAppRef(this)}detachView(t){const i=t;Ll(this._views,i),i.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t),this._injector.get(Rl,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>Ll(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new x(406,!1);const t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ll(e,n){const t=e.indexOf(n);t>-1&&e.splice(t,1)}function aA(e,n,t,i){(t||Nr(e))&&vl(e,n,t&&!i?0:1)}function ft(e,n,t,i){const o=D();return Fe(o,jt(),n)&&(G(),_n(ve(),o,e,n,t,i)),ft}function Jo(e,n,t,i){return Fe(e,jt(),t)?n+Q(t)+i:Y}function Pl(e,n){return e<<17|n<<2}function qi(e){return e>>17&32767}function nh(e){return 2|e}function ar(e){return(131068&e)>>2}function ih(e,n){return-131069&e|n<<2}function oh(e){return 1|e}function LC(e,n,t,i){const o=e[t+1],r=null===n;let s=i?qi(o):ar(o),a=!1;for(;0!==s&&(!1===a||r);){const c=e[s+1];WA(e[s],n)&&(a=!0,e[s+1]=i?oh(c):nh(c)),s=i?qi(c):ar(c)}a&&(e[t+1]=i?nh(o):oh(o))}function WA(e,n){return null===e||null==n||(Array.isArray(e)?e[1]:e)===n||!(!Array.isArray(e)||"string"!=typeof n)&&Ir(e,n)>=0}const We={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function PC(e){return e.substring(We.key,We.keyEnd)}function VC(e,n){const t=We.textEnd;return t===n?-1:(n=We.keyEnd=function KA(e,n,t){for(;n<t&&e.charCodeAt(n)>32;)n++;return n}(e,We.key=n,t),lr(e,n,t))}function lr(e,n,t){for(;n<t&&e.charCodeAt(n)<=32;)n++;return n}function g(e,n,t){const i=D();return Fe(i,jt(),n)&&wt(G(),ve(),i,e,n,i[U],t,!1),g}function rh(e,n,t,i,o){Jd(n,e,t,o?"class":"style",i)}function Vl(e,n,t){return rn(e,n,t,!1),Vl}function Un(e,n){return rn(e,n,null,!0),Un}function wn(e,n){for(let t=function QA(e){return function BC(e){We.key=0,We.keyEnd=0,We.value=0,We.valueEnd=0,We.textEnd=e.length}(e),VC(e,lr(e,0,We.textEnd))}(n);t>=0;t=VC(n,t))Nt(e,PC(n),!0)}function rn(e,n,t,i){const o=D(),r=G(),s=Rn(2);r.firstUpdatePass&&zC(r,e,s,i),n!==Y&&Fe(o,s,n)&&qC(r,r.data[Je()],o,o[U],e,o[s+1]=function ck(e,n){return null==e||""===e||("string"==typeof n?e+=n:"object"==typeof e&&(e=ze(ti(e)))),e}(n,t),i,s)}function $C(e,n){return n>=e.expandoStartIndex}function zC(e,n,t,i){const o=e.data;if(null===o[t+1]){const r=o[Je()],s=$C(e,t);ZC(r,i)&&null===n&&!s&&(n=!1),n=function nk(e,n,t,i){const o=function Lu(e){const n=W.lFrame.currentDirectiveIndex;return-1===n?null:e[n]}(e);let r=i?n.residualClasses:n.residualStyles;if(null===o)0===(i?n.classBindings:n.styleBindings)&&(t=bs(t=sh(null,e,n,t,i),n.attrs,i),r=null);else{const s=n.directiveStylingLast;if(-1===s||e[s]!==o)if(t=sh(o,e,n,t,i),null===r){let l=function ik(e,n,t){const i=t?n.classBindings:n.styleBindings;if(0!==ar(i))return e[qi(i)]}(e,n,i);void 0!==l&&Array.isArray(l)&&(l=sh(null,e,n,l[1],i),l=bs(l,n.attrs,i),function ok(e,n,t,i){e[qi(t?n.classBindings:n.styleBindings)]=i}(e,n,i,l))}else r=function rk(e,n,t){let i;const o=n.directiveEnd;for(let r=1+n.directiveStylingLast;r<o;r++)i=bs(i,e[r].hostAttrs,t);return bs(i,n.attrs,t)}(e,n,i)}return void 0!==r&&(i?n.residualClasses=r:n.residualStyles=r),t}(o,r,n,i),function GA(e,n,t,i,o,r){let s=r?n.classBindings:n.styleBindings,a=qi(s),l=ar(s);e[i]=t;let u,c=!1;if(Array.isArray(t)?(u=t[1],(null===u||Ir(t,u)>0)&&(c=!0)):u=t,o)if(0!==l){const p=qi(e[a+1]);e[i+1]=Pl(p,a),0!==p&&(e[p+1]=ih(e[p+1],i)),e[a+1]=function $A(e,n){return 131071&e|n<<17}(e[a+1],i)}else e[i+1]=Pl(a,0),0!==a&&(e[a+1]=ih(e[a+1],i)),a=i;else e[i+1]=Pl(l,0),0===a?a=i:e[l+1]=ih(e[l+1],i),l=i;c&&(e[i+1]=nh(e[i+1])),LC(e,u,i,!0),LC(e,u,i,!1),function qA(e,n,t,i,o){const r=o?e.residualClasses:e.residualStyles;null!=r&&"string"==typeof n&&Ir(r,n)>=0&&(t[i+1]=oh(t[i+1]))}(n,u,e,i,r),s=Pl(a,l),r?n.classBindings=s:n.styleBindings=s}(o,r,n,t,s,i)}}function sh(e,n,t,i,o){let r=null;const s=t.directiveEnd;let a=t.directiveStylingLast;for(-1===a?a=t.directiveStart:a++;a<s&&(r=n[a],i=bs(i,r.hostAttrs,o),r!==e);)a++;return null!==e&&(t.directiveStylingLast=a),i}function bs(e,n,t){const i=t?1:2;let o=-1;if(null!==n)for(let r=0;r<n.length;r++){const s=n[r];"number"==typeof s?o=s:o===i&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),Nt(e,s,!!t||n[++r]))}return void 0===e?null:e}function qC(e,n,t,i,o,r,s,a){if(!(3&n.type))return;const l=e.data,c=l[a+1],u=function zA(e){return!(1&~e)}(c)?WC(l,n,t,o,ar(c),s):void 0;Hl(u)||(Hl(r)||function UA(e){return!(2&~e)}(c)&&(r=WC(l,null,t,o,a,s)),function AS(e,n,t,i,o){if(n)o?e.addClass(t,i):e.removeClass(t,i);else{let r=-1===i.indexOf("-")?void 0:ii.DashCase;null==o?e.removeStyle(t,i,r):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),r|=ii.Important),e.setStyle(t,i,o,r))}}(i,s,wo(Je(),t),o,r))}function WC(e,n,t,i,o,r){const s=null===n;let a;for(;o>0;){const l=e[o],c=Array.isArray(l),u=c?l[1]:l,d=null===u;let p=t[o+1];p===Y&&(p=d?le:void 0);let h=d?uu(p,i):u===i?p:void 0;if(c&&!Hl(h)&&(h=uu(l,i)),Hl(h)&&(a=h,s))return a;const m=e[o+1];o=s?qi(m):ar(m)}if(null!==n){let l=r?n.residualClasses:n.residualStyles;null!=l&&(a=uu(l,i))}return a}function Hl(e){return void 0!==e}function ZC(e,n){return!!(e.flags&(n?8:16))}function Gt(e,n,t){!function sn(e,n,t,i){const o=G(),r=Rn(2);o.firstUpdatePass&&zC(o,null,r,i);const s=D();if(t!==Y&&Fe(s,r,t)){const a=o.data[Je()];if(ZC(a,i)&&!$C(o,r)){let l=i?a.classesWithoutHost:a.stylesWithoutHost;null!==l&&(t=Kc(l,t||"")),rh(o,a,s,t,i)}else!function lk(e,n,t,i,o,r,s,a){o===Y&&(o=le);let l=0,c=0,u=0<o.length?o[0]:null,d=0<r.length?r[0]:null;for(;null!==u||null!==d;){const p=l<o.length?o[l+1]:void 0,h=c<r.length?r[c+1]:void 0;let C,m=null;u===d?(l+=2,c+=2,p!==h&&(m=d,C=h)):null===d||null!==u&&u<d?(l+=2,m=u):(c+=2,m=d,C=h),null!==m&&qC(e,n,t,i,m,C,s,a),u=l<o.length?o[l]:null,d=c<r.length?r[c]:null}}(o,a,s,s[U],s[r+1],s[r+1]=function sk(e,n,t){if(null==t||""===t)return le;const i=[],o=ti(t);if(Array.isArray(o))for(let r=0;r<o.length;r++)e(i,o[r],!0);else if("object"==typeof o)for(const r in o)o.hasOwnProperty(r)&&e(i,r,o[r]);else"string"==typeof o&&n(i,o);return i}(e,n,t),i,r)}}(Nt,wn,Jo(D(),e,n,t),!0)}function y(e,n,t,i){const o=D(),r=G(),s=A+e,a=o[U],l=r.firstCreatePass?my(s,r,o,n,Yd,Au(),t,i):r.data[s],c=XC(r,o,l,a,n,e);o[s]=c;const u=Ia(l);return Jt(l,!0),p_(a,c,l),!oi(l)&&Ar()&&gl(r,o,c,l),(0===function r0(){return W.lFrame.elementDepthCount}()||u)&&Dt(c,o),function s0(){W.lFrame.elementDepthCount++}(),u&&(hl(r,o,l),Fd(r,l,o)),null!==i&&Qd(o,l),y}function _(){let e=ee();ku()?Fu():(e=e.parent,Jt(e,!1));const n=e;(function l0(e){return W.skipHydrationRootTNode===e})(n)&&function f0(){W.skipHydrationRootTNode=null}(),function a0(){W.lFrame.elementDepthCount--}();const t=G();return t.firstCreatePass&&vy(t,n),null!=n.classesWithoutHost&&function E0(e){return!!(8&e.flags)}(n)&&rh(t,n,D(),n.classesWithoutHost,!0),null!=n.stylesWithoutHost&&function M0(e){return!!(16&e.flags)}(n)&&rh(t,n,D(),n.stylesWithoutHost,!1),_}function N(e,n,t,i){return y(e,n,t,i),_(),N}let XC=(e,n,t,i,o,r)=>(hn(!0),ul(i,o,function tm(){return W.lFrame.currentNamespace}()));function K(e,n,t){const i=D(),o=G(),r=e+A,s=o.firstCreatePass?function kk(e,n,t,i,o){const r=n.consts,s=Bt(r,i),a=zo(n,e,8,"ng-container",s);null!==s&&If(a,s,!0);const l=Bt(r,o);return Au()&&Tf(n,t,a,l,Yd),a.mergedAttrs=Mo(a.mergedAttrs,a.attrs),null!==n.queries&&n.queries.elementStart(n,a),a}(r,o,i,n,t):o.data[r];Jt(s,!0);const a=eD(o,i,s,e);return i[r]=a,Ar()&&gl(o,i,a,s),Dt(a,i),Ia(s)&&(hl(o,i,s),Fd(o,s,i)),null!=t&&Qd(i,s),K}function X(){let e=ee();const n=G();return ku()?Fu():(e=e.parent,Jt(e,!1)),n.firstCreatePass&&(Bu(n,e),Du(e)&&n.queries.elementEnd(e)),X}let eD=(e,n,t,i)=>(hn(!0),zd(n[U],""));function me(){return D()}const jl="en-US";let rD=jl;function bD(e,n,t){return function i(o){if(o===Function)return t;rs(Xt(e)?lt(e.index,n):n,5);const s=n[8];let a=ED(n,s,t,o),l=i.__ngNextListenerFn__;for(;l;)a=ED(n,s,l,o)&&a,l=l.__ngNextListenerFn__;return a}}function ED(e,n,t,i){const o=J(null);try{return!1!==t(i)}catch(r){return function IF(e,n){const t=e[9],i=t?t.get(en,null):null;i&&i.handleError(n)}(e,r),!1}finally{J(o)}}function MD(e,n,t,i,o,r){const a=n[1],u=n[t][a.data[t].outputs[i]],d=a.firstCreatePass?xu(a):null,p=Su(n),h=u.subscribe(r),m=p.length;p.push(r,h),d&&d.push(o,e.index,m,-(m+1))}const fh=new Map;function z(e,n,t,i){const o=D(),r=G(),s=ee();return hh(r,o,o[U],s,e,n,i),z}function hh(e,n,t,i,o,r,s){const a=Ia(i),c=e.firstCreatePass?xu(e):null,u=Su(n);let d=!0;if(3&i.type||s){const p=at(i,n),h=s?s(p):p,m=u.length,C=s?M=>s(se(M[i.index])):i.index;let E=null;if(!s&&a&&(E=function xF(e,n,t,i){const o=e.cleanup;if(null!=o)for(let r=0;r<o.length-1;r+=2){const s=o[r];if(s===t&&o[r+1]===i){const a=n[7],l=o[r+2];return a.length>l?a[l]:null}"string"==typeof s&&(r+=2)}return null}(e,n,o,i.index)),null!==E)(E.__ngLastListenerFn__||E).__ngNextListenerFn__=r,E.__ngLastListenerFn__=r,d=!1;else{r=bD(i,n,r);const M=n[9].get(Pn);fh.get(M)?.(h,o,r);const $=t.listen(h,o,r);u.push(r,$),c&&c.push(o,C,m,m+1)}}else r=bD(i,n,r);if(d){const p=i.outputs?.[o],h=i.hostDirectiveOutputs?.[o];if(h&&h.length)for(let m=0;m<h.length;m+=2)MD(i,n,h[m],h[m+1],o,r);if(p&&p.length)for(const m of p)MD(i,n,m,o,o,r)}}function v(e=1){return function v0(e){return(W.lFrame.contextLView=function Ug(e,n){for(;e>0;)n=n[14],e--;return n}(e,W.lFrame.contextLView))[8]}(e)}function bn(e,n,t){return ph(e,"",n,"",t),bn}function ph(e,n,t,i,o){const r=D(),s=Jo(r,n,t,i);return s!==Y&&wt(G(),ve(),r,e,s,r[U],o,!1),ph}function RD(e,n,t,i){!function xy(e,n,t,i){const o=G();if(o.firstCreatePass){const r=ee();Ny(o,new My(n,t,i),r.index),function IN(e,n){const t=e.contentQueries||(e.contentQueries=[]);n!==(t.length?t[t.length-1]:-1)&&t.push(e.queries.length-1,n)}(o,e),!(2&~t)&&(o.staticContentQueries=!0)}return Ty(o,D(),t)}(e,n,t,i)}function Lt(e,n,t){!function Sy(e,n,t){const i=G();return i.firstCreatePass&&(Ny(i,new My(e,n,t),-1),!(2&~n)&&(i.staticViewQueries=!0)),Ty(i,D(),n)}(e,n,t)}function Mt(e){const n=D(),t=G(),i=Pu();Oa(i+1);const o=Lf(t,i);if(e.dirty&&function t0(e){return!(4&~e[2])}(n)===!(2&~o.metadata.flags)){if(null===o.matches)e.reset([]);else{const r=Oy(n,i);e.reset(r,km),e.notifyOnChanges()}return!0}return!1}function It(){return function Rf(e,n){return e[18].queries[n].queryList}(D(),Pu())}function w(e,n=""){const t=D(),i=G(),o=e+A,r=i.firstCreatePass?zo(i,o,1,n,null):i.data[o],s=GD(i,t,r,n,e);t[o]=s,Ar()&&gl(i,t,s,r),Jt(r,!1)}let GD=(e,n,t,i,o)=>(hn(!0),function $d(e,n){return e.createText(n)}(n[U],i));function O(e){return j("",e,""),O}function j(e,n,t){const i=D(),o=Jo(i,e,n,t);return o!==Y&&function $n(e,n,t){const i=wo(n,e);!function u_(e,n,t){e.setValue(n,t)}(e[U],i,t)}(i,Je(),o),j}function Ze(e,n,t){Rm(n)&&(n=n());const i=D();return Fe(i,jt(),n)&&wt(G(),ve(),i,e,n,i[U],t,!1),Ze}function be(e,n){const t=Rm(e);return t&&e.set(n),t}function Ke(e,n){const t=D(),i=G(),o=ee();return hh(i,t,t[U],o,e,n),Ke}function mh(e,n,t,i,o){if(e=q(e),Array.isArray(e))for(let r=0;r<e.length;r++)mh(e[r],n,t,i,o);else{const r=G(),s=D(),a=ee();let l=Ii(e)?e:q(e.provide);const c=Mg(e),u=1048575&a.providerIndexes,d=a.directiveStart,p=a.providerIndexes>>20;if(Ii(e)||!e.multi){const h=new kr(c,o,T),m=_h(l,n,o?u:u+p,d);-1===m?(Wu(Ra(a,s),r,l),vh(r,e,n.length),n.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(h),s.push(h)):(t[m]=h,s[m]=h)}else{const h=_h(l,n,u+p,d),m=_h(l,n,u,u+p),E=m>=0&&t[m];if(o&&!E||!o&&!(h>=0&&t[h])){Wu(Ra(a,s),r,l);const M=function tR(e,n,t,i,o){const r=new kr(e,t,T);return r.multi=[],r.index=n,r.componentProviders=0,nw(r,o,i&&!t),r}(o?eR:JF,t.length,o,i,c);!o&&E&&(t[m].providerFactory=M),vh(r,e,n.length,0),n.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(M),s.push(M)}else vh(r,e,h>-1?h:m,nw(t[o?m:h],c,!o&&i));!o&&i&&E&&t[m].componentProviders++}}}function vh(e,n,t,i){const o=Ii(n),r=function AI(e){return!!e.useClass}(n);if(o||r){const l=(r?q(n.useClass):n).prototype.ngOnDestroy;if(l){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){const u=c.indexOf(t);-1===u?c.push(t,[i,l]):c[u+1].push(i,l)}else c.push(t,l)}}}function nw(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function _h(e,n,t,i){for(let o=t;o<i;o++)if(n[o]===e)return o;return-1}function JF(e,n,t,i){return yh(this.multi,[])}function eR(e,n,t,i){const o=this.multi;let r;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=Lr(t,t[1],this.providerFactory.index,i);r=a.slice(0,s),yh(o,r);for(let l=s;l<a.length;l++)r.push(a[l])}else r=[],yh(o,r);return r}function yh(e,n){for(let t=0;t<e.length;t++)n.push((0,e[t])());return n}function Me(e,n=[]){return t=>{t.providersResolver=(i,o)=>function XF(e,n,t){const i=G();if(i.firstCreatePass){const o=Ft(e);mh(t,i.data,i.blueprint,o,!0),mh(n,i.data,i.blueprint,o,!1)}}(i,o?o(e):e,n)}}function ur(e,n,t,i){return function ow(e,n,t,i,o,r){const s=n+t;return Fe(e,s,o)?Cn(e,s+1,r?i.call(r,o):i(o)):xs(e,s+1)}(D(),ct(),e,n,t,i)}function Ch(e,n,t,i,o){return function rw(e,n,t,i,o,r,s){const a=n+t;return zi(e,a,o,r)?Cn(e,a+2,s?i.call(s,o,r):i(o,r)):xs(e,a+2)}(D(),ct(),e,n,t,i,o)}function Le(e,n,t,i,o,r){return sw(D(),ct(),e,n,t,i,o,r)}function xs(e,n){const t=e[n];return t===Y?void 0:t}function sw(e,n,t,i,o,r,s,a){const l=n+t;return function xl(e,n,t,i,o){const r=zi(e,n,t,i);return Fe(e,n+2,o)||r}(e,l,o,r,s)?Cn(e,l+3,a?i.call(a,o,r,s):i(o,r,s)):xs(e,l+3)}let WR=(()=>{class e{zone=k(he);changeDetectionScheduler=k(Ln);applicationRef=k(zt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Mh({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new he({...Ih(),scheduleInRootZone:t}),[{provide:he,useFactory:e},{provide:Qt,multi:!0,useFactory:()=>{const i=k(WR,{optional:!0});return()=>i.initialize()}},{provide:Qt,multi:!0,useFactory:()=>{const i=k(QR);return()=>{i.initialize()}}},!0===n?{provide:Im,useValue:!0}:[],{provide:Yu,useValue:t??Em}]}function Ih(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}let QR=(()=>{class e{subscription=new Tt;initialized=!1;zone=k(he);pendingTasks=k(Li);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{he.assertNotInAngularZone(),queueMicrotask(()=>{null!==t&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{he.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),As=(()=>{class e{appRef=k(zt);taskService=k(Li);ngZone=k(he);zonelessEnabled=k(Pr);tracing=k(So,{optional:!0});disableScheduling=k(Im,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new Tt;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ba):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(k(Yu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ed||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&5===t)return;let i=!1;switch(t){case 0:this.appRef.dirtyFlags|=2;break;case 3:case 2:case 4:case 5:case 1:this.appRef.dirtyFlags|=4;break;case 6:case 13:this.appRef.dirtyFlags|=2,i=!0;break;case 12:this.appRef.dirtyFlags|=16,i=!0;break;case 11:i=!0;break;default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(i))return;const o=this.useMicrotaskScheduler?Sm:Tm;this.pendingRenderTaskId=this.taskService.add(),this.cancelScheduledCallback=this.scheduleInRootZone?Zone.root.run(()=>o(()=>this.tick())):this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||null!==this.pendingRenderTaskId||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ba+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(0===this.appRef.dirtyFlags)return void this.cleanup();!this.zonelessEnabled&&7&this.appRef.dirtyFlags&&(this.appRef.dirtyFlags|=1);const t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(i){throw this.taskService.remove(t),i}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Sm(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,null!==this.pendingRenderTaskId){const t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const ui=new R("",{providedIn:"root",factory:()=>k(ui,ie.Optional|ie.SkipSelf)||function YR(){return typeof $localize<"u"&&$localize.locale||jl}()}),Zl=new R(""),nL=new R("");function ks(e){return!e.moduleRef}let Fw=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(t){this._injector=t}bootstrapModuleFactory(t,i){const o=i?.scheduleInRootZone,s=i?.ignoreChangesOutsideZone,a=[Mh({ngZoneFactory:()=>function X0(e="zone.js",n){return"noop"===e?new ed:"zone.js"===e?new he(n):e}(i?.ngZone,{...Ih({eventCoalescing:i?.ngZoneEventCoalescing,runCoalescing:i?.ngZoneRunCoalescing}),scheduleInRootZone:o}),ignoreChangesOutsideZone:s}),{provide:Ln,useExisting:As}],l=function VN(e,n,t){return new Hf(e,n,t,!1)}(t.moduleType,this.injector,a);return function kw(e){const n=ks(e)?e.r3Injector:e.moduleRef.injector,t=n.get(he);return t.run(()=>{ks(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();const i=n.get(en,null);let o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r=>{i.handleError(r)}})}),ks(e)){const r=()=>n.destroy(),s=e.platformInjector.get(Zl);s.add(r),n.onDestroy(()=>{o.unsubscribe(),s.delete(r)})}else{const r=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Zl);s.add(r),e.moduleRef.onDestroy(()=>{Ll(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(r)})}return function oL(e,n,t){try{const i=t();return Fl(i)?i.catch(o=>{throw n.runOutsideAngular(()=>e.handleError(o)),o}):i}catch(i){throw n.runOutsideAngular(()=>e.handleError(i)),i}}(i,t,()=>{const r=n.get(mC);return r.runInitializers(),r.donePromise.then(()=>{if(function Uk(e){"string"==typeof e&&(rD=e.toLowerCase().replace(/_/g,"-"))}(n.get(ui,jl)||jl),!n.get(nL,!0))return ks(e)?n.get(zt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(ks(e)){const l=n.get(zt);return void 0!==e.rootComponent&&l.bootstrap(e.rootComponent),l}return function iL(e,n){const t=e.injector.get(zt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(i=>t.bootstrap(i));else{if(!e.instance.ngDoBootstrap)throw new x(-403,!1);e.instance.ngDoBootstrap(t)}n.push(e)}(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}({moduleRef:l,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(t,i=[]){const o=_C({},i);return function qR(e,n,t){const i=new Bf(t);return Promise.resolve(i)}(0,0,t).then(r=>this.bootstrapModuleFactory(r,o))}onDestroy(t){this._destroyListeners.push(t)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new x(404,!1);this._modules.slice().forEach(i=>i.destroy()),this._destroyListeners.forEach(i=>i());const t=this._injector.get(Zl,null);t&&(t.forEach(i=>i()),t.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(i){return new(i||e)(re(et))};static \u0275prov=te({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),di=null;const Rw=new R("");function Lw(e,n,t=[]){const i=`Platform: ${n}`,o=new R(i);return(r=[])=>{let s=Sh();if(!s||s.injector.get(Rw,!1)){const a=[...t,...r,{provide:o,useValue:!0}];e?e(a):function rL(e){if(di&&!di.get(Rw,!1))throw new x(400,!1);(function vC(){!function HM(e){Up=e}(()=>{throw new x(600,!1)})})(),di=e;const n=e.get(Fw);(function Vw(e){const n=e.get(ad,null);Ig(e,()=>{n?.forEach(t=>t())})})(e)}(function Pw(e=[],n){return et.create({name:n,providers:[{provide:gu,useValue:"platform"},{provide:Zl,useValue:new Set([()=>di=null])},...e]})}(a,i))}return function sL(){const n=Sh();if(!n)throw new x(401,!1);return n}()}}function Sh(){return di?.get(Fw)??null}let Zi=(()=>class e{static __NG_ELEMENT_ID__=lL})();function lL(e){return function cL(e,n,t){if(Xt(e)&&!t){const i=lt(e.index,n);return new as(i,i)}return 175&e.type?new as(n[15],n):null}(ee(),D(),!(16&~e))}class $w{constructor(){}supports(n){return Sl(n)}create(n){return new pL(n)}}const hL=(e,n)=>n;class pL{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||hL}forEachItem(n){let t;for(t=this._itHead;null!==t;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,i=this._removalsHead,o=0,r=null;for(;t||i;){const s=!i||t&&t.currentIndex<Gw(i,o,r)?t:i,a=Gw(s,o,r),l=s.currentIndex;if(s===i)o--,i=i._nextRemoved;else if(t=t._next,null==s.previousIndex)o++;else{r||(r=[]);const c=a-o,u=l-o;if(c!=u){for(let p=0;p<c;p++){const h=p<r.length?r[p]:r[p]=0,m=h+p;u<=m&&m<c&&(r[p]=h+1)}r[s.previousIndex]=u-c}}a!==l&&n(s,a,l)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;null!==t;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;null!==t;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;null!==t;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;null!==t;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;null!==t;t=t._nextIdentityChange)n(t)}diff(n){if(null==n&&(n=[]),!Sl(n))throw new x(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let o,r,s,t=this._itHead,i=!1;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)r=n[a],s=this._trackByFn(a,r),null!==t&&Object.is(t.trackById,s)?(i&&(t=this._verifyReinsertion(t,r,s,a)),Object.is(t.item,r)||this._addIdentityChange(t,r)):(t=this._mismatch(t,r,s,a),i=!0),t=t._next}else o=0,function iO(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{const t=e[Symbol.iterator]();let i;for(;!(i=t.next()).done;)n(i.value)}}(n,a=>{s=this._trackByFn(o,a),null!==t&&Object.is(t.trackById,s)?(i&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)):(t=this._mismatch(t,a,s,o),i=!0),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;null!==n;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;null!==n;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;null!==n;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,i,o){let r;return null===n?r=this._itTail:(r=n._prev,this._remove(n)),null!==(n=null===this._unlinkedRecords?null:this._unlinkedRecords.get(i,null))?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,r,o)):null!==(n=null===this._linkedRecords?null:this._linkedRecords.get(i,o))?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,r,o)):n=this._addAfter(new gL(t,i),r,o),n}_verifyReinsertion(n,t,i,o){let r=null===this._unlinkedRecords?null:this._unlinkedRecords.get(i,null);return null!==r?n=this._reinsertAfter(r,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;null!==n;){const t=n._next;this._addToRemovals(this._unlink(n)),n=t}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,i){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(n);const o=n._prevRemoved,r=n._nextRemoved;return null===o?this._removalsHead=r:o._nextRemoved=r,null===r?this._removalsTail=o:r._prevRemoved=o,this._insertAfter(n,t,i),this._addToMoves(n,i),n}_moveAfter(n,t,i){return this._unlink(n),this._insertAfter(n,t,i),this._addToMoves(n,i),n}_addAfter(n,t,i){return this._insertAfter(n,t,i),this._additionsTail=null===this._additionsTail?this._additionsHead=n:this._additionsTail._nextAdded=n,n}_insertAfter(n,t,i){const o=null===t?this._itHead:t._next;return n._next=o,n._prev=t,null===o?this._itTail=n:o._prev=n,null===t?this._itHead=n:t._next=n,null===this._linkedRecords&&(this._linkedRecords=new zw),this._linkedRecords.put(n),n.currentIndex=i,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){null!==this._linkedRecords&&this._linkedRecords.remove(n);const t=n._prev,i=n._next;return null===t?this._itHead=i:t._next=i,null===i?this._itTail=t:i._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail=null===this._movesTail?this._movesHead=n:this._movesTail._nextMoved=n),n}_addToRemovals(n){return null===this._unlinkedRecords&&(this._unlinkedRecords=new zw),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=n:this._identityChangesTail._nextIdentityChange=n,n}}class gL{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}}class mL{_head=null;_tail=null;add(n){null===this._head?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let i;for(i=this._head;null!==i;i=i._nextDup)if((null===t||t<=i.currentIndex)&&Object.is(i.trackById,n))return i;return null}remove(n){const t=n._prevDup,i=n._nextDup;return null===t?this._head=i:t._nextDup=i,null===i?this._tail=t:i._prevDup=t,null===this._head}}class zw{map=new Map;put(n){const t=n.trackById;let i=this.map.get(t);i||(i=new mL,this.map.set(t,i)),i.add(n)}get(n,t){const o=this.map.get(n);return o?o.get(n,t):null}remove(n){const t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function Gw(e,n,t){const i=e.previousIndex;if(null===i)return i;let o=0;return t&&i<t.length&&(o=t[i]),i+n+o}class qw{constructor(){}supports(n){return n instanceof Map||$f(n)}create(){return new vL}}class vL{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(n){let t;for(t=this._mapHead;null!==t;t=t._next)n(t)}forEachPreviousItem(n){let t;for(t=this._previousMapHead;null!==t;t=t._nextPrevious)n(t)}forEachChangedItem(n){let t;for(t=this._changesHead;null!==t;t=t._nextChanged)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;null!==t;t=t._nextAdded)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;null!==t;t=t._nextRemoved)n(t)}diff(n){if(n){if(!(n instanceof Map||$f(n)))throw new x(900,!1)}else n=new Map;return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._mapHead;if(this._appendAfter=null,this._forEach(n,(i,o)=>{if(t&&t.key===o)this._maybeAddToChanges(t,i),this._appendAfter=t,t=t._next;else{const r=this._getOrCreateRecordForKey(o,i);t=this._insertBeforeOrAppend(t,r)}}),t){t._prev&&(t._prev._next=null),this._removalsHead=t;for(let i=t;null!==i;i=i._nextRemoved)i===this._mapHead&&(this._mapHead=null),this._records.delete(i.key),i._nextRemoved=i._next,i.previousValue=i.currentValue,i.currentValue=null,i._prev=null,i._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(n,t){if(n){const i=n._prev;return t._next=n,t._prev=i,n._prev=t,i&&(i._next=t),n===this._mapHead&&(this._mapHead=t),this._appendAfter=n,n}return this._appendAfter?(this._appendAfter._next=t,t._prev=this._appendAfter):this._mapHead=t,this._appendAfter=t,null}_getOrCreateRecordForKey(n,t){if(this._records.has(n)){const o=this._records.get(n);this._maybeAddToChanges(o,t);const r=o._prev,s=o._next;return r&&(r._next=s),s&&(s._prev=r),o._next=null,o._prev=null,o}const i=new _L(n);return this._records.set(n,i),i.currentValue=t,this._addToAdditions(i),i}_reset(){if(this.isDirty){let n;for(this._previousMapHead=this._mapHead,n=this._previousMapHead;null!==n;n=n._next)n._nextPrevious=n._next;for(n=this._changesHead;null!==n;n=n._nextChanged)n.previousValue=n.currentValue;for(n=this._additionsHead;null!=n;n=n._nextAdded)n.previousValue=n.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(n,t){Object.is(t,n.currentValue)||(n.previousValue=n.currentValue,n.currentValue=t,this._addToChanges(n))}_addToAdditions(n){null===this._additionsHead?this._additionsHead=this._additionsTail=n:(this._additionsTail._nextAdded=n,this._additionsTail=n)}_addToChanges(n){null===this._changesHead?this._changesHead=this._changesTail=n:(this._changesTail._nextChanged=n,this._changesTail=n)}_forEach(n,t){n instanceof Map?n.forEach(t):Object.keys(n).forEach(i=>t(n[i],i))}}class _L{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(n){this.key=n}}function Ww(){return new Ah([new $w])}let Ah=(()=>{class e{factories;static \u0275prov=te({token:e,providedIn:"root",factory:Ww});constructor(t){this.factories=t}static create(t,i){if(null!=i){const o=i.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:i=>e.create(t,i||Ww()),deps:[[e,new cu,new lu]]}}find(t){const i=this.factories.find(o=>o.supports(t));if(null!=i)return i;throw new x(901,!1)}}return e})();function Zw(){return new Kl([new qw])}let Kl=(()=>{class e{static \u0275prov=te({token:e,providedIn:"root",factory:Zw});factories;constructor(t){this.factories=t}static create(t,i){if(i){const o=i.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:i=>e.create(t,i||Zw()),deps:[[e,new cu,new lu]]}}find(t){const i=this.factories.find(o=>o.supports(t));if(i)return i;throw new x(901,!1)}}return e})();const DL=Lw(null,"core",[]);let wL=(()=>{class e{constructor(t){}static \u0275fac=function(i){return new(i||e)(re(zt))};static \u0275mod=si({type:e});static \u0275inj=Tn({})}return e})();function je(e){return function GM(e){const n=J(null);try{return e()}finally{J(n)}}(e)}function zn(e,n){return function LM(e,n){const t=Object.create(PM);t.computation=e,void 0!==n&&(t.equal=n);const i=()=>{if(Dr(t),Qs(t),t.value===Zn)throw t.error;return t.value};return i[tt]=t,i}(e,n?.equal)}let pb=null;function Rs(){return pb}class dP{}function mb(e){return"server"===e}const Yi=new R(""),qh=/\s+/,Mb=[];let hr=(()=>{class e{_ngEl;_renderer;initialClasses=Mb;rawClass;stateMap=new Map;constructor(t,i){this._ngEl=t,this._renderer=i}set klass(t){this.initialClasses=null!=t?t.trim().split(qh):Mb}set ngClass(t){this.rawClass="string"==typeof t?t.trim().split(qh):t}ngDoCheck(){for(const i of this.initialClasses)this._updateState(i,!0);const t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(const i of t)this._updateState(i,!0);else if(null!=t)for(const i of Object.keys(t))this._updateState(i,!!t[i]);this._applyStateDiff()}_updateState(t,i){const o=this.stateMap.get(t);void 0!==o?(o.enabled!==i&&(o.changed=!0,o.enabled=i),o.touched=!0):this.stateMap.set(t,{enabled:i,changed:!0,touched:!0})}_applyStateDiff(){for(const t of this.stateMap){const i=t[0],o=t[1];o.changed?(this._toggleClass(i,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(i,!1),this.stateMap.delete(i)),o.touched=!1}}_toggleClass(t,i){(t=t.trim()).length>0&&t.split(qh).forEach(o=>{i?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(i){return new(i||e)(T(ut),T(nn))};static \u0275dir=Z({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();class tV{$implicit;ngForOf;index;count;constructor(n,t,i,o){this.$implicit=n,this.ngForOf=t,this.index=i,this.count=o}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let Ki=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,i,o){this._viewContainer=t,this._template=i,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){const t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){const i=this._viewContainer;t.forEachOperation((o,r,s)=>{if(null==o.previousIndex)i.createEmbeddedView(this._template,new tV(o.item,this._ngForOf,-1,-1),null===s?void 0:s);else if(null==s)i.remove(null===r?void 0:r);else if(null!==r){const a=i.get(r);i.move(a,s),Tb(a,o)}});for(let o=0,r=i.length;o<r;o++){const a=i.get(o).context;a.index=o,a.count=r,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{Tb(i.get(o.currentIndex),o)})}static ngTemplateContextGuard(t,i){return!0}static \u0275fac=function(i){return new(i||e)(T(yn),T(jn),T(Ah))};static \u0275dir=Z({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Tb(e,n){e.context.$implicit=n.item}let qn=(()=>{class e{_viewContainer;_context=new nV;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,i){this._viewContainer=t,this._thenTemplateRef=i}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Sb(t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Sb(t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,i){return!0}static \u0275fac=function(i){return new(i||e)(T(yn),T(jn))};static \u0275dir=Z({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})();class nV{$implicit=null;ngIf=null}function Sb(e,n){if(e&&!e.createEmbeddedView)throw new x(2020,!1)}let Nb=(()=>{class e{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(t,i,o){this._ngEl=t,this._differs=i,this._renderer=o}set ngStyle(t){this._ngStyle=t,!this._differ&&t&&(this._differ=this._differs.find(t).create())}ngDoCheck(){if(this._differ){const t=this._differ.diff(this._ngStyle);t&&this._applyChanges(t)}}_setStyle(t,i){const[o,r]=t.split("."),s=-1===o.indexOf("-")?void 0:ii.DashCase;null!=i?this._renderer.setStyle(this._ngEl.nativeElement,o,r?`${i}${r}`:i,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(t){t.forEachRemovedItem(i=>this._setStyle(i.key,null)),t.forEachAddedItem(i=>this._setStyle(i.key,i.currentValue)),t.forEachChangedItem(i=>this._setStyle(i.key,i.currentValue))}static \u0275fac=function(i){return new(i||e)(T(ut),T(Kl),T(nn))};static \u0275dir=Z({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return e})(),Ob=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){const i=this._viewContainerRef;if(this._viewRef&&i.remove(i.indexOf(this._viewRef)),!this.ngTemplateOutlet)return void(this._viewRef=null);const o=this._createContextForwardProxy();this._viewRef=i.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,i,o)=>!!this.ngTemplateOutletContext&&Reflect.set(this.ngTemplateOutletContext,i,o),get:(t,i,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,i,o)}})}static \u0275fac=function(i){return new(i||e)(T(yn))};static \u0275dir=Z({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[An]})}return e})();let kb=(()=>{class e{transform(t,i,o){if(null==t)return null;if("string"!=typeof t&&!Array.isArray(t))throw function ln(e,n){return new x(2100,!1)}();return t.slice(i,o)}static \u0275fac=function(i){return new(i||e)};static \u0275pipe=bt({name:"slice",type:e,pure:!1})}return e})(),Fb=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=si({type:e});static \u0275inj=Tn({})}return e})();const Kh=new R("");let Rb=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,i){this._zone=i,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,i,o,r){return this._findPluginFor(i).addEventListener(t,i,o,r)}getZone(){return this._zone}_findPluginFor(t){let i=this._eventNameToPlugin.get(t);if(i)return i;if(i=this._plugins.find(r=>r.supports(t)),!i)throw new x(5101,!1);return this._eventNameToPlugin.set(t,i),i}static \u0275fac=function(i){return new(i||e)(re(Kh),re(he))};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})();class Lb{_doc;constructor(n){this._doc=n}manager}const hc="ng-app-id";function Pb(e){for(const n of e)n.remove()}function Vb(e,n){const t=n.createElement("style");return t.textContent=e,t}function Xh(e,n){const t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}let Hb=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(t,i,o,r={}){this.doc=t,this.appId=i,this.nonce=o,this.isServer=mb(r),function SV(e,n,t,i){const o=e.head?.querySelectorAll(`style[${hc}="${n}"],link[${hc}="${n}"]`);if(o)for(const r of o)r.removeAttribute(hc),r instanceof HTMLLinkElement?i.set(r.href.slice(r.href.lastIndexOf("/")+1),{usage:0,elements:[r]}):r.textContent&&t.set(r.textContent,{usage:0,elements:[r]})}(t,i,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,i){for(const o of t)this.addUsage(o,this.inline,Vb);i?.forEach(o=>this.addUsage(o,this.external,Xh))}removeStyles(t,i){for(const o of t)this.removeUsage(o,this.inline);i?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,i,o){const r=i.get(t);r?r.usage++:i.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,i){const o=i.get(t);o&&(o.usage--,o.usage<=0&&(Pb(o.elements),i.delete(t)))}ngOnDestroy(){for(const[,{elements:t}]of[...this.inline,...this.external])Pb(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(const[i,{elements:o}]of this.inline)o.push(this.addElement(t,Vb(i,this.doc)));for(const[i,{elements:o}]of this.external)o.push(this.addElement(t,Xh(i,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,i){return this.nonce&&i.setAttribute("nonce",this.nonce),this.isServer&&i.setAttribute(hc,this.appId),t.appendChild(i)}static \u0275fac=function(i){return new(i||e)(re(Yi),re(Pn),re(Jm,8),re(ld))};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})();const Jh={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},ep=/%COMP%/g,FV=new R("",{providedIn:"root",factory:()=>!0});function jb(e,n){return n.map(t=>t.replace(ep,e))}let Ub=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,i,o,r,s,a,l,c=null,u=null){this.eventManager=t,this.sharedStylesHost=i,this.appId=o,this.removeStylesOnCompDestroy=r,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.tracingService=u,this.platformIsServer=mb(a),this.defaultRenderer=new tp(t,s,l,this.platformIsServer,this.tracingService)}createRenderer(t,i){if(!t||!i)return this.defaultRenderer;this.platformIsServer&&i.encapsulation===Ut.ShadowDom&&(i={...i,encapsulation:Ut.Emulated});const o=this.getOrCreateRenderer(t,i);return o instanceof zb?o.applyToHost(t):o instanceof np&&o.applyStyles(),o}getOrCreateRenderer(t,i){const o=this.rendererByCompId;let r=o.get(i.id);if(!r){const s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(i.encapsulation){case Ut.Emulated:r=new zb(l,c,i,this.appId,u,s,a,d,p);break;case Ut.ShadowDom:return new VV(l,c,t,i,s,a,this.nonce,d,p);default:r=new np(l,c,i,u,s,a,d,p)}o.set(i.id,r)}return r}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(i){return new(i||e)(re(Rb),re(Hb),re(Pn),re(FV),re(Yi),re(ld),re(he),re(Jm),re(So,8))};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})();class tp{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,i,o,r){this.eventManager=n,this.doc=t,this.ngZone=i,this.platformIsServer=o,this.tracingService=r}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(Jh[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){($b(n)?n.content:n).appendChild(t)}insertBefore(n,t,i){n&&($b(n)?n.content:n).insertBefore(t,i)}removeChild(n,t){t.remove()}selectRootElement(n,t){let i="string"==typeof n?this.doc.querySelector(n):n;if(!i)throw new x(-5104,!1);return t||(i.textContent=""),i}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,i,o){if(o){t=o+":"+t;const r=Jh[o];r?n.setAttributeNS(r,t,i):n.setAttribute(t,i)}else n.setAttribute(t,i)}removeAttribute(n,t,i){if(i){const o=Jh[i];o?n.removeAttributeNS(o,t):n.removeAttribute(`${i}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,i,o){o&(ii.DashCase|ii.Important)?n.style.setProperty(t,i,o&ii.Important?"important":""):n.style[t]=i}removeStyle(n,t,i){i&ii.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,i){null!=n&&(n[t]=i)}setValue(n,t){n.nodeValue=t}listen(n,t,i,o){if("string"==typeof n&&!(n=Rs().getGlobalEventTarget(this.doc,n)))throw new x(5102,!1);let r=this.decoratePreventDefault(i);return this.tracingService?.wrapEventListener&&(r=this.tracingService.wrapEventListener(n,t,r)),this.eventManager.addEventListener(n,t,r,o)}decoratePreventDefault(n){return t=>{if("__ngUnwrap__"===t)return n;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))&&t.preventDefault()}}}function $b(e){return"TEMPLATE"===e.tagName&&void 0!==e.content}class VV extends tp{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,i,o,r,s,a,l,c){super(n,r,s,l,c),this.sharedStylesHost=t,this.hostEl=i,this.shadowRoot=i.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=jb(o.id,u);for(const p of u){const h=document.createElement("style");a&&h.setAttribute("nonce",a),h.textContent=p,this.shadowRoot.appendChild(h)}const d=o.getExternalStyles?.();if(d)for(const p of d){const h=Xh(p,r);a&&h.setAttribute("nonce",a),this.shadowRoot.appendChild(h)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,i){return super.insertBefore(this.nodeOrShadowRoot(n),t,i)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class np extends tp{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,i,o,r,s,a,l,c){super(n,r,s,a,l),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let u=i.styles;this.styles=c?jb(c,u):u,this.styleUrls=i.getExternalStyles?.(c)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}}class zb extends np{contentAttr;hostAttr;constructor(n,t,i,o,r,s,a,l,c){const u=o+"-"+i.id;super(n,t,i,r,s,a,l,c,u),this.contentAttr=function RV(e){return"_ngcontent-%COMP%".replace(ep,e)}(u),this.hostAttr=function LV(e){return"_nghost-%COMP%".replace(ep,e)}(u)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){const i=super.createElement(n,t);return super.setAttribute(i,this.contentAttr,""),i}}class ip extends dP{supportsDOMEvents=!0;static makeCurrent(){!function uP(e){pb??=e}(new ip)}onAndCancel(n,t,i,o){return n.addEventListener(t,i,o),()=>{n.removeEventListener(t,i,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return(t=t||this.getDefaultDocument()).createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return"window"===t?window:"document"===t?n:"body"===t?n.body:null}getBaseHref(n){const t=function HV(){return Ps=Ps||document.querySelector("base"),Ps?Ps.getAttribute("href"):null}();return null==t?null:function BV(e){return new URL(e,document.baseURI).pathname}(t)}resetBaseElement(){Ps=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return function pP(e,n){n=encodeURIComponent(n);for(const t of e.split(";")){const i=t.indexOf("="),[o,r]=-1==i?[t,""]:[t.slice(0,i),t.slice(i+1)];if(o.trim()===n)return decodeURIComponent(r)}return null}(document.cookie,n)}}let Ps=null,UV=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(i){return new(i||e)};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})(),$V=(()=>{class e extends Lb{constructor(t){super(t)}supports(t){return!0}addEventListener(t,i,o,r){return t.addEventListener(i,o,r),()=>this.removeEventListener(t,i,o,r)}removeEventListener(t,i,o,r){return t.removeEventListener(i,o,r)}static \u0275fac=function(i){return new(i||e)(re(Yi))};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})();const Gb=["alt","control","meta","shift"],zV={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},GV={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey};let qV=(()=>{class e extends Lb{constructor(t){super(t)}supports(t){return null!=e.parseEventName(t)}addEventListener(t,i,o,r){const s=e.parseEventName(i),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Rs().onAndCancel(t,s.domEventName,a,r))}static parseEventName(t){const i=t.toLowerCase().split("."),o=i.shift();if(0===i.length||"keydown"!==o&&"keyup"!==o)return null;const r=e._normalizeKey(i.pop());let s="",a=i.indexOf("code");if(a>-1&&(i.splice(a,1),s="code."),Gb.forEach(c=>{const u=i.indexOf(c);u>-1&&(i.splice(u,1),s+=c+".")}),s+=r,0!=i.length||0===r.length)return null;const l={};return l.domEventName=o,l.fullKey=s,l}static matchEventFullKeyCode(t,i){let o=zV[t.key]||t.key,r="";return i.indexOf("code.")>-1&&(o=t.code,r="code."),!(null==o||!o)&&(o=o.toLowerCase()," "===o?o="space":"."===o&&(o="dot"),Gb.forEach(s=>{s!==o&&(0,GV[s])(t)&&(r+=s+".")}),r+=o,r===i)}static eventCallback(t,i,o){return r=>{e.matchEventFullKeyCode(r,t)&&o.runGuarded(()=>i(r))}}static _normalizeKey(t){return"esc"===t?"escape":t}static \u0275fac=function(i){return new(i||e)(re(Yi))};static \u0275prov=te({token:e,factory:e.\u0275fac})}return e})();const YV=Lw(DL,"browser",[{provide:ld,useValue:"browser"},{provide:ad,useValue:function WV(){ip.makeCurrent()},multi:!0},{provide:Yi,useFactory:function QV(){return function ET(e){sd=e}(document),document}}]),Zb=[{provide:kl,useClass:class jV{addToWindow(n){Oe.getAngularTestability=(i,o=!0)=>{const r=n.findTestabilityInTree(i,o);if(null==r)throw new x(5103,!1);return r},Oe.getAllAngularTestabilities=()=>n.getAllTestabilities(),Oe.getAllAngularRootElements=()=>n.getAllRootElements(),Oe.frameworkStabilizers||(Oe.frameworkStabilizers=[]),Oe.frameworkStabilizers.push(i=>{const o=Oe.getAllAngularTestabilities();let r=o.length;const s=function(){r--,0==r&&i()};o.forEach(a=>{a.whenStable(s)})})}findTestabilityInTree(n,t,i){return null==t?null:n.getTestability(t)??(i?Rs().isShadowRoot(t)?this.findTestabilityInTree(n,t.host,!0):this.findTestabilityInTree(n,t.parentElement,!0):null)}}},{provide:fC,useClass:Qf,deps:[he,Yf,kl]},{provide:Qf,useClass:Qf,deps:[he,Yf,kl]}],Qb=[{provide:gu,useValue:"root"},{provide:en,useFactory:function ZV(){return new en}},{provide:Kh,useClass:$V,multi:!0,deps:[Yi]},{provide:Kh,useClass:qV,multi:!0,deps:[Yi]},Ub,Hb,Rb,{provide:bf,useExisting:Ub},{provide:class mP{},useClass:UV},[]];let KV=(()=>{class e{constructor(){}static \u0275fac=function(i){return new(i||e)};static \u0275mod=si({type:e});static \u0275inj=Tn({providers:[...Qb,...Zb],imports:[Fb,wL]})}return e})();function fi(e){return this instanceof fi?(this.v=e,this):new fi(e)}function Jb(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=function ap(e){var n="function"==typeof Symbol&&Symbol.iterator,t=n&&e[n],i=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),t={},i("next"),i("throw"),i("return"),t[Symbol.asyncIterator]=function(){return this},t);function i(r){t[r]=e[r]&&function(s){return new Promise(function(a,l){!function o(r,s,a,l){Promise.resolve(l).then(function(c){r({value:c,done:a})},s)}(a,l,(s=e[r](s)).done,s.value)})}}}"function"==typeof SuppressedError&&SuppressedError;const eE=e=>e&&"number"==typeof e.length&&"function"!=typeof e;function tE(e){return Be(e?.then)}function nE(e){return Be(e[Wc])}function iE(e){return Symbol.asyncIterator&&Be(e?.[Symbol.asyncIterator])}function oE(e){return new TypeError(`You provided ${null!==e&&"object"==typeof e?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const rE=function E2(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function sE(e){return Be(e?.[rE])}function aE(e){return function Xb(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,i=t.apply(e,n||[]),r=[];return o=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",function s(h){return function(m){return Promise.resolve(m).then(h,d)}}),o[Symbol.asyncIterator]=function(){return this},o;function a(h,m){i[h]&&(o[h]=function(C){return new Promise(function(E,M){r.push([h,C,E,M])>1||l(h,C)})},m&&(o[h]=m(o[h])))}function l(h,m){try{!function c(h){h.value instanceof fi?Promise.resolve(h.value.v).then(u,d):p(r[0][2],h)}(i[h](m))}catch(C){p(r[0][3],C)}}function u(h){l("next",h)}function d(h){l("throw",h)}function p(h,m){h(m),r.shift(),r.length&&l(r[0][0],r[0][1])}}(this,arguments,function*(){const t=e.getReader();try{for(;;){const{value:i,done:o}=yield fi(t.read());if(o)return yield fi(void 0);yield yield fi(i)}}finally{t.releaseLock()}})}function lE(e){return Be(e?.getReader)}function Vs(e){if(e instanceof St)return e;if(null!=e){if(nE(e))return function M2(e){return new St(n=>{const t=e[Wc]();if(Be(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(e);if(eE(e))return function I2(e){return new St(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}(e);if(tE(e))return function T2(e){return new St(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,Kp)})}(e);if(iE(e))return cE(e);if(sE(e))return function S2(e){return new St(n=>{for(const t of e)if(n.next(t),n.closed)return;n.complete()})}(e);if(lE(e))return function x2(e){return cE(aE(e))}(e)}throw oE(e)}function cE(e){return new St(n=>{(function N2(e,n){var t,i,o,r;return function Yb(e,n,t,i){return new(t||(t=Promise))(function(r,s){function a(u){try{c(i.next(u))}catch(d){s(d)}}function l(u){try{c(i.throw(u))}catch(d){s(d)}}function c(u){u.done?r(u.value):function o(r){return r instanceof t?r:new t(function(s){s(r)})}(u.value).then(a,l)}c((i=i.apply(e,n||[])).next())})}(this,void 0,void 0,function*(){try{for(t=Jb(e);!(i=yield t.next()).done;)if(n.next(i.value),n.closed)return}catch(s){o={error:s}}finally{try{i&&!i.done&&(r=t.return)&&(yield r.call(t))}finally{if(o)throw o.error}}n.complete()})})(e,n).catch(t=>n.error(t))})}function Xi(e,n,t,i=0,o=!1){const r=n.schedule(function(){t(),o?e.add(this.schedule(null,i)):this.unsubscribe()},i);if(e.add(r),!o)return r}function uE(e,n=0){return Di((t,i)=>{t.subscribe(Yn(i,o=>Xi(i,e,()=>i.next(o),n),()=>Xi(i,e,()=>i.complete(),n),o=>Xi(i,e,()=>i.error(o),n)))})}function dE(e,n=0){return Di((t,i)=>{i.add(e.schedule(()=>t.subscribe(i),n))})}function fE(e,n){if(!e)throw new Error("Iterable cannot be null");return new St(t=>{Xi(t,n,()=>{const i=e[Symbol.asyncIterator]();Xi(t,n,()=>{i.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}const{isArray:V2}=Array,{getPrototypeOf:H2,prototype:B2,keys:j2}=Object;const{isArray:G2}=Array;function Z2(e,n){return e.reduce((t,i,o)=>(t[i]=n[o],t),{})}function Q2(...e){const n=function z2(e){return Be(function cp(e){return e[e.length-1]}(e))?e.pop():void 0}(e),{args:t,keys:i}=function U2(e){if(1===e.length){const n=e[0];if(V2(n))return{args:n,keys:null};if(function $2(e){return e&&"object"==typeof e&&H2(e)===B2}(n)){const t=j2(n);return{args:t.map(i=>n[i]),keys:t}}}return{args:e,keys:null}}(e),o=new St(r=>{const{length:s}=t;if(!s)return void r.complete();const a=new Array(s);let l=s,c=s;for(let u=0;u<s;u++){let d=!1;Vs(t[u]).subscribe(Yn(r,p=>{d||(d=!0,c--),a[u]=p},()=>l--,void 0,()=>{(!l||!d)&&(c||r.next(i?Z2(i,a):a),r.complete())}))}});return n?o.pipe(function W2(e){return Qc(n=>function q2(e,n){return G2(n)?e(...n):e(n)}(e,n))}(n)):o}let hE=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,i){this._renderer=t,this._elementRef=i}setProperty(t,i){this._renderer.setProperty(this._elementRef.nativeElement,t,i)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(i){return new(i||e)(T(nn),T(ut))};static \u0275dir=Z({type:e})}return e})(),Ji=(()=>{class e extends hE{static \u0275fac=(()=>{let t;return function(o){return(t||(t=it(e)))(o||e)}})();static \u0275dir=Z({type:e,features:[ue]})}return e})();const cn=new R(""),Y2={provide:cn,useExisting:ye(()=>up),multi:!0};let up=(()=>{class e extends Ji{writeValue(t){this.setProperty("checked",t)}static \u0275fac=(()=>{let t;return function(o){return(t||(t=it(e)))(o||e)}})();static \u0275dir=Z({type:e,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(i,o){1&i&&z("change",function(s){return o.onChange(s.target.checked)})("blur",function(){return o.onTouched()})},standalone:!1,features:[Me([Y2]),ue]})}return e})();const K2={provide:cn,useExisting:ye(()=>Hs),multi:!0},J2=new R("");let Hs=(()=>{class e extends hE{_compositionMode;_composing=!1;constructor(t,i,o){super(t,i),this._compositionMode=o,null==this._compositionMode&&(this._compositionMode=!function X2(){const e=Rs()?Rs().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}())}writeValue(t){this.setProperty("value",t??"")}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(i){return new(i||e)(T(nn),T(ut),T(J2,8))};static \u0275dir=Z({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,o){1&i&&z("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Me([K2]),ue]})}return e})();const ot=new R(""),hi=new R("");function wE(e){return null!=e}function bE(e){return Fl(e)?function P2(e,n){return n?function L2(e,n){if(null!=e){if(nE(e))return function O2(e,n){return Vs(e).pipe(dE(n),uE(n))}(e,n);if(eE(e))return function k2(e,n){return new St(t=>{let i=0;return n.schedule(function(){i===e.length?t.complete():(t.next(e[i++]),t.closed||this.schedule())})})}(e,n);if(tE(e))return function A2(e,n){return Vs(e).pipe(dE(n),uE(n))}(e,n);if(iE(e))return fE(e,n);if(sE(e))return function F2(e,n){return new St(t=>{let i;return Xi(t,n,()=>{i=e[rE](),Xi(t,n,()=>{let o,r;try{({value:o,done:r}=i.next())}catch(s){return void t.error(s)}r?t.complete():t.next(o)},0,!0)}),()=>Be(i?.return)&&i.return()})}(e,n);if(lE(e))return function R2(e,n){return fE(aE(e),n)}(e,n)}throw oE(e)}(e,n):Vs(e)}(e):e}function EE(e){let n={};return e.forEach(t=>{n=null!=t?{...n,...t}:n}),0===Object.keys(n).length?null:n}function ME(e,n){return n.map(t=>t(e))}function IE(e){return e.map(n=>function tH(e){return!e.validate}(n)?n:t=>n.validate(t))}function hp(e){return null!=e?function TE(e){if(!e)return null;const n=e.filter(wE);return 0==n.length?null:function(t){return EE(ME(t,n))}}(IE(e)):null}function pp(e){return null!=e?function SE(e){if(!e)return null;const n=e.filter(wE);return 0==n.length?null:function(t){return Q2(ME(t,n).map(bE)).pipe(Qc(EE))}}(IE(e)):null}function xE(e,n){return null===e?[n]:Array.isArray(e)?[...e,n]:[e,n]}function gp(e){return e?Array.isArray(e)?e:[e]:[]}function gc(e,n){return Array.isArray(e)?e.includes(n):e===n}function AE(e,n){const t=gp(n);return gp(e).forEach(o=>{gc(t,o)||t.push(o)}),t}function kE(e,n){return gp(n).filter(t=>!gc(e,t))}class FE{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=hp(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=pp(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return!!this.control&&this.control.hasError(n,t)}getError(n,t){return this.control?this.control.getError(n,t):null}}class mt extends FE{name;get formDirective(){return null}get path(){return null}}class pi extends FE{_parent=null;name=null;valueAccessor=null}class RE{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}}let mc=(()=>{class e extends RE{constructor(t){super(t)}static \u0275fac=function(i){return new(i||e)(T(pi,2))};static \u0275dir=Z({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,o){2&i&&Un("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[ue]})}return e})();const Bs="VALID",_c="INVALID",pr="PENDING",js="DISABLED";class gr{}class PE extends gr{value;source;constructor(n,t){super(),this.value=n,this.source=t}}class _p extends gr{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}}class yp extends gr{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}}class yc extends gr{status;source;constructor(n,t){super(),this.status=n,this.source=t}}function Cc(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}class wp{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return je(this.statusReactive)}set status(n){je(()=>this.statusReactive.set(n))}_status=zn(()=>this.statusReactive());statusReactive=gn(void 0);get valid(){return this.status===Bs}get invalid(){return this.status===_c}get pending(){return this.status==pr}get disabled(){return this.status===js}get enabled(){return this.status!==js}errors;get pristine(){return je(this.pristineReactive)}set pristine(n){je(()=>this.pristineReactive.set(n))}_pristine=zn(()=>this.pristineReactive());pristineReactive=gn(!0);get dirty(){return!this.pristine}get touched(){return je(this.touchedReactive)}set touched(n){je(()=>this.touchedReactive.set(n))}_touched=zn(()=>this.touchedReactive());touchedReactive=gn(!1);get untouched(){return!this.touched}_events=new un;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(AE(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(AE(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(kE(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(kE(n,this._rawAsyncValidators))}hasValidator(n){return gc(this._rawValidators,n)}hasAsyncValidator(n){return gc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){const t=!1===this.touched;this.touched=!0;const i=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched({...n,sourceControl:i}),t&&!1!==n.emitEvent&&this._events.next(new yp(!0,i))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){const t=!0===this.touched;this.touched=!1,this._pendingTouched=!1;const i=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:i})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,i),t&&!1!==n.emitEvent&&this._events.next(new yp(!1,i))}markAsDirty(n={}){const t=!0===this.pristine;this.pristine=!1;const i=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty({...n,sourceControl:i}),t&&!1!==n.emitEvent&&this._events.next(new _p(!1,i))}markAsPristine(n={}){const t=!1===this.pristine;this.pristine=!0,this._pendingDirty=!1;const i=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,i),t&&!1!==n.emitEvent&&this._events.next(new _p(!0,i))}markAsPending(n={}){this.status=pr;const t=n.sourceControl??this;!1!==n.emitEvent&&(this._events.next(new yc(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending({...n,sourceControl:t})}disable(n={}){const t=this._parentMarkedDirty(n.onlySelf);this.status=js,this.errors=null,this._forEachChild(o=>{o.disable({...n,onlySelf:!0})}),this._updateValue();const i=n.sourceControl??this;!1!==n.emitEvent&&(this._events.next(new PE(this.value,i)),this._events.next(new yc(this.status,i)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...n,skipPristineCheck:t},this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){const t=this._parentMarkedDirty(n.onlySelf);this.status=Bs,this._forEachChild(i=>{i.enable({...n,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors({...n,skipPristineCheck:t},this),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){const i=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Bs||this.status===pr)&&this._runAsyncValidator(i,n.emitEvent)}const t=n.sourceControl??this;!1!==n.emitEvent&&(this._events.next(new PE(this.value,t)),this._events.next(new yc(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity({...n,sourceControl:t})}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?js:Bs}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=pr,this._hasOwnPendingAsyncValidator={emitEvent:!1!==t};const i=bE(this.asyncValidator(this));this._asyncValidationSubscription=i.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();const n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(!1!==t.emitEvent,this,t.shouldHaveEmitted)}get(n){let t=n;return null==t||(Array.isArray(t)||(t=t.split(".")),0===t.length)?null:t.reduce((i,o)=>i&&i._find(o),this)}getError(n,t){const i=t?this.get(t):this;return i&&i.errors?i.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,i){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||i)&&this._events.next(new yc(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,i)}_initObservables(){this.valueChanges=new De,this.statusChanges=new De}_calculateStatus(){return this._allControlsDisabled()?js:this.errors?_c:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(pr)?pr:this._anyControlsHaveStatus(_c)?_c:Bs}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){const i=!this._anyControlsDirty(),o=this.pristine!==i;this.pristine=i,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new _p(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new yp(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Cc(n)&&null!=n.updateOn&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){return!n&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=function cH(e){return Array.isArray(e)?hp(e):e||null}(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=function uH(e){return Array.isArray(e)?pp(e):e||null}(this._rawAsyncValidators)}}const mr=new R("",{providedIn:"root",factory:()=>Dc}),Dc="always";function Us(e,n,t=Dc){(function Ep(e,n){const t=function NE(e){return e._rawValidators}(e);null!==n.validator?e.setValidators(xE(t,n.validator)):"function"==typeof t&&e.setValidators([t]);const i=function OE(e){return e._rawAsyncValidators}(e);null!==n.asyncValidator?e.setAsyncValidators(xE(i,n.asyncValidator)):"function"==typeof i&&e.setAsyncValidators([i]);const o=()=>e.updateValueAndValidity();Ec(n._rawValidators,o),Ec(n._rawAsyncValidators,o)})(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||"always"===t)&&n.valueAccessor.setDisabledState?.(e.disabled),function hH(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,"change"===e.updateOn&&UE(e,n)})}(e,n),function gH(e,n){const t=(i,o)=>{n.valueAccessor.writeValue(i),o&&n.viewToModelUpdate(i)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}(e,n),function pH(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,"blur"===e.updateOn&&e._pendingChange&&UE(e,n),"submit"!==e.updateOn&&e.markAsTouched()})}(e,n),function fH(e,n){if(n.valueAccessor.setDisabledState){const t=i=>{n.valueAccessor.setDisabledState(i)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}(e,n)}function Ec(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function UE(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function GE(e,n){const t=e.indexOf(n);t>-1&&e.splice(t,1)}function qE(e){return"object"==typeof e&&null!==e&&2===Object.keys(e).length&&"value"in e&&"disabled"in e}Promise.resolve();const WE=class extends wp{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,i){super(function Cp(e){return(Cc(e)?e.validators:e)||null}(t),function Dp(e,n){return(Cc(n)?n.asyncValidators:e)||null}(i,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Cc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(this.defaultValue=qE(n)?n.value:n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&!1!==t.emitModelToViewChange&&this._onChange.forEach(i=>i(this.value,!1!==t.emitViewToModelChange)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){GE(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){GE(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(n){qE(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}},MH={provide:pi,useExisting:ye(()=>zs)},ZE=Promise.resolve();let zs=(()=>{class e extends pi{_changeDetectorRef;callSetDisabledState;control=new WE;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new De;constructor(t,i,o,r,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=t,this._setValidators(i),this._setAsyncValidators(o),this.valueAccessor=function Tp(e,n){if(!n)return null;let t,i,o;return Array.isArray(n),n.forEach(r=>{r.constructor===Hs?t=r:function _H(e){return Object.getPrototypeOf(e.constructor)===Ji}(r)?i=r:o=r}),o||i||t||null}(0,r)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){const i=t.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),function Ip(e,n){if(!e.hasOwnProperty("model"))return!1;const t=e.model;return!!t.isFirstChange()||!Object.is(n,t.currentValue)}(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){Us(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(t){ZE.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){const i=t.isDisabled.currentValue,o=0!==i&&function Fh(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}(i);ZE.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?function wc(e,n){return[...n.path,e]}(t,this._parent):[t]}static \u0275fac=function(i){return new(i||e)(T(mt,9),T(ot,10),T(hi,10),T(cn,10),T(Zi,8),T(mr,8))};static \u0275dir=Z({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Me([MH]),ue,An]})}return e})();const NH={provide:cn,useExisting:ye(()=>Sp),multi:!0};let Sp=(()=>{class e extends Ji{writeValue(t){this.setProperty("value",parseFloat(t))}registerOnChange(t){this.onChange=i=>{t(""==i?null:parseFloat(i))}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=it(e)))(o||e)}})();static \u0275dir=Z({type:e,selectors:[["input","type","range","formControlName",""],["input","type","range","formControl",""],["input","type","range","ngModel",""]],hostBindings:function(i,o){1&i&&z("change",function(s){return o.onChange(s.target.value)})("input",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},standalone:!1,features:[Me([NH]),ue]})}return e})();const LH={provide:cn,useExisting:ye(()=>qs),multi:!0};function tM(e,n){return null==e?`${n}`:(n&&"object"==typeof n&&(n="Object"),`${e}: ${n}`.slice(0,50))}let qs=(()=>{class e extends Ji{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;const o=tM(this._getOptionId(t),t);this.setProperty("value",o)}registerOnChange(t){this.onChange=i=>{this.value=this._getOptionValue(i),t(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(t){for(const i of this._optionMap.keys())if(this._compareWith(this._optionMap.get(i),t))return i;return null}_getOptionValue(t){const i=function PH(e){return e.split(":")[0]}(t);return this._optionMap.has(i)?this._optionMap.get(i):t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=it(e)))(o||e)}})();static \u0275dir=Z({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(i,o){1&i&&z("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Me([LH]),ue]})}return e})(),xp=(()=>{class e{_element;_renderer;_select;id;constructor(t,i,o){this._element=t,this._renderer=i,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(t){null!=this._select&&(this._select._optionMap.set(this.id,t),this._setElementValue(tM(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._setElementValue(t),this._select&&this._select.writeValue(this._select.value)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(i){return new(i||e)(T(ut),T(nn),T(qs,9))};static \u0275dir=Z({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();const VH={provide:cn,useExisting:ye(()=>Np),multi:!0};function nM(e,n){return null==e?`${n}`:("string"==typeof n&&(n=`'${n}'`),n&&"object"==typeof n&&(n="Object"),`${e}: ${n}`.slice(0,50))}let Np=(()=>{class e extends Ji{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){let i;if(this.value=t,Array.isArray(t)){const o=t.map(r=>this._getOptionId(r));i=(r,s)=>{r._setSelected(o.indexOf(s.toString())>-1)}}else i=(o,r)=>{o._setSelected(!1)};this._optionMap.forEach(i)}registerOnChange(t){this.onChange=i=>{const o=[],r=i.selectedOptions;if(void 0!==r){const s=r;for(let a=0;a<s.length;a++){const c=this._getOptionValue(s[a].value);o.push(c)}}else{const s=i.options;for(let a=0;a<s.length;a++){const l=s[a];if(l.selected){const c=this._getOptionValue(l.value);o.push(c)}}}this.value=o,t(o)}}_registerOption(t){const i=(this._idCounter++).toString();return this._optionMap.set(i,t),i}_getOptionId(t){for(const i of this._optionMap.keys())if(this._compareWith(this._optionMap.get(i)._value,t))return i;return null}_getOptionValue(t){const i=function HH(e){return e.split(":")[0]}(t);return this._optionMap.has(i)?this._optionMap.get(i)._value:t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=it(e)))(o||e)}})();static \u0275dir=Z({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(i,o){1&i&&z("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Me([VH]),ue]})}return e})(),Op=(()=>{class e{_element;_renderer;_select;id;_value;constructor(t,i,o){this._element=t,this._renderer=i,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(t){null!=this._select&&(this._value=t,this._setElementValue(nM(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._select?(this._value=t,this._setElementValue(nM(this.id,t)),this._select.writeValue(this._select.value)):this._setElementValue(t)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}_setSelected(t){this._renderer.setProperty(this._element.nativeElement,"selected",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(i){return new(i||e)(T(ut),T(nn),T(Np,9))};static \u0275dir=Z({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),ZH=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=si({type:e});static \u0275inj=Tn({})}return e})(),YH=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:mr,useValue:t.callSetDisabledState??Dc}]}}static \u0275fac=function(i){return new(i||e)};static \u0275mod=si({type:e});static \u0275inj=Tn({imports:[ZH]})}return e})();class KH extends Tt{constructor(n,t){super()}schedule(n,t=0){return this}}const Oc={setInterval(e,n,...t){const{delegate:i}=Oc;return i?.setInterval?i.setInterval(e,n,...t):setInterval(e,n,...t)},clearInterval(e){const{delegate:n}=Oc;return(n?.clearInterval||clearInterval)(e)},delegate:void 0},fM={now:()=>(fM.delegate||Date).now(),delegate:void 0};class Ws{constructor(n,t=Ws.now){this.schedulerActionCtor=n,this.now=t}schedule(n,t=0,i){return new this.schedulerActionCtor(this,n).schedule(i,t)}}Ws.now=fM.now;const hM=new class JH extends Ws{constructor(n,t=Ws.now){super(n,t),this.actions=[],this._active=!1}flush(n){const{actions:t}=this;if(this._active)return void t.push(n);let i;this._active=!0;do{if(i=n.execute(n.state,n.delay))break}while(n=t.shift());if(this._active=!1,i){for(;n=t.shift();)n.unsubscribe();throw i}}}(class XH extends KH{constructor(n,t){super(n,t),this.scheduler=n,this.work=t,this.pending=!1}schedule(n,t=0){var i;if(this.closed)return this;this.state=n;const o=this.id,r=this.scheduler;return null!=o&&(this.id=this.recycleAsyncId(r,o,t)),this.pending=!0,this.delay=t,this.id=null!==(i=this.id)&&void 0!==i?i:this.requestAsyncId(r,this.id,t),this}requestAsyncId(n,t,i=0){return Oc.setInterval(n.flush.bind(n,this),i)}recycleAsyncId(n,t,i=0){if(null!=i&&this.delay===i&&!1===this.pending)return t;null!=t&&Oc.clearInterval(t)}execute(n,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;const i=this._execute(n,t);if(i)return i;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(n,t){let o,i=!1;try{this.work(n)}catch(r){i=!0,o=r||new Error("Scheduled action threw falsy error")}if(i)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){const{id:n,scheduler:t}=this,{actions:i}=t;this.work=this.state=this.scheduler=null,this.pending=!1,ia(i,this),null!=n&&(this.id=this.recycleAsyncId(t,n,null)),this.delay=null,super.unsubscribe()}}}),eB=hM;function pM(e,n=hM,t){const i=function oB(e=0,n,t=eB){let i=-1;return null!=n&&(function nB(e){return e&&Be(e.schedule)}(n)?t=n:i=n),new St(o=>{let r=function iB(e){return e instanceof Date&&!isNaN(e)}(e)?+e-t.now():e;r<0&&(r=0);let s=0;return t.schedule(function(){o.closed||(o.next(s++),0<=i?this.schedule(void 0,i):o.complete())},r)})}(e,n);return function tB(e,n){return Di((t,i)=>{const{leading:o=!0,trailing:r=!1}=n??{};let s=!1,a=null,l=null,c=!1;const u=()=>{l?.unsubscribe(),l=null,r&&(h(),c&&i.complete())},d=()=>{l=null,c&&i.complete()},p=m=>l=Vs(e(m)).subscribe(Yn(i,u,d)),h=()=>{if(s){s=!1;const m=a;a=null,i.next(m),!c&&p(m)}};t.subscribe(Yn(i,m=>{s=!0,a=m,(!l||l.closed)&&(o?h():p(m))},()=>{c=!0,(!(r&&s&&l)||l.closed)&&i.complete()}))})}(()=>i,t)}function gM(e,n,t){const i=Be(e)||n||t?{next:e,error:n,complete:t}:e;return i?Di((o,r)=>{var s;null===(s=i.subscribe)||void 0===s||s.call(i);let a=!0;o.subscribe(Yn(r,l=>{var c;null===(c=i.next)||void 0===c||c.call(i,l),r.next(l)},()=>{var l;a=!1,null===(l=i.complete)||void 0===l||l.call(i),r.complete()},l=>{var c;a=!1,null===(c=i.error)||void 0===c||c.call(i,l),r.error(l)},()=>{var l,c;a&&(null===(l=i.unsubscribe)||void 0===l||l.call(i)),null===(c=i.finalize)||void 0===c||c.call(i)}))}):Zc}function mM(e,n=Zc){return e=e??rB,Di((t,i)=>{let o,r=!0;t.subscribe(Yn(i,s=>{const a=n(s);(r||!e(o,a))&&(r=!1,o=a,i.next(s))}))})}function rB(e,n){return e===n}var Vt=typeof window<"u"?window:{screen:{},navigator:{}},vr=(Vt.matchMedia||function(){return{matches:!1}}).bind(Vt),vM=!1,_M=function(){};Vt.addEventListener&&Vt.addEventListener("p",_M,{get passive(){return vM=!0}}),Vt.removeEventListener&&Vt.removeEventListener("p",_M,!1);var yM=vM,kp="ontouchstart"in Vt,DM=(kp||"TouchEvent"in Vt&&vr("(any-pointer: coarse)"),Vt.navigator.userAgent||"");vr("(pointer: coarse)").matches&&/iPad|Macintosh/.test(DM)&&Math.min(Vt.screen.width||0,Vt.screen.height||0);(vr("(pointer: coarse)").matches||!vr("(pointer: fine)").matches&&kp)&&/Windows.*Firefox/.test(DM),vr("(any-pointer: fine)").matches||vr("(any-hover: hover)");const fB=(e,n,t)=>({tooltip:e,placement:n,content:t});function hB(e,n){}function pB(e,n){1&e&&L(0,hB,0,0,"ng-template")}function gB(e,n){if(1&e&&(K(0),L(1,pB,1,0,null,1),X()),2&e){const t=v();f(),g("ngTemplateOutlet",t.template)("ngTemplateOutletContext",Le(2,fB,t.tooltip,t.placement,t.content))}}function mB(e,n){if(1&e&&(K(0),y(1,"div",2),w(2),_(),X()),2&e){const t=v();f(),ft("title",t.tooltip)("data-tooltip-placement",t.placement),f(),j(" ",t.content," ")}}const vB=["tooltipTemplate"],_B=["leftOuterSelectionBar"],yB=["rightOuterSelectionBar"],CB=["fullBar"],DB=["selectionBar"],wB=["minHandle"],bB=["maxHandle"],EB=["floorLabel"],MB=["ceilLabel"],IB=["minHandleLabel"],TB=["maxHandleLabel"],SB=["combinedLabel"],xB=["ticksElement"],NB=e=>({"ngx-slider-selected":e});function OB(e,n){if(1&e&&N(0,"ngx-slider-tooltip-wrapper",32),2&e){const t=v().$implicit;g("template",v().tooltipTemplate)("tooltip",t.valueTooltip)("placement",t.valueTooltipPlacement)("content",t.value)}}function AB(e,n){1&e&&N(0,"span",33),2&e&&g("innerText",v().$implicit.legend)}function kB(e,n){1&e&&N(0,"span",34),2&e&&g("innerHTML",v().$implicit.legend,Kv)}function FB(e,n){if(1&e&&(y(0,"span",27),N(1,"ngx-slider-tooltip-wrapper",28),L(2,OB,1,4,"ngx-slider-tooltip-wrapper",29)(3,AB,1,1,"span",30)(4,kB,1,1,"span",31),_()),2&e){const t=n.$implicit,i=v();g("ngClass",ur(8,NB,t.selected))("ngStyle",t.style),f(),g("template",i.tooltipTemplate)("tooltip",t.tooltip)("placement",t.tooltipPlacement),f(),g("ngIf",null!=t.value),f(),g("ngIf",null!=t.legend&&!1===i.allowUnsafeHtmlInSlider),f(),g("ngIf",null!=t.legend&&(null==i.allowUnsafeHtmlInSlider||i.allowUnsafeHtmlInSlider))}}var En=function(e){return e[e.Low=0]="Low",e[e.High=1]="High",e[e.Floor=2]="Floor",e[e.Ceil=3]="Ceil",e[e.TickValue=4]="TickValue",e}(En||{});class Ac{floor=0;ceil=null;step=1;minRange=null;maxRange=null;pushRange=!1;minLimit=null;maxLimit=null;translate=null;combineLabels=null;getLegend=null;getStepLegend=null;stepsArray=null;bindIndexForStepsArray=!1;draggableRange=!1;draggableRangeOnly=!1;showSelectionBar=!1;showSelectionBarEnd=!1;showSelectionBarFromValue=null;showOuterSelectionBars=!1;hidePointerLabels=!1;hideLimitLabels=!1;autoHideLimitLabels=!0;readOnly=!1;disabled=!1;showTicks=!1;showTicksValues=!1;tickStep=null;tickValueStep=null;ticksArray=null;ticksTooltip=null;ticksValuesTooltip=null;vertical=!1;getSelectionBarColor=null;getTickColor=null;getPointerColor=null;keyboardSupport=!0;scale=1;rotate=0;enforceStep=!0;enforceRange=!0;enforceStepsArray=!0;noSwitching=!1;onlyBindHandles=!1;rightToLeft=!1;reversedControls=!1;boundPointerLabels=!0;logScale=!1;customValueToPosition=null;customPositionToValue=null;precisionLimit=12;selectionBarGradient=null;ariaLabel="ngx-slider";ariaLabelledBy=null;ariaLabelHigh="ngx-slider-max";ariaLabelledByHigh=null;handleDimension=null;barDimension=null;animate=!0;animateOnMove=!1}const EM=new R("AllowUnsafeHtmlInSlider");var F=function(e){return e[e.Min=0]="Min",e[e.Max=1]="Max",e}(F||{});class RB{value;highValue;pointerType}class I{static isNullOrUndefined(n){return null==n}static areArraysEqual(n,t){if(n.length!==t.length)return!1;for(let i=0;i<n.length;++i)if(n[i]!==t[i])return!1;return!0}static linearValueToPosition(n,t,i){return(n-t)/(i-t)}static logValueToPosition(n,t,i){return((n=Math.log(n))-(t=Math.log(t)))/((i=Math.log(i))-t)}static linearPositionToValue(n,t,i){return n*(i-t)+t}static logPositionToValue(n,t,i){return t=Math.log(t),i=Math.log(i),Math.exp(n*(i-t)+t)}static findStepIndex(n,t){const i=t.map(r=>Math.abs(n-r.value));let o=0;for(let r=0;r<t.length;r++)i[r]!==i[o]&&i[r]<i[o]&&(o=r);return o}}class gi{static isTouchEvent(n){return void 0!==window.TouchEvent?n instanceof TouchEvent:void 0!==n.touches}static isResizeObserverAvailable(){return void 0!==window.ResizeObserver}}class He{static roundToPrecisionLimit(n,t){return+n.toPrecision(t)}static isModuloWithinPrecisionLimit(n,t,i){const o=Math.pow(10,-i);return Math.abs(n%t)<=o||Math.abs(Math.abs(n%t)-t)<=o}static clampToRange(n,t,i){return Math.min(Math.max(n,t),i)}}class MM{eventName=null;events=null;eventsSubscription=null;teardownCallback=null}class IM{renderer;constructor(n){this.renderer=n}attachPassiveEventListener(n,t,i,o){if(!0!==yM)return this.attachEventListener(n,t,i,o);const r=new MM;r.eventName=t,r.events=new un;const s=a=>{r.events.next(a)};return n.addEventListener(t,s,{passive:!0,capture:!1}),r.teardownCallback=()=>{n.removeEventListener(t,s,{passive:!0,capture:!1})},r.eventsSubscription=r.events.pipe(I.isNullOrUndefined(o)?gM(()=>{}):pM(o,void 0,{leading:!0,trailing:!0})).subscribe(a=>{i(a)}),r}detachEventListener(n){I.isNullOrUndefined(n.eventsSubscription)||(n.eventsSubscription.unsubscribe(),n.eventsSubscription=null),I.isNullOrUndefined(n.events)||(n.events.complete(),n.events=null),I.isNullOrUndefined(n.teardownCallback)||(n.teardownCallback(),n.teardownCallback=null)}attachEventListener(n,t,i,o){const r=new MM;return r.eventName=t,r.events=new un,r.teardownCallback=this.renderer.listen(n,t,a=>{r.events.next(a)}),r.eventsSubscription=r.events.pipe(I.isNullOrUndefined(o)?gM(()=>{}):pM(o,void 0,{leading:!0,trailing:!0})).subscribe(a=>{i(a)}),r}}let mi=(()=>{class e{elemRef;renderer;changeDetectionRef;_position=0;get position(){return this._position}_dimension=0;get dimension(){return this._dimension}_alwaysHide=!1;get alwaysHide(){return this._alwaysHide}_vertical=!1;get vertical(){return this._vertical}_scale=1;get scale(){return this._scale}_rotate=0;get rotate(){return this._rotate}opacity=1;visibility="visible";left="";bottom="";height="";width="";transform="";eventListenerHelper;eventListeners=[];constructor(t,i,o){this.elemRef=t,this.renderer=i,this.changeDetectionRef=o,this.eventListenerHelper=new IM(this.renderer)}setAlwaysHide(t){this._alwaysHide=t,this.visibility=t?"hidden":"visible"}hide(){this.opacity=0}show(){this.alwaysHide||(this.opacity=1)}isVisible(){return!this.alwaysHide&&0!==this.opacity}setVertical(t){this._vertical=t,this._vertical?(this.left="",this.width=""):(this.bottom="",this.height="")}setScale(t){this._scale=t}setRotate(t){this._rotate=t,this.transform="rotate("+t+"deg)"}getRotate(){return this._rotate}setPosition(t){this._position!==t&&!this.isRefDestroyed()&&this.changeDetectionRef.markForCheck(),this._position=t,this._vertical?this.bottom=Math.round(t)+"px":this.left=Math.round(t)+"px"}calculateDimension(){const t=this.getBoundingClientRect();this._dimension=this.vertical?(t.bottom-t.top)*this.scale:(t.right-t.left)*this.scale}setDimension(t){this._dimension!==t&&!this.isRefDestroyed()&&this.changeDetectionRef.markForCheck(),this._dimension=t,this._vertical?this.height=Math.round(t)+"px":this.width=Math.round(t)+"px"}getBoundingClientRect(){return this.elemRef.nativeElement.getBoundingClientRect()}on(t,i,o){const r=this.eventListenerHelper.attachEventListener(this.elemRef.nativeElement,t,i,o);this.eventListeners.push(r)}onPassive(t,i,o){const r=this.eventListenerHelper.attachPassiveEventListener(this.elemRef.nativeElement,t,i,o);this.eventListeners.push(r)}off(t){let i,o;I.isNullOrUndefined(t)?(i=[],o=this.eventListeners):(i=this.eventListeners.filter(r=>r.eventName!==t),o=this.eventListeners.filter(r=>r.eventName===t));for(const r of o)this.eventListenerHelper.detachEventListener(r);this.eventListeners=i}isRefDestroyed(){return I.isNullOrUndefined(this.changeDetectionRef)||this.changeDetectionRef.destroyed}static \u0275fac=function(i){return new(i||e)(T(ut),T(nn),T(Zi))};static \u0275dir=Z({type:e,selectors:[["","ngxSliderElement",""]],hostVars:14,hostBindings:function(i,o){2&i&&Vl("opacity",o.opacity)("visibility",o.visibility)("left",o.left)("bottom",o.bottom)("height",o.height)("width",o.width)("transform",o.transform)},standalone:!1})}return e})(),Fp=(()=>{class e extends mi{active=!1;role="";tabindex="";ariaOrientation="";ariaLabel="";ariaLabelledBy="";ariaValueNow="";ariaValueText="";ariaValueMin="";ariaValueMax="";focus(){this.elemRef.nativeElement.focus()}focusIfNeeded(){document.activeElement!==this.elemRef.nativeElement&&this.elemRef.nativeElement.focus()}constructor(t,i,o){super(t,i,o)}static \u0275fac=function(i){return new(i||e)(T(ut),T(nn),T(Zi))};static \u0275dir=Z({type:e,selectors:[["","ngxSliderHandle",""]],hostVars:11,hostBindings:function(i,o){2&i&&(ft("role",o.role)("tabindex",o.tabindex)("aria-orientation",o.ariaOrientation)("aria-label",o.ariaLabel)("aria-labelledby",o.ariaLabelledBy)("aria-valuenow",o.ariaValueNow)("aria-valuetext",o.ariaValueText)("aria-valuemin",o.ariaValueMin)("aria-valuemax",o.ariaValueMax),Un("ngx-slider-active",o.active))},standalone:!1,features:[ue]})}return e})(),_r=(()=>{class e extends mi{allowUnsafeHtmlInSlider;_value=null;get value(){return this._value}constructor(t,i,o,r){super(t,i,o),this.allowUnsafeHtmlInSlider=r}setValue(t){let i=!1;!this.alwaysHide&&(I.isNullOrUndefined(this.value)||this.value.length!==t.length||this.value.length>0&&0===this.dimension)&&(i=!0),this._value=t,!1===this.allowUnsafeHtmlInSlider?this.elemRef.nativeElement.innerText=t:this.elemRef.nativeElement.innerHTML=t,i&&this.calculateDimension()}static \u0275fac=function(i){return new(i||e)(T(ut),T(nn),T(Zi),T(EM,8))};static \u0275dir=Z({type:e,selectors:[["","ngxSliderLabel",""]],standalone:!1,features:[ue]})}return e})(),LB=(()=>{class e{template;tooltip;placement;content;static \u0275fac=function(i){return new(i||e)};static \u0275cmp=on({type:e,selectors:[["ngx-slider-tooltip-wrapper"]],inputs:{template:"template",tooltip:"tooltip",placement:"placement",content:"content"},standalone:!1,decls:2,vars:2,consts:[[4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"ngx-slider-inner-tooltip"]],template:function(i,o){1&i&&L(0,gB,2,6,"ng-container",0)(1,mB,3,3,"ng-container",0),2&i&&(g("ngIf",o.template),f(),g("ngIf",!o.template))},dependencies:[qn,Ob],styles:[".ngx-slider-inner-tooltip[_ngcontent-%COMP%]{height:100%}"]})}return e})();class PB{selected=!1;style={};tooltip=null;tooltipPlacement=null;value=null;valueTooltip=null;valueTooltipPlacement=null;legend=null}class TM{active=!1;value=0;difference=0;position=0;lowLimit=0;highLimit=0}class kc{value;highValue;static compare(n,t){return!(I.isNullOrUndefined(n)&&I.isNullOrUndefined(t)||I.isNullOrUndefined(n)!==I.isNullOrUndefined(t))&&n.value===t.value&&n.highValue===t.highValue}}class SM extends kc{forceChange;static compare(n,t){return!(I.isNullOrUndefined(n)&&I.isNullOrUndefined(t)||I.isNullOrUndefined(n)!==I.isNullOrUndefined(t))&&n.value===t.value&&n.highValue===t.highValue&&n.forceChange===t.forceChange}}const VB={provide:cn,useExisting:ye(()=>xM),multi:!0};let xM=(()=>{class e{renderer;elementRef;changeDetectionRef;zone;allowUnsafeHtmlInSlider;sliderElementNgxSliderClass=!0;value=null;valueChange=new De;highValue=null;highValueChange=new De;options=new Ac;userChangeStart=new De;userChange=new De;userChangeEnd=new De;manualRefreshSubscription;set manualRefresh(t){this.unsubscribeManualRefresh(),this.manualRefreshSubscription=t.subscribe(()=>{setTimeout(()=>this.calculateViewDimensionsAndDetectChanges())})}triggerFocusSubscription;set triggerFocus(t){this.unsubscribeTriggerFocus(),this.triggerFocusSubscription=t.subscribe(i=>{this.focusPointer(i)})}cancelUserChangeSubscription;set cancelUserChange(t){this.unsubscribeCancelUserChange(),this.cancelUserChangeSubscription=t.subscribe(()=>{this.moving&&(this.positionTrackingHandle(this.preStartHandleValue),this.forceEnd(!0))})}get range(){return!I.isNullOrUndefined(this.value)&&!I.isNullOrUndefined(this.highValue)}initHasRun=!1;inputModelChangeSubject=new un;inputModelChangeSubscription=null;outputModelChangeSubject=new un;outputModelChangeSubscription=null;viewLowValue=null;viewHighValue=null;viewOptions=new Ac;handleHalfDimension=0;maxHandlePosition=0;currentTrackingPointer=null;currentFocusPointer=null;firstKeyDown=!1;touchId=null;dragging=new TM;preStartHandleValue=null;leftOuterSelectionBarElement;rightOuterSelectionBarElement;fullBarElement;selectionBarElement;minHandleElement;maxHandleElement;floorLabelElement;ceilLabelElement;minHandleLabelElement;maxHandleLabelElement;combinedLabelElement;ticksElement;tooltipTemplate;sliderElementVerticalClass=!1;sliderElementAnimateClass=!1;sliderElementWithLegendClass=!1;sliderElementDisabledAttr=null;sliderElementAriaLabel="ngx-slider";barStyle={};minPointerStyle={};maxPointerStyle={};fullBarTransparentClass=!1;selectionBarDraggableClass=!1;ticksUnderValuesClass=!1;get showTicks(){return this.viewOptions.showTicks}intermediateTicks=!1;ticks=[];eventListenerHelper=null;onMoveEventListener=null;onEndEventListener=null;moving=!1;resizeObserver=null;onTouchedCallback=null;onChangeCallback=null;constructor(t,i,o,r,s){this.renderer=t,this.elementRef=i,this.changeDetectionRef=o,this.zone=r,this.allowUnsafeHtmlInSlider=s,this.eventListenerHelper=new IM(this.renderer)}ngOnInit(){this.viewOptions=new Ac,Object.assign(this.viewOptions,this.options),this.updateDisabledState(),this.updateVerticalState(),this.updateAriaLabel()}ngAfterViewInit(){this.applyOptions(),this.subscribeInputModelChangeSubject(),this.subscribeOutputModelChangeSubject(),this.renormaliseModelValues(),this.viewLowValue=this.modelValueToViewValue(this.value),this.viewHighValue=this.range?this.modelValueToViewValue(this.highValue):null,this.updateVerticalState(),this.manageElementsStyle(),this.updateDisabledState(),this.calculateViewDimensions(),this.addAccessibility(),this.updateCeilLabel(),this.updateFloorLabel(),this.initHandles(),this.manageEventsBindings(),this.updateAriaLabel(),this.subscribeResizeObserver(),this.initHasRun=!0,this.isRefDestroyed()||this.changeDetectionRef.detectChanges()}ngOnChanges(t){!I.isNullOrUndefined(t.options)&&JSON.stringify(t.options.previousValue)!==JSON.stringify(t.options.currentValue)&&this.onChangeOptions(),(!I.isNullOrUndefined(t.value)||!I.isNullOrUndefined(t.highValue))&&this.inputModelChangeSubject.next({value:this.value,highValue:this.highValue,controlAccessorChange:!1,forceChange:!1,internalChange:!1})}ngOnDestroy(){this.unbindEvents(),this.unsubscribeResizeObserver(),this.unsubscribeInputModelChangeSubject(),this.unsubscribeOutputModelChangeSubject(),this.unsubscribeManualRefresh(),this.unsubscribeTriggerFocus()}writeValue(t){t instanceof Array?(this.value=t[0],this.highValue=t[1]):this.value=t,this.inputModelChangeSubject.next({value:this.value,highValue:this.highValue,forceChange:!1,internalChange:!1,controlAccessorChange:!0})}registerOnChange(t){this.onChangeCallback=t}registerOnTouched(t){this.onTouchedCallback=t}setDisabledState(t){this.viewOptions.disabled=t,this.updateDisabledState(),this.initHasRun&&this.manageEventsBindings()}setAriaLabel(t){this.viewOptions.ariaLabel=t,this.updateAriaLabel()}onResize(t){this.calculateViewDimensionsAndDetectChanges()}subscribeInputModelChangeSubject(){this.inputModelChangeSubscription=this.inputModelChangeSubject.pipe(mM(SM.compare),function sB(e,n){return Di((t,i)=>{let o=0;t.subscribe(Yn(i,r=>e.call(n,r,o++)&&i.next(r)))})}(t=>!t.forceChange&&!t.internalChange)).subscribe(t=>this.applyInputModelChange(t))}subscribeOutputModelChangeSubject(){this.outputModelChangeSubscription=this.outputModelChangeSubject.pipe(mM(SM.compare)).subscribe(t=>this.publishOutputModelChange(t))}subscribeResizeObserver(){gi.isResizeObserverAvailable()&&(this.resizeObserver=new ResizeObserver(()=>this.calculateViewDimensionsAndDetectChanges()),this.resizeObserver.observe(this.elementRef.nativeElement))}unsubscribeResizeObserver(){gi.isResizeObserverAvailable()&&null!==this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)}unsubscribeOnMove(){I.isNullOrUndefined(this.onMoveEventListener)||(this.eventListenerHelper.detachEventListener(this.onMoveEventListener),this.onMoveEventListener=null)}unsubscribeOnEnd(){I.isNullOrUndefined(this.onEndEventListener)||(this.eventListenerHelper.detachEventListener(this.onEndEventListener),this.onEndEventListener=null)}unsubscribeInputModelChangeSubject(){I.isNullOrUndefined(this.inputModelChangeSubscription)||(this.inputModelChangeSubscription.unsubscribe(),this.inputModelChangeSubscription=null)}unsubscribeOutputModelChangeSubject(){I.isNullOrUndefined(this.outputModelChangeSubscription)||(this.outputModelChangeSubscription.unsubscribe(),this.outputModelChangeSubscription=null)}unsubscribeManualRefresh(){I.isNullOrUndefined(this.manualRefreshSubscription)||(this.manualRefreshSubscription.unsubscribe(),this.manualRefreshSubscription=null)}unsubscribeTriggerFocus(){I.isNullOrUndefined(this.triggerFocusSubscription)||(this.triggerFocusSubscription.unsubscribe(),this.triggerFocusSubscription=null)}unsubscribeCancelUserChange(){I.isNullOrUndefined(this.cancelUserChangeSubscription)||(this.cancelUserChangeSubscription.unsubscribe(),this.cancelUserChangeSubscription=null)}getPointerElement(t){return t===F.Min?this.minHandleElement:t===F.Max?this.maxHandleElement:null}getCurrentTrackingValue(){return this.currentTrackingPointer===F.Min?this.viewLowValue:this.currentTrackingPointer===F.Max?this.viewHighValue:null}modelValueToViewValue(t){return I.isNullOrUndefined(t)?NaN:I.isNullOrUndefined(this.viewOptions.stepsArray)||this.viewOptions.bindIndexForStepsArray?+t:I.findStepIndex(+t,this.viewOptions.stepsArray)}viewValueToModelValue(t){return I.isNullOrUndefined(this.viewOptions.stepsArray)||this.viewOptions.bindIndexForStepsArray?t:this.getStepValue(t)}getStepValue(t){const i=this.viewOptions.stepsArray[t];return I.isNullOrUndefined(i)?NaN:i.value}applyViewChange(){this.value=this.viewValueToModelValue(this.viewLowValue),this.range&&(this.highValue=this.viewValueToModelValue(this.viewHighValue)),this.outputModelChangeSubject.next({value:this.value,highValue:this.highValue,controlAccessorChange:!1,userEventInitiated:!0,forceChange:!1}),this.inputModelChangeSubject.next({value:this.value,highValue:this.highValue,controlAccessorChange:!1,forceChange:!1,internalChange:!0})}applyInputModelChange(t){const i=this.normaliseModelValues(t),o=!kc.compare(t,i);o&&(this.value=i.value,this.highValue=i.highValue),this.viewLowValue=this.modelValueToViewValue(i.value),this.viewHighValue=this.range?this.modelValueToViewValue(i.highValue):null,this.updateLowHandle(this.valueToPosition(this.viewLowValue)),this.range&&this.updateHighHandle(this.valueToPosition(this.viewHighValue)),this.updateSelectionBar(),this.updateTicksScale(),this.updateAriaAttributes(),this.range&&this.updateCombinedLabel(),this.outputModelChangeSubject.next({value:i.value,highValue:i.highValue,controlAccessorChange:t.controlAccessorChange,forceChange:o,userEventInitiated:!1})}publishOutputModelChange(t){const i=()=>{this.valueChange.emit(t.value),this.range&&this.highValueChange.emit(t.highValue),!t.controlAccessorChange&&(I.isNullOrUndefined(this.onChangeCallback)||this.onChangeCallback(this.range?[t.value,t.highValue]:t.value),I.isNullOrUndefined(this.onTouchedCallback)||this.onTouchedCallback(this.range?[t.value,t.highValue]:t.value))};t.userEventInitiated?(i(),this.userChange.emit(this.getChangeContext())):setTimeout(()=>{i()})}normaliseModelValues(t){const i=new kc;if(i.value=t.value,i.highValue=t.highValue,!I.isNullOrUndefined(this.viewOptions.stepsArray)){if(this.viewOptions.enforceStepsArray){const o=I.findStepIndex(i.value,this.viewOptions.stepsArray);if(i.value=this.viewOptions.stepsArray[o].value,this.range){const r=I.findStepIndex(i.highValue,this.viewOptions.stepsArray);i.highValue=this.viewOptions.stepsArray[r].value}}return i}if(this.viewOptions.enforceStep&&(i.value=this.roundStep(i.value),this.range&&(i.highValue=this.roundStep(i.highValue))),this.viewOptions.enforceRange&&(i.value=He.clampToRange(i.value,this.viewOptions.floor,this.viewOptions.ceil),this.range&&(i.highValue=He.clampToRange(i.highValue,this.viewOptions.floor,this.viewOptions.ceil)),this.range&&t.value>t.highValue))if(this.viewOptions.noSwitching)i.value=i.highValue;else{const o=t.value;i.value=t.highValue,i.highValue=o}return i}renormaliseModelValues(){const t={value:this.value,highValue:this.highValue},i=this.normaliseModelValues(t);kc.compare(i,t)||(this.value=i.value,this.highValue=i.highValue,this.outputModelChangeSubject.next({value:this.value,highValue:this.highValue,controlAccessorChange:!1,forceChange:!0,userEventInitiated:!1}))}onChangeOptions(){if(!this.initHasRun)return;const t=this.getOptionsInfluencingEventBindings(this.viewOptions);this.applyOptions();const i=this.getOptionsInfluencingEventBindings(this.viewOptions),o=!I.areArraysEqual(t,i);this.renormaliseModelValues(),this.viewLowValue=this.modelValueToViewValue(this.value),this.viewHighValue=this.range?this.modelValueToViewValue(this.highValue):null,this.resetSlider(o)}applyOptions(){if(this.viewOptions=new Ac,Object.assign(this.viewOptions,this.options),this.viewOptions.draggableRange=this.range&&this.viewOptions.draggableRange,this.viewOptions.draggableRangeOnly=this.range&&this.viewOptions.draggableRangeOnly,this.viewOptions.draggableRangeOnly&&(this.viewOptions.draggableRange=!0),this.viewOptions.showTicks=this.viewOptions.showTicks||this.viewOptions.showTicksValues||!I.isNullOrUndefined(this.viewOptions.ticksArray),this.viewOptions.showTicks&&(!I.isNullOrUndefined(this.viewOptions.tickStep)||!I.isNullOrUndefined(this.viewOptions.ticksArray))&&(this.intermediateTicks=!0),this.viewOptions.showSelectionBar=this.viewOptions.showSelectionBar||this.viewOptions.showSelectionBarEnd||!I.isNullOrUndefined(this.viewOptions.showSelectionBarFromValue),I.isNullOrUndefined(this.viewOptions.stepsArray)?this.applyFloorCeilOptions():this.applyStepsArrayOptions(),I.isNullOrUndefined(this.viewOptions.combineLabels)&&(this.viewOptions.combineLabels=(t,i)=>t+" - "+i),this.viewOptions.logScale&&0===this.viewOptions.floor)throw Error("Can't use floor=0 with logarithmic scale")}applyStepsArrayOptions(){this.viewOptions.floor=0,this.viewOptions.ceil=this.viewOptions.stepsArray.length-1,this.viewOptions.step=1,I.isNullOrUndefined(this.viewOptions.translate)&&(this.viewOptions.translate=t=>String(this.viewOptions.bindIndexForStepsArray?this.getStepValue(t):t))}applyFloorCeilOptions(){if(I.isNullOrUndefined(this.viewOptions.step)?this.viewOptions.step=1:(this.viewOptions.step=+this.viewOptions.step,this.viewOptions.step<=0&&(this.viewOptions.step=1)),I.isNullOrUndefined(this.viewOptions.ceil)||I.isNullOrUndefined(this.viewOptions.floor))throw Error("floor and ceil options must be supplied");this.viewOptions.ceil=+this.viewOptions.ceil,this.viewOptions.floor=+this.viewOptions.floor,I.isNullOrUndefined(this.viewOptions.translate)&&(this.viewOptions.translate=t=>String(t))}resetSlider(t=!0){this.manageElementsStyle(),this.addAccessibility(),this.updateCeilLabel(),this.updateFloorLabel(),t&&(this.unbindEvents(),this.manageEventsBindings()),this.updateDisabledState(),this.updateAriaLabel(),this.calculateViewDimensions(),this.refocusPointerIfNeeded()}focusPointer(t){t!==F.Min&&t!==F.Max&&(t=F.Min),t===F.Min?this.minHandleElement.focus():this.range&&t===F.Max&&this.maxHandleElement.focus()}refocusPointerIfNeeded(){I.isNullOrUndefined(this.currentFocusPointer)||this.getPointerElement(this.currentFocusPointer).focusIfNeeded()}manageElementsStyle(){this.updateScale(),this.floorLabelElement.setAlwaysHide(this.viewOptions.showTicksValues||this.viewOptions.hideLimitLabels),this.ceilLabelElement.setAlwaysHide(this.viewOptions.showTicksValues||this.viewOptions.hideLimitLabels);const t=this.viewOptions.showTicksValues&&!this.intermediateTicks;this.minHandleLabelElement.setAlwaysHide(t||this.viewOptions.hidePointerLabels),this.maxHandleLabelElement.setAlwaysHide(t||!this.range||this.viewOptions.hidePointerLabels),this.combinedLabelElement.setAlwaysHide(t||!this.range||this.viewOptions.hidePointerLabels),this.selectionBarElement.setAlwaysHide(!this.range&&!this.viewOptions.showSelectionBar),this.leftOuterSelectionBarElement.setAlwaysHide(!this.range||!this.viewOptions.showOuterSelectionBars),this.rightOuterSelectionBarElement.setAlwaysHide(!this.range||!this.viewOptions.showOuterSelectionBars),this.fullBarTransparentClass=this.range&&this.viewOptions.showOuterSelectionBars,this.selectionBarDraggableClass=this.viewOptions.draggableRange&&!this.viewOptions.onlyBindHandles,this.ticksUnderValuesClass=this.intermediateTicks&&this.options.showTicksValues,this.sliderElementVerticalClass!==this.viewOptions.vertical&&(this.updateVerticalState(),setTimeout(()=>{this.resetSlider()})),this.sliderElementAnimateClass!==this.viewOptions.animate&&setTimeout(()=>{this.sliderElementAnimateClass=this.viewOptions.animate}),this.updateRotate()}manageEventsBindings(){this.viewOptions.disabled||this.viewOptions.readOnly?this.unbindEvents():this.bindEvents()}updateDisabledState(){this.sliderElementDisabledAttr=this.viewOptions.disabled?"disabled":null}updateAriaLabel(){this.sliderElementAriaLabel=this.viewOptions.ariaLabel||"nxg-slider"}updateVerticalState(){this.sliderElementVerticalClass=this.viewOptions.vertical;for(const t of this.getAllSliderElements())I.isNullOrUndefined(t)||t.setVertical(this.viewOptions.vertical)}updateScale(){for(const t of this.getAllSliderElements())t.setScale(this.viewOptions.scale)}updateRotate(){for(const t of this.getAllSliderElements())t.setRotate(this.viewOptions.rotate)}getAllSliderElements(){return[this.leftOuterSelectionBarElement,this.rightOuterSelectionBarElement,this.fullBarElement,this.selectionBarElement,this.minHandleElement,this.maxHandleElement,this.floorLabelElement,this.ceilLabelElement,this.minHandleLabelElement,this.maxHandleLabelElement,this.combinedLabelElement,this.ticksElement]}initHandles(){this.updateLowHandle(this.valueToPosition(this.viewLowValue)),this.range&&this.updateHighHandle(this.valueToPosition(this.viewHighValue)),this.updateSelectionBar(),this.range&&this.updateCombinedLabel(),this.updateTicksScale()}addAccessibility(){this.updateAriaAttributes(),this.minHandleElement.role="slider",this.minHandleElement.tabindex=!this.viewOptions.keyboardSupport||this.viewOptions.readOnly||this.viewOptions.disabled?"":"0",this.minHandleElement.ariaOrientation=this.viewOptions.vertical||0!==this.viewOptions.rotate?"vertical":"horizontal",I.isNullOrUndefined(this.viewOptions.ariaLabel)?I.isNullOrUndefined(this.viewOptions.ariaLabelledBy)||(this.minHandleElement.ariaLabelledBy=this.viewOptions.ariaLabelledBy):this.minHandleElement.ariaLabel=this.viewOptions.ariaLabel,this.range&&(this.maxHandleElement.role="slider",this.maxHandleElement.tabindex=!this.viewOptions.keyboardSupport||this.viewOptions.readOnly||this.viewOptions.disabled?"":"0",this.maxHandleElement.ariaOrientation=this.viewOptions.vertical||0!==this.viewOptions.rotate?"vertical":"horizontal",I.isNullOrUndefined(this.viewOptions.ariaLabelHigh)?I.isNullOrUndefined(this.viewOptions.ariaLabelledByHigh)||(this.maxHandleElement.ariaLabelledBy=this.viewOptions.ariaLabelledByHigh):this.maxHandleElement.ariaLabel=this.viewOptions.ariaLabelHigh)}updateAriaAttributes(){this.minHandleElement.ariaValueNow=(+this.value).toString(),this.minHandleElement.ariaValueText=this.viewOptions.translate(+this.value,En.Low),this.minHandleElement.ariaValueMin=this.viewOptions.floor.toString(),this.minHandleElement.ariaValueMax=this.viewOptions.ceil.toString(),this.range&&(this.maxHandleElement.ariaValueNow=(+this.highValue).toString(),this.maxHandleElement.ariaValueText=this.viewOptions.translate(+this.highValue,En.High),this.maxHandleElement.ariaValueMin=this.viewOptions.floor.toString(),this.maxHandleElement.ariaValueMax=this.viewOptions.ceil.toString())}calculateViewDimensions(){I.isNullOrUndefined(this.viewOptions.handleDimension)?this.minHandleElement.calculateDimension():this.minHandleElement.setDimension(this.viewOptions.handleDimension);const t=this.minHandleElement.dimension;this.handleHalfDimension=t/2,I.isNullOrUndefined(this.viewOptions.barDimension)?this.fullBarElement.calculateDimension():this.fullBarElement.setDimension(this.viewOptions.barDimension),this.maxHandlePosition=this.fullBarElement.dimension-t,this.initHasRun&&(this.updateFloorLabel(),this.updateCeilLabel(),this.initHandles())}calculateViewDimensionsAndDetectChanges(){this.calculateViewDimensions(),this.isRefDestroyed()||this.changeDetectionRef.detectChanges()}isRefDestroyed(){return this.changeDetectionRef.destroyed}updateTicksScale(){if(!this.viewOptions.showTicks&&this.sliderElementWithLegendClass)return void setTimeout(()=>{this.sliderElementWithLegendClass=!1});const t=I.isNullOrUndefined(this.viewOptions.ticksArray)?this.getTicksArray():this.viewOptions.ticksArray,i=this.viewOptions.vertical?"translateY":"translateX";this.viewOptions.rightToLeft&&t.reverse();const o=I.isNullOrUndefined(this.viewOptions.tickValueStep)?I.isNullOrUndefined(this.viewOptions.tickStep)?this.viewOptions.step:this.viewOptions.tickStep:this.viewOptions.tickValueStep;let r=!1;const s=t.map(a=>{let l=this.valueToPosition(a);this.viewOptions.vertical&&(l=this.maxHandlePosition-l);const c=i+"("+Math.round(l)+"px)",u=new PB;u.selected=this.isTickSelected(a),u.style={"-webkit-transform":c,"-moz-transform":c,"-o-transform":c,"-ms-transform":c,transform:c},u.selected&&!I.isNullOrUndefined(this.viewOptions.getSelectionBarColor)&&(u.style["background-color"]=this.getSelectionBarColor()),!u.selected&&!I.isNullOrUndefined(this.viewOptions.getTickColor)&&(u.style["background-color"]=this.getTickColor(a)),I.isNullOrUndefined(this.viewOptions.ticksTooltip)||(u.tooltip=this.viewOptions.ticksTooltip(a),u.tooltipPlacement=this.viewOptions.vertical?"right":"top"),this.viewOptions.showTicksValues&&!I.isNullOrUndefined(o)&&He.isModuloWithinPrecisionLimit(a,o,this.viewOptions.precisionLimit)&&(u.value=this.getDisplayValue(a,En.TickValue),I.isNullOrUndefined(this.viewOptions.ticksValuesTooltip)||(u.valueTooltip=this.viewOptions.ticksValuesTooltip(a),u.valueTooltipPlacement=this.viewOptions.vertical?"right":"top"));let d=null;if(I.isNullOrUndefined(this.viewOptions.stepsArray))I.isNullOrUndefined(this.viewOptions.getLegend)||(d=this.viewOptions.getLegend(a));else{const p=this.viewOptions.stepsArray[a];I.isNullOrUndefined(this.viewOptions.getStepLegend)?I.isNullOrUndefined(p)||(d=p.legend):d=this.viewOptions.getStepLegend(p)}return I.isNullOrUndefined(d)||(u.legend=d,r=!0),u});if(this.sliderElementWithLegendClass!==r&&setTimeout(()=>{this.sliderElementWithLegendClass=r}),I.isNullOrUndefined(this.ticks)||this.ticks.length!==s.length)this.ticks=s,this.isRefDestroyed()||this.changeDetectionRef.detectChanges();else for(let a=0;a<s.length;++a)Object.assign(this.ticks[a],s[a])}getTicksArray(){if(!this.viewOptions.showTicks)return[];const t=I.isNullOrUndefined(this.viewOptions.tickStep)?this.viewOptions.step:this.viewOptions.tickStep,i=[],o=1+Math.floor(He.roundToPrecisionLimit(Math.abs(this.viewOptions.ceil-this.viewOptions.floor)/t,this.viewOptions.precisionLimit));for(let r=0;r<o;++r)i.push(He.roundToPrecisionLimit(this.viewOptions.floor+t*r,this.viewOptions.precisionLimit));return i}isTickSelected(t){if(!this.range)if(I.isNullOrUndefined(this.viewOptions.showSelectionBarFromValue)){if(this.viewOptions.showSelectionBarEnd){if(t>=this.viewLowValue)return!0}else if(this.viewOptions.showSelectionBar&&t<=this.viewLowValue)return!0}else{const i=this.viewOptions.showSelectionBarFromValue;if(this.viewLowValue>i&&t>=i&&t<=this.viewLowValue)return!0;if(this.viewLowValue<i&&t<=i&&t>=this.viewLowValue)return!0}return!!(this.range&&t>=this.viewLowValue&&t<=this.viewHighValue)}updateFloorLabel(){this.floorLabelElement.alwaysHide||(this.floorLabelElement.setValue(this.getDisplayValue(this.viewOptions.floor,En.Floor)),this.floorLabelElement.calculateDimension(),this.floorLabelElement.setPosition(this.viewOptions.rightToLeft?this.fullBarElement.dimension-this.floorLabelElement.dimension:0))}updateCeilLabel(){this.ceilLabelElement.alwaysHide||(this.ceilLabelElement.setValue(this.getDisplayValue(this.viewOptions.ceil,En.Ceil)),this.ceilLabelElement.calculateDimension(),this.ceilLabelElement.setPosition(this.viewOptions.rightToLeft?0:this.fullBarElement.dimension-this.ceilLabelElement.dimension))}updateHandles(t,i){t===F.Min?this.updateLowHandle(i):t===F.Max&&this.updateHighHandle(i),this.updateSelectionBar(),this.updateTicksScale(),this.range&&this.updateCombinedLabel()}getHandleLabelPos(t,i){const o=t===F.Min?this.minHandleLabelElement.dimension:this.maxHandleLabelElement.dimension,r=i-o/2+this.handleHalfDimension,s=this.fullBarElement.dimension-o;return this.viewOptions.boundPointerLabels?this.viewOptions.rightToLeft&&t===F.Min||!this.viewOptions.rightToLeft&&t===F.Max?Math.min(r,s):Math.min(Math.max(r,0),s):r}updateLowHandle(t){this.minHandleElement.setPosition(t),this.minHandleLabelElement.setValue(this.getDisplayValue(this.viewLowValue,En.Low)),this.minHandleLabelElement.setPosition(this.getHandleLabelPos(F.Min,t)),I.isNullOrUndefined(this.viewOptions.getPointerColor)||(this.minPointerStyle={backgroundColor:this.getPointerColor(F.Min)}),this.viewOptions.autoHideLimitLabels&&this.updateFloorAndCeilLabelsVisibility()}updateHighHandle(t){this.maxHandleElement.setPosition(t),this.maxHandleLabelElement.setValue(this.getDisplayValue(this.viewHighValue,En.High)),this.maxHandleLabelElement.setPosition(this.getHandleLabelPos(F.Max,t)),I.isNullOrUndefined(this.viewOptions.getPointerColor)||(this.maxPointerStyle={backgroundColor:this.getPointerColor(F.Max)}),this.viewOptions.autoHideLimitLabels&&this.updateFloorAndCeilLabelsVisibility()}updateFloorAndCeilLabelsVisibility(){if(this.viewOptions.hidePointerLabels)return;let t=!1,i=!1;const o=this.isLabelBelowFloorLabel(this.minHandleLabelElement),r=this.isLabelAboveCeilLabel(this.minHandleLabelElement),s=this.isLabelAboveCeilLabel(this.maxHandleLabelElement),a=this.isLabelBelowFloorLabel(this.combinedLabelElement),l=this.isLabelAboveCeilLabel(this.combinedLabelElement);if(o?(t=!0,this.floorLabelElement.hide()):(t=!1,this.floorLabelElement.show()),r?(i=!0,this.ceilLabelElement.hide()):(i=!1,this.ceilLabelElement.show()),this.range){const c=this.combinedLabelElement.isVisible()?l:s,u=this.combinedLabelElement.isVisible()?a:o;c?this.ceilLabelElement.hide():i||this.ceilLabelElement.show(),u?this.floorLabelElement.hide():t||this.floorLabelElement.show()}}isLabelBelowFloorLabel(t){const i=t.position,r=this.floorLabelElement.position;return this.viewOptions.rightToLeft?i+t.dimension>=r-2:i<=r+this.floorLabelElement.dimension+2}isLabelAboveCeilLabel(t){const i=t.position,r=this.ceilLabelElement.position;return this.viewOptions.rightToLeft?i<=r+this.ceilLabelElement.dimension+2:i+t.dimension>=r-2}updateSelectionBar(){let t=0,i=0;const o=this.viewOptions.rightToLeft?!this.viewOptions.showSelectionBarEnd:this.viewOptions.showSelectionBarEnd,r=this.viewOptions.rightToLeft?this.maxHandleElement.position+this.handleHalfDimension:this.minHandleElement.position+this.handleHalfDimension;if(this.range)i=Math.abs(this.maxHandleElement.position-this.minHandleElement.position),t=r;else if(I.isNullOrUndefined(this.viewOptions.showSelectionBarFromValue))o?(i=Math.ceil(Math.abs(this.maxHandlePosition-this.minHandleElement.position)+this.handleHalfDimension),t=Math.floor(this.minHandleElement.position+this.handleHalfDimension)):(i=this.minHandleElement.position+this.handleHalfDimension,t=0);else{const s=this.viewOptions.showSelectionBarFromValue,a=this.valueToPosition(s);(this.viewOptions.rightToLeft?this.viewLowValue<=s:this.viewLowValue>s)?(i=this.minHandleElement.position-a,t=a+this.handleHalfDimension):(i=a-this.minHandleElement.position,t=this.minHandleElement.position+this.handleHalfDimension)}if(this.selectionBarElement.setDimension(i),this.selectionBarElement.setPosition(t),this.range&&this.viewOptions.showOuterSelectionBars&&(this.viewOptions.rightToLeft?(this.rightOuterSelectionBarElement.setDimension(t),this.rightOuterSelectionBarElement.setPosition(0),this.fullBarElement.calculateDimension(),this.leftOuterSelectionBarElement.setDimension(this.fullBarElement.dimension-(t+i)),this.leftOuterSelectionBarElement.setPosition(t+i)):(this.leftOuterSelectionBarElement.setDimension(t),this.leftOuterSelectionBarElement.setPosition(0),this.fullBarElement.calculateDimension(),this.rightOuterSelectionBarElement.setDimension(this.fullBarElement.dimension-(t+i)),this.rightOuterSelectionBarElement.setPosition(t+i))),I.isNullOrUndefined(this.viewOptions.getSelectionBarColor)){if(!I.isNullOrUndefined(this.viewOptions.selectionBarGradient)){const s=I.isNullOrUndefined(this.viewOptions.showSelectionBarFromValue)?0:this.valueToPosition(this.viewOptions.showSelectionBarFromValue),a=s-t>0&&!o||s-t<=0&&o;this.barStyle={backgroundImage:"linear-gradient(to "+(this.viewOptions.vertical?a?"bottom":"top":a?"left":"right")+", "+this.viewOptions.selectionBarGradient.from+" 0%,"+this.viewOptions.selectionBarGradient.to+" 100%)"},this.viewOptions.vertical?(this.barStyle.backgroundPosition="center "+(s+i+t+(a?-this.handleHalfDimension:0))+"px",this.barStyle.backgroundSize="100% "+(this.fullBarElement.dimension-this.handleHalfDimension)+"px"):(this.barStyle.backgroundPosition=s-t+(a?this.handleHalfDimension:0)+"px center",this.barStyle.backgroundSize=this.fullBarElement.dimension-this.handleHalfDimension+"px 100%")}}else{const s=this.getSelectionBarColor();this.barStyle={backgroundColor:s}}}getSelectionBarColor(){return this.range?this.viewOptions.getSelectionBarColor(this.value,this.highValue):this.viewOptions.getSelectionBarColor(this.value)}getPointerColor(t){return this.viewOptions.getPointerColor(t===F.Max?this.highValue:this.value,t)}getTickColor(t){return this.viewOptions.getTickColor(t)}updateCombinedLabel(){let t=null;if(t=this.viewOptions.rightToLeft?this.minHandleLabelElement.position-this.minHandleLabelElement.dimension-10<=this.maxHandleLabelElement.position:this.minHandleLabelElement.position+this.minHandleLabelElement.dimension+10>=this.maxHandleLabelElement.position,t){const i=this.getDisplayValue(this.viewLowValue,En.Low),o=this.getDisplayValue(this.viewHighValue,En.High),r=this.viewOptions.rightToLeft?this.viewOptions.combineLabels(o,i):this.viewOptions.combineLabels(i,o);this.combinedLabelElement.setValue(r);const s=this.viewOptions.boundPointerLabels?Math.min(Math.max(this.selectionBarElement.position+this.selectionBarElement.dimension/2-this.combinedLabelElement.dimension/2,0),this.fullBarElement.dimension-this.combinedLabelElement.dimension):this.selectionBarElement.position+this.selectionBarElement.dimension/2-this.combinedLabelElement.dimension/2;this.combinedLabelElement.setPosition(s),this.minHandleLabelElement.hide(),this.maxHandleLabelElement.hide(),this.combinedLabelElement.show()}else this.updateHighHandle(this.valueToPosition(this.viewHighValue)),this.updateLowHandle(this.valueToPosition(this.viewLowValue)),this.maxHandleLabelElement.show(),this.minHandleLabelElement.show(),this.combinedLabelElement.hide();this.viewOptions.autoHideLimitLabels&&this.updateFloorAndCeilLabelsVisibility()}getDisplayValue(t,i){return!I.isNullOrUndefined(this.viewOptions.stepsArray)&&!this.viewOptions.bindIndexForStepsArray&&(t=this.getStepValue(t)),this.viewOptions.translate(t,i)}roundStep(t,i){const o=I.isNullOrUndefined(i)?this.viewOptions.step:i;let r=He.roundToPrecisionLimit((t-this.viewOptions.floor)/o,this.viewOptions.precisionLimit);return r=Math.round(r)*o,He.roundToPrecisionLimit(this.viewOptions.floor+r,this.viewOptions.precisionLimit)}valueToPosition(t){let i=I.linearValueToPosition;I.isNullOrUndefined(this.viewOptions.customValueToPosition)?this.viewOptions.logScale&&(i=I.logValueToPosition):i=this.viewOptions.customValueToPosition;let o=i(t=He.clampToRange(t,this.viewOptions.floor,this.viewOptions.ceil),this.viewOptions.floor,this.viewOptions.ceil);return I.isNullOrUndefined(o)&&(o=0),this.viewOptions.rightToLeft&&(o=1-o),o*this.maxHandlePosition}positionToValue(t){let i=t/this.maxHandlePosition;this.viewOptions.rightToLeft&&(i=1-i);let o=I.linearPositionToValue;I.isNullOrUndefined(this.viewOptions.customPositionToValue)?this.viewOptions.logScale&&(o=I.logPositionToValue):o=this.viewOptions.customPositionToValue;const r=o(i,this.viewOptions.floor,this.viewOptions.ceil);return I.isNullOrUndefined(r)?0:r}getEventXY(t,i){if(t instanceof MouseEvent)return this.viewOptions.vertical||0!==this.viewOptions.rotate?t.clientY:t.clientX;let o=0;const r=t.touches;if(!I.isNullOrUndefined(i))for(let s=0;s<r.length;s++)if(r[s].identifier===i){o=s;break}return this.viewOptions.vertical||0!==this.viewOptions.rotate?r[o].clientY:r[o].clientX}getEventPosition(t,i){const o=this.elementRef.nativeElement.getBoundingClientRect(),r=this.viewOptions.vertical||0!==this.viewOptions.rotate?o.bottom:o.left;let s=0;return s=this.viewOptions.vertical||0!==this.viewOptions.rotate?-this.getEventXY(t,i)+r:this.getEventXY(t,i)-r,s*this.viewOptions.scale-this.handleHalfDimension}getNearestHandle(t){if(!this.range)return F.Min;const i=this.getEventPosition(t),o=Math.abs(i-this.minHandleElement.position),r=Math.abs(i-this.maxHandleElement.position);return o<r?F.Min:o>r?F.Max:this.viewOptions.rightToLeft?i>this.minHandleElement.position?F.Min:F.Max:i<this.minHandleElement.position?F.Min:F.Max}bindEvents(){const t=this.viewOptions.draggableRange;this.viewOptions.onlyBindHandles||this.selectionBarElement.on("mousedown",i=>this.onBarStart(null,t,i,!0,!0,!0)),this.viewOptions.draggableRangeOnly?(this.minHandleElement.on("mousedown",i=>this.onBarStart(F.Min,t,i,!0,!0)),this.maxHandleElement.on("mousedown",i=>this.onBarStart(F.Max,t,i,!0,!0))):(this.minHandleElement.on("mousedown",i=>this.onStart(F.Min,i,!0,!0)),this.range&&this.maxHandleElement.on("mousedown",i=>this.onStart(F.Max,i,!0,!0)),this.viewOptions.onlyBindHandles||(this.fullBarElement.on("mousedown",i=>this.onStart(null,i,!0,!0,!0)),this.ticksElement.on("mousedown",i=>this.onStart(null,i,!0,!0,!0,!0)))),this.viewOptions.onlyBindHandles||this.selectionBarElement.onPassive("touchstart",i=>this.onBarStart(null,t,i,!0,!0,!0)),this.viewOptions.draggableRangeOnly?(this.minHandleElement.onPassive("touchstart",i=>this.onBarStart(F.Min,t,i,!0,!0)),this.maxHandleElement.onPassive("touchstart",i=>this.onBarStart(F.Max,t,i,!0,!0))):(this.minHandleElement.onPassive("touchstart",i=>this.onStart(F.Min,i,!0,!0)),this.range&&this.maxHandleElement.onPassive("touchstart",i=>this.onStart(F.Max,i,!0,!0)),this.viewOptions.onlyBindHandles||(this.fullBarElement.onPassive("touchstart",i=>this.onStart(null,i,!0,!0,!0)),this.ticksElement.onPassive("touchstart",i=>this.onStart(null,i,!1,!1,!0,!0)))),this.viewOptions.keyboardSupport&&(this.minHandleElement.on("focus",()=>this.onPointerFocus(F.Min)),this.range&&this.maxHandleElement.on("focus",()=>this.onPointerFocus(F.Max)))}getOptionsInfluencingEventBindings(t){return[t.disabled,t.readOnly,t.draggableRange,t.draggableRangeOnly,t.onlyBindHandles,t.keyboardSupport]}unbindEvents(){this.unsubscribeOnMove(),this.unsubscribeOnEnd();for(const t of this.getAllSliderElements())I.isNullOrUndefined(t)||t.off()}onBarStart(t,i,o,r,s,a,l){i?this.onDragStart(t,o,r,s):this.onStart(t,o,r,s,a,l)}onStart(t,i,o,r,s,a){i.stopPropagation(),!gi.isTouchEvent(i)&&!yM&&i.preventDefault(),this.moving=!1,this.calculateViewDimensions(),I.isNullOrUndefined(t)&&(t=this.getNearestHandle(i)),this.currentTrackingPointer=t;const l=this.getPointerElement(t);if(l.active=!0,this.preStartHandleValue=this.getCurrentTrackingValue(),this.viewOptions.keyboardSupport&&l.focus(),o){this.unsubscribeOnMove();const c=u=>this.dragging.active?this.onDragMove(u):this.onMove(u);this.onMoveEventListener=gi.isTouchEvent(i)?this.eventListenerHelper.attachPassiveEventListener(document,"touchmove",c):this.eventListenerHelper.attachEventListener(document,"mousemove",c)}if(r){this.unsubscribeOnEnd();const c=u=>this.onEnd(u);this.onEndEventListener=gi.isTouchEvent(i)?this.eventListenerHelper.attachPassiveEventListener(document,"touchend",c):this.eventListenerHelper.attachEventListener(document,"mouseup",c)}this.userChangeStart.emit(this.getChangeContext()),gi.isTouchEvent(i)&&!I.isNullOrUndefined(i.changedTouches)&&I.isNullOrUndefined(this.touchId)&&(this.touchId=i.changedTouches[0].identifier),s&&this.onMove(i,!0),a&&this.onEnd(i)}onMove(t,i){let o=null;if(gi.isTouchEvent(t)){const c=t.changedTouches;for(let u=0;u<c.length;u++)if(c[u].identifier===this.touchId){o=c[u];break}if(I.isNullOrUndefined(o))return}this.viewOptions.animate&&!this.viewOptions.animateOnMove&&this.moving&&(this.sliderElementAnimateClass=!1),this.moving=!0;const r=I.isNullOrUndefined(o)?this.getEventPosition(t):this.getEventPosition(t,o.identifier);let s;r<=0?s=this.viewOptions.rightToLeft?this.viewOptions.ceil:this.viewOptions.floor:r>=this.maxHandlePosition?s=this.viewOptions.rightToLeft?this.viewOptions.floor:this.viewOptions.ceil:(s=this.positionToValue(r),s=i&&!I.isNullOrUndefined(this.viewOptions.tickStep)?this.roundStep(s,this.viewOptions.tickStep):this.roundStep(s)),this.positionTrackingHandle(s)}forceEnd(t=!1){this.moving=!1,this.viewOptions.animate&&(this.sliderElementAnimateClass=!0),t&&(this.sliderElementAnimateClass=!1,setTimeout(()=>{this.sliderElementAnimateClass=this.viewOptions.animate})),this.touchId=null,this.viewOptions.keyboardSupport||(this.minHandleElement.active=!1,this.maxHandleElement.active=!1,this.currentTrackingPointer=null),this.dragging.active=!1,this.unsubscribeOnMove(),this.unsubscribeOnEnd(),this.userChangeEnd.emit(this.getChangeContext())}onEnd(t){gi.isTouchEvent(t)&&t.changedTouches[0].identifier!==this.touchId||this.forceEnd()}onPointerFocus(t){const i=this.getPointerElement(t);i.on("blur",()=>this.onPointerBlur(i)),i.on("keydown",o=>this.onKeyboardEvent(o)),i.on("keyup",()=>this.onKeyUp()),i.active=!0,this.currentTrackingPointer=t,this.currentFocusPointer=t,this.firstKeyDown=!0}onKeyUp(){this.firstKeyDown=!0,this.userChangeEnd.emit(this.getChangeContext())}onPointerBlur(t){t.off("blur"),t.off("keydown"),t.off("keyup"),t.active=!1,I.isNullOrUndefined(this.touchId)&&(this.currentTrackingPointer=null,this.currentFocusPointer=null)}getKeyActions(t){const i=this.viewOptions.ceil-this.viewOptions.floor;let o=t+this.viewOptions.step,r=t-this.viewOptions.step,s=t+i/10,a=t-i/10;this.viewOptions.reversedControls&&(o=t-this.viewOptions.step,r=t+this.viewOptions.step,s=t-i/10,a=t+i/10);const l={UP:o,DOWN:r,LEFT:r,RIGHT:o,PAGEUP:s,PAGEDOWN:a,HOME:this.viewOptions.reversedControls?this.viewOptions.ceil:this.viewOptions.floor,END:this.viewOptions.reversedControls?this.viewOptions.floor:this.viewOptions.ceil};return this.viewOptions.rightToLeft&&(l.LEFT=o,l.RIGHT=r,(this.viewOptions.vertical||0!==this.viewOptions.rotate)&&(l.UP=r,l.DOWN=o)),l}onKeyboardEvent(t){const i=this.getCurrentTrackingValue(),o=I.isNullOrUndefined(t.keyCode)?t.which:t.keyCode,l=this.getKeyActions(i)[{38:"UP",40:"DOWN",37:"LEFT",39:"RIGHT",33:"PAGEUP",34:"PAGEDOWN",36:"HOME",35:"END"}[o]];if(I.isNullOrUndefined(l)||I.isNullOrUndefined(this.currentTrackingPointer))return;t.preventDefault(),this.firstKeyDown&&(this.firstKeyDown=!1,this.userChangeStart.emit(this.getChangeContext()));const c=He.clampToRange(l,this.viewOptions.floor,this.viewOptions.ceil),u=this.roundStep(c);if(this.viewOptions.draggableRangeOnly){const d=this.viewHighValue-this.viewLowValue;let p,h;this.currentTrackingPointer===F.Min?(p=u,h=u+d,h>this.viewOptions.ceil&&(h=this.viewOptions.ceil,p=h-d)):this.currentTrackingPointer===F.Max&&(h=u,p=u-d,p<this.viewOptions.floor&&(p=this.viewOptions.floor,h=p+d)),this.positionTrackingBar(p,h)}else this.positionTrackingHandle(u)}onDragStart(t,i,o,r){const s=this.getEventPosition(i);this.dragging=new TM,this.dragging.active=!0,this.dragging.value=this.positionToValue(s),this.dragging.difference=this.viewHighValue-this.viewLowValue,this.dragging.lowLimit=this.viewOptions.rightToLeft?this.minHandleElement.position-s:s-this.minHandleElement.position,this.dragging.highLimit=this.viewOptions.rightToLeft?s-this.maxHandleElement.position:this.maxHandleElement.position-s,this.onStart(t,i,o,r)}getMinValue(t,i,o){const r=this.viewOptions.rightToLeft;let s=null;return s=i?o?r?this.viewOptions.floor:this.viewOptions.ceil-this.dragging.difference:r?this.viewOptions.ceil-this.dragging.difference:this.viewOptions.floor:this.positionToValue(r?t+this.dragging.lowLimit:t-this.dragging.lowLimit),this.roundStep(s)}getMaxValue(t,i,o){const r=this.viewOptions.rightToLeft;let s=null;return s=i?o?r?this.viewOptions.floor+this.dragging.difference:this.viewOptions.ceil:r?this.viewOptions.ceil:this.viewOptions.floor+this.dragging.difference:r?this.positionToValue(t+this.dragging.lowLimit)+this.dragging.difference:this.positionToValue(t-this.dragging.lowLimit)+this.dragging.difference,this.roundStep(s)}onDragMove(t){const i=this.getEventPosition(t);let o,r,s,a;this.viewOptions.animate&&!this.viewOptions.animateOnMove&&this.moving&&(this.sliderElementAnimateClass=!1),this.moving=!0,this.viewOptions.rightToLeft?(o=this.dragging.lowLimit,r=this.dragging.highLimit,s=this.maxHandleElement,a=this.minHandleElement):(o=this.dragging.highLimit,r=this.dragging.lowLimit,s=this.minHandleElement,a=this.maxHandleElement);const c=i>=this.maxHandlePosition-o;let u,d;if(i<=r){if(0===s.position)return;u=this.getMinValue(i,!0,!1),d=this.getMaxValue(i,!0,!1)}else if(c){if(a.position===this.maxHandlePosition)return;d=this.getMaxValue(i,!0,!0),u=this.getMinValue(i,!0,!0)}else u=this.getMinValue(i,!1,!1),d=this.getMaxValue(i,!1,!1);this.positionTrackingBar(u,d)}positionTrackingBar(t,i){!I.isNullOrUndefined(this.viewOptions.minLimit)&&t<this.viewOptions.minLimit&&(i=He.roundToPrecisionLimit((t=this.viewOptions.minLimit)+this.dragging.difference,this.viewOptions.precisionLimit)),!I.isNullOrUndefined(this.viewOptions.maxLimit)&&i>this.viewOptions.maxLimit&&(t=He.roundToPrecisionLimit((i=this.viewOptions.maxLimit)-this.dragging.difference,this.viewOptions.precisionLimit)),this.viewLowValue=t,this.viewHighValue=i,this.applyViewChange(),this.updateHandles(F.Min,this.valueToPosition(t)),this.updateHandles(F.Max,this.valueToPosition(i))}positionTrackingHandle(t){t=this.applyMinMaxLimit(t),this.range&&(this.viewOptions.pushRange?t=this.applyPushRange(t):(this.viewOptions.noSwitching&&(this.currentTrackingPointer===F.Min&&t>this.viewHighValue?t=this.applyMinMaxRange(this.viewHighValue):this.currentTrackingPointer===F.Max&&t<this.viewLowValue&&(t=this.applyMinMaxRange(this.viewLowValue))),t=this.applyMinMaxRange(t),this.currentTrackingPointer===F.Min&&t>this.viewHighValue?(this.viewLowValue=this.viewHighValue,this.applyViewChange(),this.updateHandles(F.Min,this.maxHandleElement.position),this.updateAriaAttributes(),this.currentTrackingPointer=F.Max,this.minHandleElement.active=!1,this.maxHandleElement.active=!0,this.viewOptions.keyboardSupport&&this.maxHandleElement.focus()):this.currentTrackingPointer===F.Max&&t<this.viewLowValue&&(this.viewHighValue=this.viewLowValue,this.applyViewChange(),this.updateHandles(F.Max,this.minHandleElement.position),this.updateAriaAttributes(),this.currentTrackingPointer=F.Min,this.maxHandleElement.active=!1,this.minHandleElement.active=!0,this.viewOptions.keyboardSupport&&this.minHandleElement.focus()))),this.getCurrentTrackingValue()!==t&&(this.currentTrackingPointer===F.Min?(this.viewLowValue=t,this.applyViewChange()):this.currentTrackingPointer===F.Max&&(this.viewHighValue=t,this.applyViewChange()),this.updateHandles(this.currentTrackingPointer,this.valueToPosition(t)),this.updateAriaAttributes())}applyMinMaxLimit(t){return!I.isNullOrUndefined(this.viewOptions.minLimit)&&t<this.viewOptions.minLimit?this.viewOptions.minLimit:!I.isNullOrUndefined(this.viewOptions.maxLimit)&&t>this.viewOptions.maxLimit?this.viewOptions.maxLimit:t}applyMinMaxRange(t){const o=Math.abs(t-(this.currentTrackingPointer===F.Min?this.viewHighValue:this.viewLowValue));if(!I.isNullOrUndefined(this.viewOptions.minRange)&&o<this.viewOptions.minRange){if(this.currentTrackingPointer===F.Min)return He.roundToPrecisionLimit(this.viewHighValue-this.viewOptions.minRange,this.viewOptions.precisionLimit);if(this.currentTrackingPointer===F.Max)return He.roundToPrecisionLimit(this.viewLowValue+this.viewOptions.minRange,this.viewOptions.precisionLimit)}if(!I.isNullOrUndefined(this.viewOptions.maxRange)&&o>this.viewOptions.maxRange){if(this.currentTrackingPointer===F.Min)return He.roundToPrecisionLimit(this.viewHighValue-this.viewOptions.maxRange,this.viewOptions.precisionLimit);if(this.currentTrackingPointer===F.Max)return He.roundToPrecisionLimit(this.viewLowValue+this.viewOptions.maxRange,this.viewOptions.precisionLimit)}return t}applyPushRange(t){const i=this.currentTrackingPointer===F.Min?this.viewHighValue-t:t-this.viewLowValue,o=I.isNullOrUndefined(this.viewOptions.minRange)?this.viewOptions.step:this.viewOptions.minRange,r=this.viewOptions.maxRange;return i<o?(this.currentTrackingPointer===F.Min?(this.viewHighValue=He.roundToPrecisionLimit(Math.min(t+o,this.viewOptions.ceil),this.viewOptions.precisionLimit),t=He.roundToPrecisionLimit(this.viewHighValue-o,this.viewOptions.precisionLimit),this.applyViewChange(),this.updateHandles(F.Max,this.valueToPosition(this.viewHighValue))):this.currentTrackingPointer===F.Max&&(this.viewLowValue=He.roundToPrecisionLimit(Math.max(t-o,this.viewOptions.floor),this.viewOptions.precisionLimit),t=He.roundToPrecisionLimit(this.viewLowValue+o,this.viewOptions.precisionLimit),this.applyViewChange(),this.updateHandles(F.Min,this.valueToPosition(this.viewLowValue))),this.updateAriaAttributes()):!I.isNullOrUndefined(r)&&i>r&&(this.currentTrackingPointer===F.Min?(this.viewHighValue=He.roundToPrecisionLimit(t+r,this.viewOptions.precisionLimit),this.applyViewChange(),this.updateHandles(F.Max,this.valueToPosition(this.viewHighValue))):this.currentTrackingPointer===F.Max&&(this.viewLowValue=He.roundToPrecisionLimit(t-r,this.viewOptions.precisionLimit),this.applyViewChange(),this.updateHandles(F.Min,this.valueToPosition(this.viewLowValue))),this.updateAriaAttributes()),t}getChangeContext(){const t=new RB;return t.pointerType=this.currentTrackingPointer,t.value=+this.value,this.range&&(t.highValue=+this.highValue),t}static \u0275fac=function(i){return new(i||e)(T(nn),T(ut),T(Zi),T(he),T(EM,8))};static \u0275cmp=on({type:e,selectors:[["ngx-slider"]],contentQueries:function(i,o,r){if(1&i&&RD(r,vB,5),2&i){let s;Mt(s=It())&&(o.tooltipTemplate=s.first)}},viewQuery:function(i,o){if(1&i&&(Lt(_B,5,mi),Lt(yB,5,mi),Lt(CB,5,mi),Lt(DB,5,mi),Lt(wB,5,Fp),Lt(bB,5,Fp),Lt(EB,5,_r),Lt(MB,5,_r),Lt(IB,5,_r),Lt(TB,5,_r),Lt(SB,5,_r),Lt(xB,5,mi)),2&i){let r;Mt(r=It())&&(o.leftOuterSelectionBarElement=r.first),Mt(r=It())&&(o.rightOuterSelectionBarElement=r.first),Mt(r=It())&&(o.fullBarElement=r.first),Mt(r=It())&&(o.selectionBarElement=r.first),Mt(r=It())&&(o.minHandleElement=r.first),Mt(r=It())&&(o.maxHandleElement=r.first),Mt(r=It())&&(o.floorLabelElement=r.first),Mt(r=It())&&(o.ceilLabelElement=r.first),Mt(r=It())&&(o.minHandleLabelElement=r.first),Mt(r=It())&&(o.maxHandleLabelElement=r.first),Mt(r=It())&&(o.combinedLabelElement=r.first),Mt(r=It())&&(o.ticksElement=r.first)}},hostVars:10,hostBindings:function(i,o){1&i&&z("resize",function(s){return o.onResize(s)},0,cl),2&i&&(ft("disabled",o.sliderElementDisabledAttr)("aria-label",o.sliderElementAriaLabel),Un("ngx-slider",o.sliderElementNgxSliderClass)("vertical",o.sliderElementVerticalClass)("animate",o.sliderElementAnimateClass)("with-legend",o.sliderElementWithLegendClass))},inputs:{value:"value",highValue:"highValue",options:"options",manualRefresh:"manualRefresh",triggerFocus:"triggerFocus",cancelUserChange:"cancelUserChange"},outputs:{valueChange:"valueChange",highValueChange:"highValueChange",userChangeStart:"userChangeStart",userChange:"userChange",userChangeEnd:"userChangeEnd"},standalone:!1,features:[Me([VB]),An],decls:29,vars:13,consts:[["leftOuterSelectionBar",""],["rightOuterSelectionBar",""],["fullBar",""],["selectionBar",""],["minHandle",""],["maxHandle",""],["floorLabel",""],["ceilLabel",""],["minHandleLabel",""],["maxHandleLabel",""],["combinedLabel",""],["ticksElement",""],["ngxSliderElement","",1,"ngx-slider-span","ngx-slider-bar-wrapper","ngx-slider-left-out-selection"],[1,"ngx-slider-span","ngx-slider-bar"],["ngxSliderElement","",1,"ngx-slider-span","ngx-slider-bar-wrapper","ngx-slider-right-out-selection"],["ngxSliderElement","",1,"ngx-slider-span","ngx-slider-bar-wrapper","ngx-slider-full-bar"],["ngxSliderElement","",1,"ngx-slider-span","ngx-slider-bar-wrapper","ngx-slider-selection-bar"],[1,"ngx-slider-span","ngx-slider-bar","ngx-slider-selection",3,"ngStyle"],["ngxSliderHandle","",1,"ngx-slider-span","ngx-slider-pointer","ngx-slider-pointer-min",3,"ngStyle"],["ngxSliderHandle","",1,"ngx-slider-span","ngx-slider-pointer","ngx-slider-pointer-max",3,"ngStyle"],["ngxSliderLabel","",1,"ngx-slider-span","ngx-slider-bubble","ngx-slider-limit","ngx-slider-floor"],["ngxSliderLabel","",1,"ngx-slider-span","ngx-slider-bubble","ngx-slider-limit","ngx-slider-ceil"],["ngxSliderLabel","",1,"ngx-slider-span","ngx-slider-bubble","ngx-slider-model-value"],["ngxSliderLabel","",1,"ngx-slider-span","ngx-slider-bubble","ngx-slider-model-high"],["ngxSliderLabel","",1,"ngx-slider-span","ngx-slider-bubble","ngx-slider-combined"],["ngxSliderElement","",1,"ngx-slider-ticks",3,"hidden"],["class","ngx-slider-tick",3,"ngClass","ngStyle",4,"ngFor","ngForOf"],[1,"ngx-slider-tick",3,"ngClass","ngStyle"],[3,"template","tooltip","placement"],["class","ngx-slider-span ngx-slider-tick-value",3,"template","tooltip","placement","content",4,"ngIf"],["class","ngx-slider-span ngx-slider-tick-legend",3,"innerText",4,"ngIf"],["class","ngx-slider-span ngx-slider-tick-legend",3,"innerHTML",4,"ngIf"],[1,"ngx-slider-span","ngx-slider-tick-value",3,"template","tooltip","placement","content"],[1,"ngx-slider-span","ngx-slider-tick-legend",3,"innerText"],[1,"ngx-slider-span","ngx-slider-tick-legend",3,"innerHTML"]],template:function(i,o){1&i&&(y(0,"span",12,0),N(2,"span",13),_(),y(3,"span",14,1),N(5,"span",13),_(),y(6,"span",15,2),N(8,"span",13),_(),y(9,"span",16,3),N(11,"span",17),_(),N(12,"span",18,4)(14,"span",19,5)(16,"span",20,6)(18,"span",21,7)(20,"span",22,8)(22,"span",23,9)(24,"span",24,10),y(26,"span",25,11),L(28,FB,5,10,"span",26),_()),2&i&&(f(6),Un("ngx-slider-transparent",o.fullBarTransparentClass),f(3),Un("ngx-slider-draggable",o.selectionBarDraggableClass),f(2),g("ngStyle",o.barStyle),f(),g("ngStyle",o.minPointerStyle),f(2),Vl("display",o.range?"inherit":"none"),g("ngStyle",o.maxPointerStyle),f(12),Un("ngx-slider-ticks-values-under",o.ticksUnderValuesClass),g("hidden",!o.showTicks),f(2),g("ngForOf",o.ticks))},dependencies:[hr,Ki,qn,Nb,mi,Fp,_r,LB],styles:['.ngx-slider{display:inline-block;position:relative;height:4px;width:100%;margin:35px 0 15px;vertical-align:middle;-webkit-user-select:none;user-select:none;touch-action:pan-y}  .ngx-slider.with-legend{margin-bottom:40px}  .ngx-slider[disabled]{cursor:not-allowed}  .ngx-slider[disabled] .ngx-slider-pointer{cursor:not-allowed;background-color:#d8e0f3}  .ngx-slider[disabled] .ngx-slider-draggable{cursor:not-allowed}  .ngx-slider[disabled] .ngx-slider-selection{background:#8b91a2}  .ngx-slider[disabled] .ngx-slider-tick{cursor:not-allowed}  .ngx-slider[disabled] .ngx-slider-tick.ngx-slider-selected{background:#8b91a2}  .ngx-slider .ngx-slider-span{white-space:nowrap;position:absolute;display:inline-block}  .ngx-slider .ngx-slider-base{width:100%;height:100%;padding:0}  .ngx-slider .ngx-slider-bar-wrapper{left:0;box-sizing:border-box;margin-top:-16px;padding-top:16px;width:100%;height:32px;z-index:1}  .ngx-slider .ngx-slider-draggable{cursor:move}  .ngx-slider .ngx-slider-bar{left:0;width:100%;height:4px;z-index:1;background:#d8e0f3;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}  .ngx-slider .ngx-slider-bar-wrapper.ngx-slider-transparent .ngx-slider-bar{background:transparent}  .ngx-slider .ngx-slider-bar-wrapper.ngx-slider-left-out-selection .ngx-slider-bar{background:#df002d}  .ngx-slider .ngx-slider-bar-wrapper.ngx-slider-right-out-selection .ngx-slider-bar{background:#03a688}  .ngx-slider .ngx-slider-selection{z-index:2;background:#0db9f0;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}  .ngx-slider .ngx-slider-pointer{cursor:pointer;width:32px;height:32px;top:-14px;background-color:#0db9f0;z-index:3;-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px}  .ngx-slider .ngx-slider-pointer:after{content:"";width:8px;height:8px;position:absolute;top:12px;left:12px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;background:#fff}  .ngx-slider .ngx-slider-pointer:hover:after{background-color:#fff}  .ngx-slider .ngx-slider-pointer.ngx-slider-active{z-index:4}  .ngx-slider .ngx-slider-pointer.ngx-slider-active:after{background-color:#451aff}  .ngx-slider .ngx-slider-bubble{cursor:default;bottom:16px;padding:1px 3px;color:#55637d;font-size:16px}  .ngx-slider .ngx-slider-bubble.ngx-slider-limit{color:#55637d}  .ngx-slider .ngx-slider-ticks{box-sizing:border-box;width:100%;height:0;position:absolute;left:0;top:-3px;margin:0;z-index:1;list-style:none}  .ngx-slider .ngx-slider-ticks-values-under .ngx-slider-tick-value{top:auto;bottom:-36px}  .ngx-slider .ngx-slider-tick{text-align:center;cursor:pointer;width:10px;height:10px;background:#d8e0f3;border-radius:50%;position:absolute;top:0;left:0;margin-left:11px}  .ngx-slider .ngx-slider-tick.ngx-slider-selected{background:#0db9f0}  .ngx-slider .ngx-slider-tick-value{position:absolute;top:-34px;transform:translate(-50%)}  .ngx-slider .ngx-slider-tick-legend{position:absolute;top:24px;transform:translate(-50%);max-width:50px;white-space:normal}  .ngx-slider.vertical{position:relative;width:4px;height:100%;margin:0 20px;padding:0;vertical-align:baseline;touch-action:pan-x}  .ngx-slider.vertical .ngx-slider-base{width:100%;height:100%;padding:0}  .ngx-slider.vertical .ngx-slider-bar-wrapper{top:auto;left:0;margin:0 0 0 -16px;padding:0 0 0 16px;height:100%;width:32px}  .ngx-slider.vertical .ngx-slider-bar{bottom:0;left:auto;width:4px;height:100%}  .ngx-slider.vertical .ngx-slider-pointer{left:-14px!important;top:auto;bottom:0}  .ngx-slider.vertical .ngx-slider-bubble{left:16px!important;bottom:0}  .ngx-slider.vertical .ngx-slider-ticks{height:100%;width:0;left:-3px;top:0;z-index:1}  .ngx-slider.vertical .ngx-slider-tick{vertical-align:middle;margin-left:auto;margin-top:11px}  .ngx-slider.vertical .ngx-slider-tick-value{left:24px;top:auto;transform:translateY(-28%)}  .ngx-slider.vertical .ngx-slider-tick-legend{top:auto;right:24px;transform:translateY(-28%);max-width:none;white-space:nowrap}  .ngx-slider.vertical .ngx-slider-ticks-values-under .ngx-slider-tick-value{bottom:auto;left:auto;right:24px}  .ngx-slider *{transition:none}  .ngx-slider.animate .ngx-slider-bar-wrapper{transition:all linear .3s}  .ngx-slider.animate .ngx-slider-selection{transition:background-color linear .3s}  .ngx-slider.animate .ngx-slider-pointer{transition:all linear .3s}  .ngx-slider.animate .ngx-slider-pointer:after{transition:all linear .3s}  .ngx-slider.animate .ngx-slider-bubble{transition:all linear .3s}  .ngx-slider.animate .ngx-slider-bubble.ngx-slider-limit{transition:opacity linear .3s}  .ngx-slider.animate .ngx-slider-bubble.ngx-slider-combined{transition:opacity linear .3s}  .ngx-slider.animate .ngx-slider-tick{transition:background-color linear .3s}']})}return e})(),HB=(()=>{class e{static \u0275fac=function(i){return new(i||e)};static \u0275mod=si({type:e});static \u0275inj=Tn({imports:[Fb]})}return e})();class NM{constructor(){this.riskHotspotsSettings=null,this.coverageInfoSettings=null}}class BB{constructor(){this.showLineCoverage=!0,this.showBranchCoverage=!0,this.showMethodCoverage=!0,this.showFullMethodCoverage=!0,this.visibleMetrics=[],this.groupingMaximum=0,this.grouping=0,this.historyComparisionDate="",this.historyComparisionType="",this.filter="",this.lineCoverageMin=0,this.lineCoverageMax=100,this.branchCoverageMin=0,this.branchCoverageMax=100,this.methodCoverageMin=0,this.methodCoverageMax=100,this.methodFullCoverageMin=0,this.methodFullCoverageMax=100,this.sortBy="name",this.sortOrder="asc",this.collapseStates=[]}}class jB{constructor(n){this.et="",this.et=n.et,this.cl=n.cl,this.ucl=n.ucl,this.cal=n.cal,this.tl=n.tl,this.lcq=n.lcq,this.cb=n.cb,this.tb=n.tb,this.bcq=n.bcq,this.cm=n.cm,this.fcm=n.fcm,this.tm=n.tm,this.mcq=n.mcq,this.mfcq=n.mfcq}get coverageRatioText(){return 0===this.tl?"-":this.cl+"/"+this.cal}get branchCoverageRatioText(){return 0===this.tb?"-":this.cb+"/"+this.tb}get methodCoverageRatioText(){return 0===this.tm?"-":this.cm+"/"+this.tm}get methodFullCoverageRatioText(){return 0===this.tm?"-":this.fcm+"/"+this.tm}}class Ht{static roundNumber(n){return Math.floor(n*Math.pow(10,Ht.maximumDecimalPlacesForCoverageQuotas))/Math.pow(10,Ht.maximumDecimalPlacesForCoverageQuotas)}static getNthOrLastIndexOf(n,t,i){let o=0,r=-1,s=-1;for(;o<i&&(s=n.indexOf(t,r+1),-1!==s);)r=s,o++;return r}}class OM{constructor(){this.name="",this.coveredLines=0,this.uncoveredLines=0,this.coverableLines=0,this.totalLines=0,this.coveredBranches=0,this.totalBranches=0,this.coveredMethods=0,this.fullyCoveredMethods=0,this.totalMethods=0}get coverage(){return 0===this.coverableLines?NaN:Ht.roundNumber(100*this.coveredLines/this.coverableLines)}get coveragePercentage(){return 0===this.coverableLines?"":this.coverage+"%"}get coverageRatioText(){return 0===this.coverableLines?"-":this.coveredLines+"/"+this.coverableLines}get branchCoverage(){return 0===this.totalBranches?NaN:Ht.roundNumber(100*this.coveredBranches/this.totalBranches)}get branchCoveragePercentage(){return 0===this.totalBranches?"":this.branchCoverage+"%"}get branchCoverageRatioText(){return 0===this.totalBranches?"-":this.coveredBranches+"/"+this.totalBranches}get methodCoverage(){return 0===this.totalMethods?NaN:Ht.roundNumber(100*this.coveredMethods/this.totalMethods)}get methodCoveragePercentage(){return 0===this.totalMethods?"":this.methodCoverage+"%"}get methodCoverageRatioText(){return 0===this.totalMethods?"-":this.coveredMethods+"/"+this.totalMethods}get methodFullCoverage(){return 0===this.totalMethods?NaN:Ht.roundNumber(100*this.fullyCoveredMethods/this.totalMethods)}get methodFullCoveragePercentage(){return 0===this.totalMethods?"":this.methodFullCoverage+"%"}get methodFullCoverageRatioText(){return 0===this.totalMethods?"-":this.fullyCoveredMethods+"/"+this.totalMethods}}class Rp extends OM{constructor(n,t){super(),this.reportPath="",this.lineCoverageHistory=[],this.branchCoverageHistory=[],this.methodCoverageHistory=[],this.methodFullCoverageHistory=[],this.historicCoverages=[],this.currentHistoricCoverage=null,this.name=n.name,this.reportPath=n.rp?n.rp+t:n.rp,this.coveredLines=n.cl,this.uncoveredLines=n.ucl,this.coverableLines=n.cal,this.totalLines=n.tl,this.coveredBranches=n.cb,this.totalBranches=n.tb,this.coveredMethods=n.cm,this.fullyCoveredMethods=n.fcm,this.totalMethods=n.tm,this.lineCoverageHistory=n.lch,this.branchCoverageHistory=n.bch,this.methodCoverageHistory=n.mch,this.methodFullCoverageHistory=n.mfch,n.hc.forEach(i=>{this.historicCoverages.push(new jB(i))}),this.metrics=n.metrics}get coverage(){return 0===this.coverableLines?NaN:Ht.roundNumber(100*this.coveredLines/this.coverableLines)}visible(n){if(""!==n.filter&&-1===this.name.toLowerCase().indexOf(n.filter.toLowerCase()))return!1;let t=this.coverage,i=t;if(t=Number.isNaN(t)?0:t,i=Number.isNaN(i)?100:i,n.lineCoverageMin>t||n.lineCoverageMax<i)return!1;let o=this.branchCoverage,r=o;if(o=Number.isNaN(o)?0:o,r=Number.isNaN(r)?100:r,n.branchCoverageMin>o||n.branchCoverageMax<r)return!1;let s=this.methodCoverage,a=s;if(s=Number.isNaN(s)?0:s,a=Number.isNaN(a)?100:a,n.methodCoverageMin>s||n.methodCoverageMax<a)return!1;let l=this.methodFullCoverage,c=l;if(l=Number.isNaN(l)?0:l,c=Number.isNaN(c)?100:c,n.methodFullCoverageMin>l||n.methodFullCoverageMax<c)return!1;if(""===n.historyComparisionType||null===this.currentHistoricCoverage)return!0;if("allChanges"===n.historyComparisionType){if(this.coveredLines===this.currentHistoricCoverage.cl&&this.uncoveredLines===this.currentHistoricCoverage.ucl&&this.coverableLines===this.currentHistoricCoverage.cal&&this.totalLines===this.currentHistoricCoverage.tl&&this.coveredBranches===this.currentHistoricCoverage.cb&&this.totalBranches===this.currentHistoricCoverage.tb&&this.coveredMethods===this.currentHistoricCoverage.cm&&this.fullyCoveredMethods===this.currentHistoricCoverage.fcm&&this.totalMethods===this.currentHistoricCoverage.tm)return!1}else if("lineCoverageIncreaseOnly"===n.historyComparisionType){let u=this.coverage;if(isNaN(u)||u<=this.currentHistoricCoverage.lcq)return!1}else if("lineCoverageDecreaseOnly"===n.historyComparisionType){let u=this.coverage;if(isNaN(u)||u>=this.currentHistoricCoverage.lcq)return!1}else if("branchCoverageIncreaseOnly"===n.historyComparisionType){let u=this.branchCoverage;if(isNaN(u)||u<=this.currentHistoricCoverage.bcq)return!1}else if("branchCoverageDecreaseOnly"===n.historyComparisionType){let u=this.branchCoverage;if(isNaN(u)||u>=this.currentHistoricCoverage.bcq)return!1}else if("methodCoverageIncreaseOnly"===n.historyComparisionType){let u=this.methodCoverage;if(isNaN(u)||u<=this.currentHistoricCoverage.mcq)return!1}else if("methodCoverageDecreaseOnly"===n.historyComparisionType){let u=this.methodCoverage;if(isNaN(u)||u>=this.currentHistoricCoverage.mcq)return!1}else if("fullMethodCoverageIncreaseOnly"===n.historyComparisionType){let u=this.methodFullCoverage;if(isNaN(u)||u<=this.currentHistoricCoverage.mfcq)return!1}else if("fullMethodCoverageDecreaseOnly"===n.historyComparisionType){let u=this.methodFullCoverage;if(isNaN(u)||u>=this.currentHistoricCoverage.mfcq)return!1}return!0}updateCurrentHistoricCoverage(n){if(this.currentHistoricCoverage=null,""!==n)for(let t=0;t<this.historicCoverages.length;t++)if(this.historicCoverages[t].et===n){this.currentHistoricCoverage=this.historicCoverages[t];break}}}class vi extends OM{constructor(n,t){super(),this.subElements=[],this.classes=[],this.collapsed=!1,this.name=n,this.collapsed=n.indexOf("Test")>-1&&null===t}visible(n){if(""!==n.filter&&this.name.toLowerCase().indexOf(n.filter.toLowerCase())>-1)return!0;for(let t=0;t<this.subElements.length;t++)if(this.subElements[t].visible(n))return!0;for(let t=0;t<this.classes.length;t++)if(this.classes[t].visible(n))return!0;return!1}insertClass(n,t){if(this.coveredLines+=n.coveredLines,this.uncoveredLines+=n.uncoveredLines,this.coverableLines+=n.coverableLines,this.totalLines+=n.totalLines,this.coveredBranches+=n.coveredBranches,this.totalBranches+=n.totalBranches,this.coveredMethods+=n.coveredMethods,this.fullyCoveredMethods+=n.fullyCoveredMethods,this.totalMethods+=n.totalMethods,null===t)return void this.classes.push(n);let i=Ht.getNthOrLastIndexOf(n.name,".",t);-1===i&&(i=Ht.getNthOrLastIndexOf(n.name,"\\",t));let o=-1===i?"-":n.name.substring(0,i);for(let s=0;s<this.subElements.length;s++)if(this.subElements[s].name===o)return void this.subElements[s].insertClass(n,null);let r=new vi(o,this);this.subElements.push(r),r.insertClass(n,null)}collapse(){this.collapsed=!0;for(let n=0;n<this.subElements.length;n++)this.subElements[n].collapse()}expand(){this.collapsed=!1;for(let n=0;n<this.subElements.length;n++)this.subElements[n].expand()}toggleCollapse(n){n.preventDefault(),this.collapsed=!this.collapsed}updateCurrentHistoricCoverage(n){for(let t=0;t<this.subElements.length;t++)this.subElements[t].updateCurrentHistoricCoverage(n);for(let t=0;t<this.classes.length;t++)this.classes[t].updateCurrentHistoricCoverage(n)}static sortCodeElementViewModels(n,t,i){let o=i?-1:1,r=i?1:-1;"name"===t?n.sort(function(s,a){return s.name===a.name?0:s.name<a.name?o:r}):"covered"===t?n.sort(function(s,a){return s.coveredLines===a.coveredLines?0:s.coveredLines<a.coveredLines?o:r}):"uncovered"===t?n.sort(function(s,a){return s.uncoveredLines===a.uncoveredLines?0:s.uncoveredLines<a.uncoveredLines?o:r}):"coverable"===t?n.sort(function(s,a){return s.coverableLines===a.coverableLines?0:s.coverableLines<a.coverableLines?o:r}):"total"===t?n.sort(function(s,a){return s.totalLines===a.totalLines?0:s.totalLines<a.totalLines?o:r}):"coverage"===t?n.sort(function(s,a){return s.coverage===a.coverage?0:isNaN(s.coverage)?o:isNaN(a.coverage)?r:s.coverage<a.coverage?o:r}):"covered_branches"===t?n.sort(function(s,a){return s.coveredBranches===a.coveredBranches?0:isNaN(s.coveredBranches)?o:isNaN(a.coveredBranches)?r:s.coveredBranches<a.coveredBranches?o:r}):"total_branches"===t?n.sort(function(s,a){return s.totalBranches===a.totalBranches?0:isNaN(s.totalBranches)?o:isNaN(a.totalBranches)?r:s.totalBranches<a.totalBranches?o:r}):"branchcoverage"===t?n.sort(function(s,a){return s.branchCoverage===a.branchCoverage?0:isNaN(s.branchCoverage)?o:isNaN(a.branchCoverage)?r:s.branchCoverage<a.branchCoverage?o:r}):"covered_methods"===t?n.sort(function(s,a){return s.coveredMethods===a.coveredMethods?0:isNaN(s.coveredMethods)?o:isNaN(a.coveredMethods)?r:s.coveredMethods<a.coveredMethods?o:r}):"fullycovered_methods"===t?n.sort(function(s,a){return s.fullyCoveredMethods===a.fullyCoveredMethods?0:isNaN(s.fullyCoveredMethods)?o:isNaN(a.fullyCoveredMethods)?r:s.fullyCoveredMethods<a.fullyCoveredMethods?o:r}):"total_methods"===t?n.sort(function(s,a){return s.totalMethods===a.totalMethods?0:isNaN(s.totalMethods)?o:isNaN(a.totalMethods)?r:s.totalMethods<a.totalMethods?o:r}):"methodcoverage"===t?n.sort(function(s,a){return s.methodCoverage===a.methodCoverage?0:isNaN(s.methodCoverage)?o:isNaN(a.methodCoverage)?r:s.methodCoverage<a.methodCoverage?o:r}):"methodfullcoverage"===t&&n.sort(function(s,a){return s.methodFullCoverage===a.methodFullCoverage?0:isNaN(s.methodFullCoverage)?o:isNaN(a.methodFullCoverage)?r:s.methodFullCoverage<a.methodFullCoverage?o:r})}changeSorting(n,t){vi.sortCodeElementViewModels(this.subElements,n,t);let i=t?-1:1,o=t?1:-1;this.classes.sort("name"===n?function(r,s){return r.name===s.name?0:r.name<s.name?i:o}:"covered"===n?function(r,s){return r.coveredLines===s.coveredLines?0:r.coveredLines<s.coveredLines?i:o}:"uncovered"===n?function(r,s){return r.uncoveredLines===s.uncoveredLines?0:r.uncoveredLines<s.uncoveredLines?i:o}:"coverable"===n?function(r,s){return r.coverableLines===s.coverableLines?0:r.coverableLines<s.coverableLines?i:o}:"total"===n?function(r,s){return r.totalLines===s.totalLines?0:r.totalLines<s.totalLines?i:o}:"coverage"===n?function(r,s){return r.coverage===s.coverage?0:isNaN(r.coverage)?i:isNaN(s.coverage)?o:r.coverage<s.coverage?i:o}:"covered_branches"===n?function(r,s){return r.coveredBranches===s.coveredBranches?0:r.coveredBranches<s.coveredBranches?i:o}:"total_branches"===n?function(r,s){return r.totalBranches===s.totalBranches?0:r.totalBranches<s.totalBranches?i:o}:"branchcoverage"===n?function(r,s){return r.branchCoverage===s.branchCoverage?0:isNaN(r.branchCoverage)?i:isNaN(s.branchCoverage)?o:r.branchCoverage<s.branchCoverage?i:o}:"covered_methods"===n?function(r,s){return r.coveredMethods===s.coveredMethods?0:r.coveredMethods<s.coveredMethods?i:o}:"fullycovered_methods"===n?function(r,s){return r.fullyCoveredMethods===s.fullyCoveredMethods?0:r.fullyCoveredMethods<s.fullyCoveredMethods?i:o}:"total_methods"===n?function(r,s){return r.totalMethods===s.totalMethods?0:r.totalMethods<s.totalMethods?i:o}:"methodcoverage"===n?function(r,s){return r.methodCoverage===s.methodCoverage?0:isNaN(r.methodCoverage)?i:isNaN(s.methodCoverage)?o:r.methodCoverage<s.methodCoverage?i:o}:"methodfullcoverage"===n?function(r,s){return r.methodFullCoverage===s.methodFullCoverage?0:isNaN(r.methodFullCoverage)?i:isNaN(s.methodFullCoverage)?o:r.methodFullCoverage<s.methodFullCoverage?i:o}:function(r,s){const a=r.metrics[n],l=s.metrics[n];return a===l?0:isNaN(a)?i:isNaN(l)?o:a<l?i:o});for(let r=0;r<this.subElements.length;r++)this.subElements[r].changeSorting(n,t)}}let Lp=(()=>{class e{get nativeWindow(){return function UB(){return window}()}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275prov=te({token:e,factory:e.\u0275fac})}}return e})(),$B=(()=>{class e{constructor(){this.translations={}}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=on({type:e,selectors:[["pro-button"]],inputs:{translations:"translations"},standalone:!1,decls:3,vars:1,consts:[["href","https://reportgenerator.io/pro","target","_blank",1,"pro-button","pro-button-tiny",3,"title"]],template:function(i,o){1&i&&(w(0,"\xa0"),y(1,"a",0),w(2,"PRO"),_()),2&i&&(f(),bn("title",o.translations.methodCoverageProVersion))},encapsulation:2})}}return e})();function zB(e,n){if(1&e){const t=me();y(0,"div",3)(1,"label")(2,"input",4),Ke("ngModelChange",function(o){H(t);const r=v();return be(r.showBranchCoverage,o)||(r.showBranchCoverage=o),B(o)}),z("change",function(){H(t);const o=v();return B(o.showBranchCoverageChange.emit(o.showBranchCoverage))}),_(),w(3),_()()}if(2&e){const t=v();f(2),Ze("ngModel",t.showBranchCoverage),f(),j(" ",t.translations.branchCoverage,"")}}function GB(e,n){1&e&&N(0,"pro-button",9),2&e&&g("translations",v().translations)}function qB(e,n){1&e&&N(0,"pro-button",9),2&e&&g("translations",v().translations)}function WB(e,n){1&e&&N(0,"pro-button",9),2&e&&g("translations",v(2).translations)}function ZB(e,n){1&e&&(y(0,"a",13),N(1,"i",14),_()),2&e&&g("href",v().$implicit.explanationUrl,ni)}function QB(e,n){if(1&e){const t=me();y(0,"div",3)(1,"label")(2,"input",11),z("change",function(){const o=H(t).$implicit;return B(v(2).toggleMetric(o))}),_(),w(3),_(),w(4,"\xa0"),L(5,ZB,2,1,"a",12),_()}if(2&e){const t=n.$implicit,i=v(2);f(2),g("checked",i.isMetricSelected(t))("disabled",!i.methodCoverageAvailable),f(),j(" ",t.name,""),f(2),g("ngIf",t.explanationUrl)}}function YB(e,n){if(1&e&&(K(0),N(1,"br")(2,"br"),y(3,"b"),w(4),_(),L(5,WB,1,1,"pro-button",7)(6,QB,6,4,"div",10),X()),2&e){const t=v();f(4),O(t.translations.metrics),f(),g("ngIf",!t.methodCoverageAvailable),f(),g("ngForOf",t.metrics)}}let KB=(()=>{class e{constructor(){this.visible=!1,this.visibleChange=new De,this.translations={},this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.metrics=[],this.showLineCoverage=!1,this.showLineCoverageChange=new De,this.showBranchCoverage=!1,this.showBranchCoverageChange=new De,this.showMethodCoverage=!1,this.showMethodCoverageChange=new De,this.showMethodFullCoverage=!1,this.showMethodFullCoverageChange=new De,this.visibleMetrics=[],this.visibleMetricsChange=new De}isMetricSelected(t){return void 0!==this.visibleMetrics.find(i=>i.name===t.name)}toggleMetric(t){let i=this.visibleMetrics.find(o=>o.name===t.name);i?this.visibleMetrics.splice(this.visibleMetrics.indexOf(i),1):this.visibleMetrics.push(t),this.visibleMetrics=[...this.visibleMetrics],this.visibleMetricsChange.emit(this.visibleMetrics)}close(){this.visible=!1,this.visibleChange.emit(this.visible)}cancelEvent(t){t.stopPropagation()}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=on({type:e,selectors:[["popup"]],inputs:{visible:"visible",translations:"translations",branchCoverageAvailable:"branchCoverageAvailable",methodCoverageAvailable:"methodCoverageAvailable",metrics:"metrics",showLineCoverage:"showLineCoverage",showBranchCoverage:"showBranchCoverage",showMethodCoverage:"showMethodCoverage",showMethodFullCoverage:"showMethodFullCoverage",visibleMetrics:"visibleMetrics"},outputs:{visibleChange:"visibleChange",showLineCoverageChange:"showLineCoverageChange",showBranchCoverageChange:"showBranchCoverageChange",showMethodCoverageChange:"showMethodCoverageChange",showMethodFullCoverageChange:"showMethodFullCoverageChange",visibleMetricsChange:"visibleMetricsChange"},standalone:!1,decls:22,vars:13,consts:[[1,"popup-container",3,"click"],[1,"popup",3,"click"],[1,"close",3,"click"],[1,"mt-1"],["type","checkbox",3,"ngModelChange","change","ngModel"],["class","mt-1",4,"ngIf"],["type","checkbox",3,"ngModelChange","change","ngModel","disabled"],[3,"translations",4,"ngIf"],[4,"ngIf"],[3,"translations"],["class","mt-1",4,"ngFor","ngForOf"],["type","checkbox",3,"change","checked","disabled"],["target","_blank",3,"href",4,"ngIf"],["target","_blank",3,"href"],[1,"icon-info-circled"]],template:function(i,o){1&i&&(y(0,"div",0),z("click",function(){return o.close()}),y(1,"div",1),z("click",function(s){return o.cancelEvent(s)}),y(2,"div",2),z("click",function(){return o.close()}),w(3,"X"),_(),y(4,"b"),w(5),_(),y(6,"div",3)(7,"label")(8,"input",4),Ke("ngModelChange",function(s){return be(o.showLineCoverage,s)||(o.showLineCoverage=s),s}),z("change",function(){return o.showLineCoverageChange.emit(o.showLineCoverage)}),_(),w(9),_()(),L(10,zB,4,2,"div",5),y(11,"div",3)(12,"label")(13,"input",6),Ke("ngModelChange",function(s){return be(o.showMethodCoverage,s)||(o.showMethodCoverage=s),s}),z("change",function(){return o.showMethodCoverageChange.emit(o.showMethodCoverage)}),_(),w(14),_(),L(15,GB,1,1,"pro-button",7),_(),y(16,"div",3)(17,"label")(18,"input",6),Ke("ngModelChange",function(s){return be(o.showMethodFullCoverage,s)||(o.showMethodFullCoverage=s),s}),z("change",function(){return o.showMethodFullCoverageChange.emit(o.showMethodFullCoverage)}),_(),w(19),_(),L(20,qB,1,1,"pro-button",7),_(),L(21,YB,7,3,"ng-container",8),_()()),2&i&&(f(5),O(o.translations.coverageTypes),f(3),Ze("ngModel",o.showLineCoverage),f(),j(" ",o.translations.coverage,""),f(),g("ngIf",o.branchCoverageAvailable),f(3),Ze("ngModel",o.showMethodCoverage),g("disabled",!o.methodCoverageAvailable),f(),j(" ",o.translations.methodCoverage,""),f(),g("ngIf",!o.methodCoverageAvailable),f(3),Ze("ngModel",o.showMethodFullCoverage),g("disabled",!o.methodCoverageAvailable),f(),j(" ",o.translations.fullMethodCoverage,""),f(),g("ngIf",!o.methodCoverageAvailable),f(),g("ngIf",o.metrics.length>0))},dependencies:[Ki,qn,up,mc,zs,$B],encapsulation:2})}}return e})();function XB(e,n){1&e&&N(0,"td",3)}function JB(e,n){1&e&&N(0,"td"),2&e&&Gt("green ",v().greenClass,"")}function ej(e,n){1&e&&N(0,"td"),2&e&&Gt("red ",v().redClass,"")}let AM=(()=>{class e{constructor(){this.grayVisible=!0,this.greenVisible=!1,this.redVisible=!1,this.greenClass="",this.redClass="",this._percentage=NaN}get percentage(){return this._percentage}set percentage(t){this._percentage=t,this.grayVisible=isNaN(t),this.greenVisible=!isNaN(t)&&Math.round(t)>0,this.redVisible=!isNaN(t)&&100-Math.round(t)>0,this.greenClass="covered"+Math.round(t),this.redClass="covered"+(100-Math.round(t))}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=on({type:e,selectors:[["coverage-bar"]],inputs:{percentage:"percentage"},standalone:!1,decls:4,vars:3,consts:[[1,"coverage"],["class","gray covered100",4,"ngIf"],[3,"class",4,"ngIf"],[1,"gray","covered100"]],template:function(i,o){1&i&&(y(0,"table",0),L(1,XB,1,0,"td",1)(2,JB,1,3,"td",2)(3,ej,1,3,"td",2),_()),2&i&&(f(),g("ngIf",o.grayVisible),f(),g("ngIf",o.greenVisible),f(),g("ngIf",o.redVisible))},dependencies:[qn],encapsulation:2,changeDetection:0})}}return e})();const tj=["codeelement-row",""],nj=(e,n)=>({"icon-plus":e,"icon-minus":n});function ij(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.coveredLines)}}function oj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.uncoveredLines)}}function rj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.coverableLines)}}function sj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.totalLines)}}function aj(e,n){if(1&e&&(y(0,"th",6),w(1),_()),2&e){const t=v();g("title",t.element.coverageRatioText),f(),O(t.element.coveragePercentage)}}function lj(e,n){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),_()),2&e){const t=v();f(),g("percentage",t.element.coverage)}}function cj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.coveredBranches)}}function uj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.totalBranches)}}function dj(e,n){if(1&e&&(y(0,"th",6),w(1),_()),2&e){const t=v();g("title",t.element.branchCoverageRatioText),f(),O(t.element.branchCoveragePercentage)}}function fj(e,n){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),_()),2&e){const t=v();f(),g("percentage",t.element.branchCoverage)}}function hj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.coveredMethods)}}function pj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.totalMethods)}}function gj(e,n){if(1&e&&(y(0,"th",6),w(1),_()),2&e){const t=v();g("title",t.element.methodCoverageRatioText),f(),O(t.element.methodCoveragePercentage)}}function mj(e,n){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),_()),2&e){const t=v();f(),g("percentage",t.element.methodCoverage)}}function vj(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.fullyCoveredMethods)}}function _j(e,n){if(1&e&&(y(0,"th",5),w(1),_()),2&e){const t=v();f(),O(t.element.totalMethods)}}function yj(e,n){if(1&e&&(y(0,"th",6),w(1),_()),2&e){const t=v();g("title",t.element.methodFullCoverageRatioText),f(),O(t.element.methodFullCoveragePercentage)}}function Cj(e,n){if(1&e&&(y(0,"th",5),N(1,"coverage-bar",7),_()),2&e){const t=v();f(),g("percentage",t.element.methodFullCoverage)}}function Dj(e,n){1&e&&N(0,"th",5)}let wj=(()=>{class e{constructor(){this.collapsed=!1,this.lineCoverageAvailable=!1,this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.methodFullCoverageAvailable=!1,this.visibleMetrics=[]}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=on({type:e,selectors:[["","codeelement-row",""]],inputs:{element:"element",collapsed:"collapsed",lineCoverageAvailable:"lineCoverageAvailable",branchCoverageAvailable:"branchCoverageAvailable",methodCoverageAvailable:"methodCoverageAvailable",methodFullCoverageAvailable:"methodFullCoverageAvailable",visibleMetrics:"visibleMetrics"},standalone:!1,attrs:tj,decls:23,vars:24,consts:[["href","#",3,"click"],[3,"ngClass"],["class","right",4,"ngIf"],["class","right",3,"title",4,"ngIf"],["class","right",4,"ngFor","ngForOf"],[1,"right"],[1,"right",3,"title"],[3,"percentage"]],template:function(i,o){1&i&&(y(0,"th")(1,"a",0),z("click",function(s){return o.element.toggleCollapse(s)}),N(2,"i",1),w(3),_()(),L(4,ij,2,1,"th",2)(5,oj,2,1,"th",2)(6,rj,2,1,"th",2)(7,sj,2,1,"th",2)(8,aj,2,2,"th",3)(9,lj,2,1,"th",2)(10,cj,2,1,"th",2)(11,uj,2,1,"th",2)(12,dj,2,2,"th",3)(13,fj,2,1,"th",2)(14,hj,2,1,"th",2)(15,pj,2,1,"th",2)(16,gj,2,2,"th",3)(17,mj,2,1,"th",2)(18,vj,2,1,"th",2)(19,_j,2,1,"th",2)(20,yj,2,2,"th",3)(21,Cj,2,1,"th",2)(22,Dj,1,0,"th",4)),2&i&&(f(2),g("ngClass",Ch(21,nj,o.element.collapsed,!o.element.collapsed)),f(),j(" ",o.element.name,""),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngForOf",o.visibleMetrics))},dependencies:[hr,Ki,qn,AM],encapsulation:2,changeDetection:0})}}return e})();const bj=["coverage-history-chart",""];let Ej=(()=>{class e{constructor(){this.path=null,this._historicCoverages=[]}get historicCoverages(){return this._historicCoverages}set historicCoverages(t){if(this._historicCoverages=t,t.length>1){let i="";for(let o=0;o<t.length;o++)i+=0===o?"M":"L",i+=`${Ht.roundNumber(30*o/(t.length-1))}`,i+=`,${Ht.roundNumber(18-18*t[o]/100)}`;this.path=i}else this.path=null}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=on({type:e,selectors:[["","coverage-history-chart",""]],inputs:{historicCoverages:"historicCoverages"},standalone:!1,attrs:bj,decls:3,vars:1,consts:[["width","30","height","18",1,"ct-chart-line"],[1,"ct-series","ct-series-a"],[1,"ct-line"]],template:function(i,o){1&i&&(function em(){W.lFrame.currentNamespace="svg"}(),y(0,"svg",0)(1,"g",1),N(2,"path",2),_()()),2&i&&(f(2),ft("d",o.path))},encapsulation:2,changeDetection:0})}}return e})();const Mj=["class-row",""],Fc=e=>({historiccoverageoffset:e});function Ij(e,n){if(1&e&&(y(0,"a",5),w(1),_()),2&e){const t=v();g("href",t.clazz.reportPath,ni),f(),O(t.clazz.name)}}function Tj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v();f(),O(t.clazz.name)}}function Sj(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.coveredLines,t.clazz.currentHistoricCoverage.cl),""),f(),j(" ",t.clazz.coveredLines," "),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),j(" ",t.clazz.currentHistoricCoverage.cl," ")}}function xj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.coveredLines," ")}}function Nj(e,n){if(1&e&&(y(0,"td",6),L(1,Sj,5,6,"ng-container",1)(2,xj,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function Oj(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.currentHistoricCoverage.ucl,t.clazz.uncoveredLines),""),f(),j(" ",t.clazz.uncoveredLines," "),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),j(" ",t.clazz.currentHistoricCoverage.ucl," ")}}function Aj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.uncoveredLines," ")}}function kj(e,n){if(1&e&&(y(0,"td",6),L(1,Oj,5,6,"ng-container",1)(2,Aj,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function Fj(e,n){if(1&e&&(K(0),y(1,"div",8),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(2),O(t.clazz.coverableLines),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),O(t.clazz.currentHistoricCoverage.cal)}}function Rj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.coverableLines," ")}}function Lj(e,n){if(1&e&&(y(0,"td",6),L(1,Fj,5,3,"ng-container",1)(2,Rj,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function Pj(e,n){if(1&e&&(K(0),y(1,"div",8),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(2),O(t.clazz.totalLines),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),O(t.clazz.currentHistoricCoverage.tl)}}function Vj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.totalLines," ")}}function Hj(e,n){if(1&e&&(y(0,"td",6),L(1,Pj,5,3,"ng-container",1)(2,Vj,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function Bj(e,n){if(1&e&&N(0,"div",11),2&e){const t=v(2);bn("title",t.translations.history+": "+t.translations.coverage),g("historicCoverages",t.clazz.lineCoverageHistory)("ngClass",ur(3,Fc,null!==t.clazz.currentHistoricCoverage))}}function jj(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.coverage,t.clazz.currentHistoricCoverage.lcq),""),f(),j(" ",t.clazz.coveragePercentage," "),f(),g("title",t.clazz.currentHistoricCoverage.et+": "+t.clazz.currentHistoricCoverage.coverageRatioText),f(),j("",t.clazz.currentHistoricCoverage.lcq,"%")}}function Uj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.coveragePercentage," ")}}function $j(e,n){if(1&e&&(y(0,"td",9),L(1,Bj,1,5,"div",10)(2,jj,5,6,"ng-container",1)(3,Uj,2,1,"ng-container",1),_()),2&e){const t=v();g("title",t.clazz.coverageRatioText),f(),g("ngIf",t.clazz.lineCoverageHistory.length>1),f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function zj(e,n){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),_()),2&e){const t=v();f(),g("percentage",t.clazz.coverage)}}function Gj(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.coveredBranches,t.clazz.currentHistoricCoverage.cb),""),f(),j(" ",t.clazz.coveredBranches," "),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),j(" ",t.clazz.currentHistoricCoverage.cb," ")}}function qj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.coveredBranches," ")}}function Wj(e,n){if(1&e&&(y(0,"td",6),L(1,Gj,5,6,"ng-container",1)(2,qj,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function Zj(e,n){if(1&e&&(K(0),y(1,"div",8),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(2),O(t.clazz.totalBranches),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),O(t.clazz.currentHistoricCoverage.tb)}}function Qj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.totalBranches," ")}}function Yj(e,n){if(1&e&&(y(0,"td",6),L(1,Zj,5,3,"ng-container",1)(2,Qj,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function Kj(e,n){if(1&e&&N(0,"div",14),2&e){const t=v(2);bn("title",t.translations.history+": "+t.translations.branchCoverage),g("historicCoverages",t.clazz.branchCoverageHistory)("ngClass",ur(3,Fc,null!==t.clazz.currentHistoricCoverage))}}function Xj(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.branchCoverage,t.clazz.currentHistoricCoverage.bcq),""),f(),j(" ",t.clazz.branchCoveragePercentage," "),f(),g("title",t.clazz.currentHistoricCoverage.et+": "+t.clazz.currentHistoricCoverage.branchCoverageRatioText),f(),j("",t.clazz.currentHistoricCoverage.bcq,"%")}}function Jj(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.branchCoveragePercentage," ")}}function e3(e,n){if(1&e&&(y(0,"td",9),L(1,Kj,1,5,"div",13)(2,Xj,5,6,"ng-container",1)(3,Jj,2,1,"ng-container",1),_()),2&e){const t=v();g("title",t.clazz.branchCoverageRatioText),f(),g("ngIf",t.clazz.branchCoverageHistory.length>1),f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function t3(e,n){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),_()),2&e){const t=v();f(),g("percentage",t.clazz.branchCoverage)}}function n3(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.coveredMethods,t.clazz.currentHistoricCoverage.cm),""),f(),j(" ",t.clazz.coveredMethods," "),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),j(" ",t.clazz.currentHistoricCoverage.cm," ")}}function i3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.coveredMethods," ")}}function o3(e,n){if(1&e&&(y(0,"td",6),L(1,n3,5,6,"ng-container",1)(2,i3,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function r3(e,n){if(1&e&&(K(0),y(1,"div",8),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(2),O(t.clazz.totalMethods),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),O(t.clazz.currentHistoricCoverage.tm)}}function s3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.totalMethods," ")}}function a3(e,n){if(1&e&&(y(0,"td",6),L(1,r3,5,3,"ng-container",1)(2,s3,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function l3(e,n){if(1&e&&N(0,"div",16),2&e){const t=v(2);bn("title",t.translations.history+": "+t.translations.methodCoverage),g("historicCoverages",t.clazz.methodCoverageHistory)("ngClass",ur(3,Fc,null!==t.clazz.currentHistoricCoverage))}}function c3(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.methodCoverage,t.clazz.currentHistoricCoverage.mcq),""),f(),j(" ",t.clazz.methodCoveragePercentage," "),f(),g("title",t.clazz.currentHistoricCoverage.et+": "+t.clazz.currentHistoricCoverage.methodCoverageRatioText),f(),j("",t.clazz.currentHistoricCoverage.mcq,"%")}}function u3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.methodCoveragePercentage," ")}}function d3(e,n){if(1&e&&(y(0,"td",9),L(1,l3,1,5,"div",15)(2,c3,5,6,"ng-container",1)(3,u3,2,1,"ng-container",1),_()),2&e){const t=v();g("title",t.clazz.methodCoverageRatioText),f(),g("ngIf",t.clazz.methodCoverageHistory.length>1),f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function f3(e,n){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),_()),2&e){const t=v();f(),g("percentage",t.clazz.methodCoverage)}}function h3(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.fullyCoveredMethods,t.clazz.currentHistoricCoverage.fcm),""),f(),j(" ",t.clazz.fullyCoveredMethods," "),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),j(" ",t.clazz.currentHistoricCoverage.fcm," ")}}function p3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.fullyCoveredMethods," ")}}function g3(e,n){if(1&e&&(y(0,"td",6),L(1,h3,5,6,"ng-container",1)(2,p3,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function m3(e,n){if(1&e&&(K(0),y(1,"div",8),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(2),O(t.clazz.totalMethods),f(),g("title",t.clazz.currentHistoricCoverage.et),f(),O(t.clazz.currentHistoricCoverage.tm)}}function v3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.totalMethods," ")}}function _3(e,n){if(1&e&&(y(0,"td",6),L(1,m3,5,3,"ng-container",1)(2,v3,2,1,"ng-container",1),_()),2&e){const t=v();f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function y3(e,n){if(1&e&&N(0,"div",18),2&e){const t=v(2);bn("title",t.translations.history+": "+t.translations.fullMethodCoverage),g("historicCoverages",t.clazz.methodFullCoverageHistory)("ngClass",ur(3,Fc,null!==t.clazz.currentHistoricCoverage))}}function C3(e,n){if(1&e&&(K(0),y(1,"div"),w(2),_(),y(3,"div",7),w(4),_(),X()),2&e){const t=v(2);f(),Gt("currenthistory ",t.getClassName(t.clazz.methodFullCoverage,t.clazz.currentHistoricCoverage.mfcq),""),f(),j(" ",t.clazz.methodFullCoveragePercentage," "),f(),g("title",t.clazz.currentHistoricCoverage.et+": "+t.clazz.currentHistoricCoverage.methodFullCoverageRatioText),f(),j("",t.clazz.currentHistoricCoverage.mfcq,"%")}}function D3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),j(" ",t.clazz.methodFullCoveragePercentage," ")}}function w3(e,n){if(1&e&&(y(0,"td",9),L(1,y3,1,5,"div",17)(2,C3,5,6,"ng-container",1)(3,D3,2,1,"ng-container",1),_()),2&e){const t=v();g("title",t.clazz.methodFullCoverageRatioText),f(),g("ngIf",t.clazz.methodFullCoverageHistory.length>1),f(),g("ngIf",null!==t.clazz.currentHistoricCoverage),f(),g("ngIf",null===t.clazz.currentHistoricCoverage)}}function b3(e,n){if(1&e&&(y(0,"td",6),N(1,"coverage-bar",12),_()),2&e){const t=v();f(),g("percentage",t.clazz.methodFullCoverage)}}function E3(e,n){if(1&e&&(y(0,"td",6),w(1),_()),2&e){const t=n.$implicit,i=v();f(),O(i.clazz.metrics[t.abbreviation])}}let M3=(()=>{class e{constructor(){this.translations={},this.lineCoverageAvailable=!1,this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.methodFullCoverageAvailable=!1,this.visibleMetrics=[],this.historyComparisionDate=""}getClassName(t,i){return t>i?"lightgreen":t<i?"lightred":"lightgraybg"}static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=on({type:e,selectors:[["","class-row",""]],inputs:{clazz:"clazz",translations:"translations",lineCoverageAvailable:"lineCoverageAvailable",branchCoverageAvailable:"branchCoverageAvailable",methodCoverageAvailable:"methodCoverageAvailable",methodFullCoverageAvailable:"methodFullCoverageAvailable",visibleMetrics:"visibleMetrics",historyComparisionDate:"historyComparisionDate"},standalone:!1,attrs:Mj,decls:22,vars:21,consts:[[3,"href",4,"ngIf"],[4,"ngIf"],["class","right",4,"ngIf"],["class","right",3,"title",4,"ngIf"],["class","right",4,"ngFor","ngForOf"],[3,"href"],[1,"right"],[3,"title"],[1,"currenthistory"],[1,"right",3,"title"],["coverage-history-chart","","class","tinylinecoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinylinecoveragechart","ct-chart",3,"historicCoverages","ngClass","title"],[3,"percentage"],["coverage-history-chart","","class","tinybranchcoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinybranchcoveragechart","ct-chart",3,"historicCoverages","ngClass","title"],["coverage-history-chart","","class","tinymethodcoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinymethodcoveragechart","ct-chart",3,"historicCoverages","ngClass","title"],["coverage-history-chart","","class","tinyfullmethodcoveragechart ct-chart",3,"historicCoverages","ngClass","title",4,"ngIf"],["coverage-history-chart","",1,"tinyfullmethodcoveragechart","ct-chart",3,"historicCoverages","ngClass","title"]],template:function(i,o){1&i&&(y(0,"td"),L(1,Ij,2,2,"a",0)(2,Tj,2,1,"ng-container",1),_(),L(3,Nj,3,2,"td",2)(4,kj,3,2,"td",2)(5,Lj,3,2,"td",2)(6,Hj,3,2,"td",2)(7,$j,4,4,"td",3)(8,zj,2,1,"td",2)(9,Wj,3,2,"td",2)(10,Yj,3,2,"td",2)(11,e3,4,4,"td",3)(12,t3,2,1,"td",2)(13,o3,3,2,"td",2)(14,a3,3,2,"td",2)(15,d3,4,4,"td",3)(16,f3,2,1,"td",2)(17,g3,3,2,"td",2)(18,_3,3,2,"td",2)(19,w3,4,4,"td",3)(20,b3,2,1,"td",2)(21,E3,2,1,"td",4)),2&i&&(f(),g("ngIf",""!==o.clazz.reportPath),f(),g("ngIf",""===o.clazz.reportPath),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.lineCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.branchCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngIf",o.methodFullCoverageAvailable),f(),g("ngForOf",o.visibleMetrics))},dependencies:[hr,Ki,qn,Ej,AM],encapsulation:2,changeDetection:0})}}return e})();const rt=(e,n,t)=>({"icon-up-dir_active":e,"icon-down-dir_active":n,"icon-up-down-dir":t});function I3(e,n){if(1&e){const t=me();y(0,"popup",30),Ke("visibleChange",function(o){H(t);const r=v(2);return be(r.popupVisible,o)||(r.popupVisible=o),B(o)})("showLineCoverageChange",function(o){H(t);const r=v(2);return be(r.settings.showLineCoverage,o)||(r.settings.showLineCoverage=o),B(o)})("showBranchCoverageChange",function(o){H(t);const r=v(2);return be(r.settings.showBranchCoverage,o)||(r.settings.showBranchCoverage=o),B(o)})("showMethodCoverageChange",function(o){H(t);const r=v(2);return be(r.settings.showMethodCoverage,o)||(r.settings.showMethodCoverage=o),B(o)})("showMethodFullCoverageChange",function(o){H(t);const r=v(2);return be(r.settings.showFullMethodCoverage,o)||(r.settings.showFullMethodCoverage=o),B(o)})("visibleMetricsChange",function(o){H(t);const r=v(2);return be(r.settings.visibleMetrics,o)||(r.settings.visibleMetrics=o),B(o)}),_()}if(2&e){const t=v(2);Ze("visible",t.popupVisible),g("translations",t.translations)("branchCoverageAvailable",t.branchCoverageAvailable)("methodCoverageAvailable",t.methodCoverageAvailable)("metrics",t.metrics),Ze("showLineCoverage",t.settings.showLineCoverage)("showBranchCoverage",t.settings.showBranchCoverage)("showMethodCoverage",t.settings.showMethodCoverage)("showMethodFullCoverage",t.settings.showFullMethodCoverage)("visibleMetrics",t.settings.visibleMetrics)}}function T3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),O(t.translations.noGrouping)}}function S3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),O(t.translations.byAssembly)}}function x3(e,n){if(1&e&&(K(0),w(1),X()),2&e){const t=v(2);f(),O(t.translations.byNamespace+" "+t.settings.grouping)}}function N3(e,n){if(1&e&&(y(0,"option",34),w(1),_()),2&e){const t=n.$implicit;g("value",t),f(),O(t)}}function O3(e,n){1&e&&N(0,"br")}function A3(e,n){if(1&e&&(y(0,"option",44),w(1),_()),2&e){const t=v(4);f(),j(" ",t.translations.branchCoverageIncreaseOnly," ")}}function k3(e,n){if(1&e&&(y(0,"option",45),w(1),_()),2&e){const t=v(4);f(),j(" ",t.translations.branchCoverageDecreaseOnly," ")}}function F3(e,n){if(1&e&&(y(0,"option",46),w(1),_()),2&e){const t=v(4);f(),j(" ",t.translations.methodCoverageIncreaseOnly," ")}}function R3(e,n){if(1&e&&(y(0,"option",47),w(1),_()),2&e){const t=v(4);f(),j(" ",t.translations.methodCoverageDecreaseOnly," ")}}function L3(e,n){if(1&e&&(y(0,"option",48),w(1),_()),2&e){const t=v(4);f(),j(" ",t.translations.fullMethodCoverageIncreaseOnly," ")}}function P3(e,n){if(1&e&&(y(0,"option",49),w(1),_()),2&e){const t=v(4);f(),j(" ",t.translations.fullMethodCoverageDecreaseOnly," ")}}function V3(e,n){if(1&e){const t=me();y(0,"div")(1,"select",31),Ke("ngModelChange",function(o){H(t);const r=v(3);return be(r.settings.historyComparisionType,o)||(r.settings.historyComparisionType=o),B(o)}),y(2,"option",32),w(3),_(),y(4,"option",35),w(5),_(),y(6,"option",36),w(7),_(),y(8,"option",37),w(9),_(),L(10,A3,2,1,"option",38)(11,k3,2,1,"option",39)(12,F3,2,1,"option",40)(13,R3,2,1,"option",41)(14,L3,2,1,"option",42)(15,P3,2,1,"option",43),_()()}if(2&e){const t=v(3);f(),Ze("ngModel",t.settings.historyComparisionType),f(2),O(t.translations.filter),f(2),O(t.translations.allChanges),f(2),O(t.translations.lineCoverageIncreaseOnly),f(2),O(t.translations.lineCoverageDecreaseOnly),f(),g("ngIf",t.branchCoverageAvailable),f(),g("ngIf",t.branchCoverageAvailable),f(),g("ngIf",t.methodCoverageAvailable),f(),g("ngIf",t.methodCoverageAvailable),f(),g("ngIf",t.methodCoverageAvailable),f(),g("ngIf",t.methodCoverageAvailable)}}function H3(e,n){if(1&e){const t=me();K(0),y(1,"div"),w(2),y(3,"select",31),Ke("ngModelChange",function(o){H(t);const r=v(2);return be(r.settings.historyComparisionDate,o)||(r.settings.historyComparisionDate=o),B(o)}),z("ngModelChange",function(){return H(t),B(v(2).updateCurrentHistoricCoverage())}),y(4,"option",32),w(5),_(),L(6,N3,2,2,"option",33),_()(),L(7,O3,1,0,"br",0)(8,V3,16,11,"div",0),X()}if(2&e){const t=v(2);f(2),j(" ",t.translations.compareHistory," "),f(),Ze("ngModel",t.settings.historyComparisionDate),f(2),O(t.translations.date),f(),g("ngForOf",t.historicCoverageExecutionTimes),f(),g("ngIf",""!==t.settings.historyComparisionDate),f(),g("ngIf",""!==t.settings.historyComparisionDate)}}function B3(e,n){1&e&&N(0,"col",50)}function j3(e,n){1&e&&N(0,"col",51)}function U3(e,n){1&e&&N(0,"col",52)}function $3(e,n){1&e&&N(0,"col",53)}function z3(e,n){1&e&&N(0,"col",54)}function G3(e,n){1&e&&N(0,"col",55)}function q3(e,n){1&e&&N(0,"col",50)}function W3(e,n){1&e&&N(0,"col",53)}function Z3(e,n){1&e&&N(0,"col",54)}function Q3(e,n){1&e&&N(0,"col",55)}function Y3(e,n){1&e&&N(0,"col",50)}function K3(e,n){1&e&&N(0,"col",53)}function X3(e,n){1&e&&N(0,"col",54)}function J3(e,n){1&e&&N(0,"col",55)}function eU(e,n){1&e&&N(0,"col",50)}function tU(e,n){1&e&&N(0,"col",53)}function nU(e,n){1&e&&N(0,"col",54)}function iU(e,n){1&e&&N(0,"col",55)}function oU(e,n){1&e&&N(0,"col",55)}function rU(e,n){if(1&e&&(y(0,"th",56),w(1),_()),2&e){const t=v(2);f(),O(t.translations.coverage)}}function sU(e,n){if(1&e&&(y(0,"th",57),w(1),_()),2&e){const t=v(2);f(),O(t.translations.branchCoverage)}}function aU(e,n){if(1&e&&(y(0,"th",57),w(1),_()),2&e){const t=v(2);f(),O(t.translations.methodCoverage)}}function lU(e,n){if(1&e&&(y(0,"th",57),w(1),_()),2&e){const t=v(2);f(),O(t.translations.fullMethodCoverage)}}function cU(e,n){if(1&e&&(y(0,"th",58),w(1),_()),2&e){const t=v(2);ft("colspan",t.settings.visibleMetrics.length),f(),O(t.translations.metrics)}}function uU(e,n){if(1&e){const t=me();y(0,"td",56)(1,"ngx-slider",59),Ke("valueChange",function(o){H(t);const r=v(2);return be(r.settings.lineCoverageMin,o)||(r.settings.lineCoverageMin=o),B(o)})("highValueChange",function(o){H(t);const r=v(2);return be(r.settings.lineCoverageMax,o)||(r.settings.lineCoverageMax=o),B(o)}),_()()}if(2&e){const t=v(2);f(),Ze("value",t.settings.lineCoverageMin)("highValue",t.settings.lineCoverageMax),g("options",t.sliderOptions)}}function dU(e,n){if(1&e){const t=me();y(0,"td",57)(1,"ngx-slider",59),Ke("valueChange",function(o){H(t);const r=v(2);return be(r.settings.branchCoverageMin,o)||(r.settings.branchCoverageMin=o),B(o)})("highValueChange",function(o){H(t);const r=v(2);return be(r.settings.branchCoverageMax,o)||(r.settings.branchCoverageMax=o),B(o)}),_()()}if(2&e){const t=v(2);f(),Ze("value",t.settings.branchCoverageMin)("highValue",t.settings.branchCoverageMax),g("options",t.sliderOptions)}}function fU(e,n){if(1&e){const t=me();y(0,"td",57)(1,"ngx-slider",59),Ke("valueChange",function(o){H(t);const r=v(2);return be(r.settings.methodCoverageMin,o)||(r.settings.methodCoverageMin=o),B(o)})("highValueChange",function(o){H(t);const r=v(2);return be(r.settings.methodCoverageMax,o)||(r.settings.methodCoverageMax=o),B(o)}),_()()}if(2&e){const t=v(2);f(),Ze("value",t.settings.methodCoverageMin)("highValue",t.settings.methodCoverageMax),g("options",t.sliderOptions)}}function hU(e,n){if(1&e){const t=me();y(0,"td",57)(1,"ngx-slider",59),Ke("valueChange",function(o){H(t);const r=v(2);return be(r.settings.methodFullCoverageMin,o)||(r.settings.methodFullCoverageMin=o),B(o)})("highValueChange",function(o){H(t);const r=v(2);return be(r.settings.methodFullCoverageMax,o)||(r.settings.methodFullCoverageMax=o),B(o)}),_()()}if(2&e){const t=v(2);f(),Ze("value",t.settings.methodFullCoverageMin)("highValue",t.settings.methodFullCoverageMax),g("options",t.sliderOptions)}}function pU(e,n){1&e&&N(0,"td",58),2&e&&ft("colspan",v(2).settings.visibleMetrics.length)}function gU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("covered",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"covered"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"covered"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"covered"!==t.settings.sortBy)),f(),O(t.translations.covered)}}function mU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("uncovered",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"uncovered"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"uncovered"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"uncovered"!==t.settings.sortBy)),f(),O(t.translations.uncovered)}}function vU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("coverable",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"coverable"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"coverable"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"coverable"!==t.settings.sortBy)),f(),O(t.translations.coverable)}}function _U(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("total",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"total"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"total"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"total"!==t.settings.sortBy)),f(),O(t.translations.total)}}function yU(e,n){if(1&e){const t=me();y(0,"th",61)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("coverage",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"coverage"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"coverage"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"coverage"!==t.settings.sortBy)),f(),O(t.translations.percentage)}}function CU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("covered_branches",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"covered_branches"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"covered_branches"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"covered_branches"!==t.settings.sortBy)),f(),O(t.translations.covered)}}function DU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("total_branches",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"total_branches"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"total_branches"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"total_branches"!==t.settings.sortBy)),f(),O(t.translations.total)}}function wU(e,n){if(1&e){const t=me();y(0,"th",61)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("branchcoverage",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"branchcoverage"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"branchcoverage"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"branchcoverage"!==t.settings.sortBy)),f(),O(t.translations.percentage)}}function bU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("covered_methods",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"covered_methods"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"covered_methods"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"covered_methods"!==t.settings.sortBy)),f(),O(t.translations.covered)}}function EU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("total_methods",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"total_methods"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"total_methods"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"total_methods"!==t.settings.sortBy)),f(),O(t.translations.total)}}function MU(e,n){if(1&e){const t=me();y(0,"th",61)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("methodcoverage",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"methodcoverage"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"methodcoverage"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"methodcoverage"!==t.settings.sortBy)),f(),O(t.translations.percentage)}}function IU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("fullycovered_methods",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"fullycovered_methods"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"fullycovered_methods"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"fullycovered_methods"!==t.settings.sortBy)),f(),O(t.translations.covered)}}function TU(e,n){if(1&e){const t=me();y(0,"th",60)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("total_methods",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"total_methods"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"total_methods"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"total_methods"!==t.settings.sortBy)),f(),O(t.translations.total)}}function SU(e,n){if(1&e){const t=me();y(0,"th",61)(1,"a",3),z("click",function(o){return H(t),B(v(2).updateSorting("methodfullcoverage",o))}),N(2,"i",26),w(3),_()()}if(2&e){const t=v(2);f(2),g("ngClass",Le(2,rt,"methodfullcoverage"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"methodfullcoverage"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"methodfullcoverage"!==t.settings.sortBy)),f(),O(t.translations.percentage)}}function xU(e,n){if(1&e){const t=me();y(0,"th")(1,"a",3),z("click",function(o){const r=H(t).$implicit;return B(v(2).updateSorting(r.abbreviation,o))}),N(2,"i",26),w(3),_(),y(4,"a",62),N(5,"i",63),_()()}if(2&e){const t=n.$implicit,i=v(2);f(2),g("ngClass",Le(3,rt,i.settings.sortBy===t.abbreviation&&"asc"===i.settings.sortOrder,i.settings.sortBy===t.abbreviation&&"desc"===i.settings.sortOrder,i.settings.sortBy!==t.abbreviation)),f(),O(t.name),f(),bn("href",t.explanationUrl,ni)}}function NU(e,n){if(1&e&&N(0,"tr",65),2&e){const t=v().$implicit,i=v(2);g("element",t)("collapsed",t.collapsed)("lineCoverageAvailable",i.settings.showLineCoverage)("branchCoverageAvailable",i.branchCoverageAvailable&&i.settings.showBranchCoverage)("methodCoverageAvailable",i.methodCoverageAvailable&&i.settings.showMethodCoverage)("methodFullCoverageAvailable",i.methodCoverageAvailable&&i.settings.showFullMethodCoverage)("visibleMetrics",i.settings.visibleMetrics)}}function OU(e,n){if(1&e&&N(0,"tr",67),2&e){const t=v().$implicit,i=v(3);g("clazz",t)("translations",i.translations)("lineCoverageAvailable",i.settings.showLineCoverage)("branchCoverageAvailable",i.branchCoverageAvailable&&i.settings.showBranchCoverage)("methodCoverageAvailable",i.methodCoverageAvailable&&i.settings.showMethodCoverage)("methodFullCoverageAvailable",i.methodCoverageAvailable&&i.settings.showFullMethodCoverage)("visibleMetrics",i.settings.visibleMetrics)("historyComparisionDate",i.settings.historyComparisionDate)}}function AU(e,n){if(1&e&&(K(0),L(1,OU,1,8,"tr",66),X()),2&e){const t=n.$implicit,i=v().$implicit,o=v(2);f(),g("ngIf",!i.collapsed&&t.visible(o.settings))}}function kU(e,n){if(1&e&&N(0,"tr",70),2&e){const t=v().$implicit,i=v(5);g("clazz",t)("translations",i.translations)("lineCoverageAvailable",i.settings.showLineCoverage)("branchCoverageAvailable",i.branchCoverageAvailable&&i.settings.showBranchCoverage)("methodCoverageAvailable",i.methodCoverageAvailable&&i.settings.showMethodCoverage)("methodFullCoverageAvailable",i.methodCoverageAvailable&&i.settings.showFullMethodCoverage)("visibleMetrics",i.settings.visibleMetrics)("historyComparisionDate",i.settings.historyComparisionDate)}}function FU(e,n){if(1&e&&(K(0),L(1,kU,1,8,"tr",69),X()),2&e){const t=n.$implicit,i=v(2).$implicit,o=v(3);f(),g("ngIf",!i.collapsed&&t.visible(o.settings))}}function RU(e,n){if(1&e&&(K(0),N(1,"tr",68),L(2,FU,2,1,"ng-container",29),X()),2&e){const t=v().$implicit,i=v(3);f(),g("element",t)("collapsed",t.collapsed)("lineCoverageAvailable",i.settings.showLineCoverage)("branchCoverageAvailable",i.branchCoverageAvailable&&i.settings.showBranchCoverage)("methodCoverageAvailable",i.methodCoverageAvailable&&i.settings.showMethodCoverage)("methodFullCoverageAvailable",i.methodCoverageAvailable&&i.settings.showFullMethodCoverage)("visibleMetrics",i.settings.visibleMetrics),f(),g("ngForOf",t.classes)}}function LU(e,n){if(1&e&&(K(0),L(1,RU,3,8,"ng-container",0),X()),2&e){const t=n.$implicit,i=v().$implicit,o=v(2);f(),g("ngIf",!i.collapsed&&t.visible(o.settings))}}function PU(e,n){if(1&e&&(K(0),L(1,NU,1,7,"tr",64)(2,AU,2,1,"ng-container",29)(3,LU,2,1,"ng-container",29),X()),2&e){const t=n.$implicit,i=v(2);f(),g("ngIf",t.visible(i.settings)),f(),g("ngForOf",t.classes),f(),g("ngForOf",t.subElements)}}function VU(e,n){if(1&e){const t=me();y(0,"div"),L(1,I3,1,10,"popup",1),y(2,"div",2)(3,"div")(4,"a",3),z("click",function(o){return H(t),B(v().collapseAll(o))}),w(5),_(),w(6," | "),y(7,"a",3),z("click",function(o){return H(t),B(v().expandAll(o))}),w(8),_()(),y(9,"div",4)(10,"span",5),L(11,T3,2,1,"ng-container",0)(12,S3,2,1,"ng-container",0)(13,x3,2,1,"ng-container",0),_(),N(14,"br"),w(15),y(16,"input",6),Ke("ngModelChange",function(o){H(t);const r=v();return be(r.settings.grouping,o)||(r.settings.grouping=o),B(o)}),z("ngModelChange",function(){return H(t),B(v().updateCoverageInfo())}),_()(),y(17,"div",4),L(18,H3,9,6,"ng-container",0),_(),y(19,"div",7)(20,"button",8),z("click",function(){return H(t),B(v().popupVisible=!0)}),N(21,"i",9),w(22),_()()(),y(23,"div",10)(24,"table",11)(25,"colgroup"),N(26,"col",12),L(27,B3,1,0,"col",13)(28,j3,1,0,"col",14)(29,U3,1,0,"col",15)(30,$3,1,0,"col",16)(31,z3,1,0,"col",17)(32,G3,1,0,"col",18)(33,q3,1,0,"col",13)(34,W3,1,0,"col",16)(35,Z3,1,0,"col",17)(36,Q3,1,0,"col",18)(37,Y3,1,0,"col",13)(38,K3,1,0,"col",16)(39,X3,1,0,"col",17)(40,J3,1,0,"col",18)(41,eU,1,0,"col",13)(42,tU,1,0,"col",16)(43,nU,1,0,"col",17)(44,iU,1,0,"col",18)(45,oU,1,0,"col",19),_(),y(46,"thead")(47,"tr",20),N(48,"th"),L(49,rU,2,1,"th",21)(50,sU,2,1,"th",22)(51,aU,2,1,"th",22)(52,lU,2,1,"th",22)(53,cU,2,2,"th",23),_(),y(54,"tr",24)(55,"td")(56,"input",25),Ke("ngModelChange",function(o){H(t);const r=v();return be(r.settings.filter,o)||(r.settings.filter=o),B(o)}),_()(),L(57,uU,2,3,"td",21)(58,dU,2,3,"td",22)(59,fU,2,3,"td",22)(60,hU,2,3,"td",22)(61,pU,1,1,"td",23),_(),y(62,"tr")(63,"th")(64,"a",3),z("click",function(o){return H(t),B(v().updateSorting("name",o))}),N(65,"i",26),w(66),_()(),L(67,gU,4,6,"th",27)(68,mU,4,6,"th",27)(69,vU,4,6,"th",27)(70,_U,4,6,"th",27)(71,yU,4,6,"th",28)(72,CU,4,6,"th",27)(73,DU,4,6,"th",27)(74,wU,4,6,"th",28)(75,bU,4,6,"th",27)(76,EU,4,6,"th",27)(77,MU,4,6,"th",28)(78,IU,4,6,"th",27)(79,TU,4,6,"th",27)(80,SU,4,6,"th",28)(81,xU,6,7,"th",29),_()(),y(82,"tbody"),L(83,PU,4,3,"ng-container",29),_()()()()}if(2&e){const t=v();f(),g("ngIf",t.popupVisible),f(4),O(t.translations.collapseAll),f(3),O(t.translations.expandAll),f(3),g("ngIf",-1===t.settings.grouping),f(),g("ngIf",0===t.settings.grouping),f(),g("ngIf",t.settings.grouping>0),f(2),j(" ",t.translations.grouping," "),f(),g("max",t.settings.groupingMaximum),Ze("ngModel",t.settings.grouping),f(2),g("ngIf",t.historicCoverageExecutionTimes.length>0),f(4),O(t.metrics.length>0?t.translations.selectCoverageTypesAndMetrics:t.translations.selectCoverageTypes),f(5),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngForOf",t.settings.visibleMetrics),f(4),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.settings.visibleMetrics.length>0),f(3),bn("placeholder",t.translations.filter),Ze("ngModel",t.settings.filter),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.settings.visibleMetrics.length>0),f(4),g("ngClass",Le(60,rt,"name"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"name"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"name"!==t.settings.sortBy)),f(),O(t.translations.name),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.settings.showLineCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.branchCoverageAvailable&&t.settings.showBranchCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngIf",t.methodCoverageAvailable&&t.settings.showFullMethodCoverage),f(),g("ngForOf",t.settings.visibleMetrics),f(2),g("ngForOf",t.codeElements)}}let HU=(()=>{class e{constructor(t){this.queryString="",this.historicCoverageExecutionTimes=[],this.branchCoverageAvailable=!1,this.methodCoverageAvailable=!1,this.metrics=[],this.codeElements=[],this.translations={},this.popupVisible=!1,this.settings=new BB,this.sliderOptions={floor:0,ceil:100,step:1,ticksArray:[0,10,20,30,40,50,60,70,80,90,100],showTicks:!0},this.window=t.nativeWindow}ngOnInit(){this.historicCoverageExecutionTimes=this.window.historicCoverageExecutionTimes,this.branchCoverageAvailable=this.window.branchCoverageAvailable,this.methodCoverageAvailable=this.window.methodCoverageAvailable,this.metrics=this.window.metrics,this.translations=this.window.translations,Ht.maximumDecimalPlacesForCoverageQuotas=this.window.maximumDecimalPlacesForCoverageQuotas;let t=!1;if(void 0!==this.window.history&&void 0!==this.window.history.replaceState&&null!==this.window.history.state&&null!=this.window.history.state.coverageInfoSettings)console.log("Coverage info: Restoring from history",this.window.history.state.coverageInfoSettings),t=!0,this.settings=JSON.parse(JSON.stringify(this.window.history.state.coverageInfoSettings));else{let o=0,r=this.window.assemblies;for(let s=0;s<r.length;s++)for(let a=0;a<r[s].classes.length;a++)o=Math.max(o,(r[s].classes[a].name.match(/\.|\\/g)||[]).length);this.settings.groupingMaximum=o,console.log("Grouping maximum: "+o),this.settings.showBranchCoverage=this.branchCoverageAvailable,this.settings.showMethodCoverage=this.methodCoverageAvailable,this.settings.showFullMethodCoverage=this.methodCoverageAvailable}const i=window.location.href.indexOf("?");i>-1&&(this.queryString=window.location.href.substring(i)),this.updateCoverageInfo(),t&&this.restoreCollapseState()}onBeforeUnload(){if(this.saveCollapseState(),void 0!==this.window.history&&void 0!==this.window.history.replaceState){console.log("Coverage info: Updating history",this.settings);let t=new NM;null!==window.history.state&&(t=JSON.parse(JSON.stringify(this.window.history.state))),t.coverageInfoSettings=JSON.parse(JSON.stringify(this.settings)),window.history.replaceState(t,"")}}updateCoverageInfo(){let t=(new Date).getTime(),i=this.window.assemblies,o=[],r=0;if(0===this.settings.grouping)for(let l=0;l<i.length;l++){let c=new vi(i[l].name,null);o.push(c);for(let u=0;u<i[l].classes.length;u++)c.insertClass(new Rp(i[l].classes[u],this.queryString),null),r++}else if(-1===this.settings.grouping){let l=new vi(this.translations.all,null);o.push(l);for(let c=0;c<i.length;c++)for(let u=0;u<i[c].classes.length;u++)l.insertClass(new Rp(i[c].classes[u],this.queryString),null),r++}else for(let l=0;l<i.length;l++){let c=new vi(i[l].name,null);o.push(c);for(let u=0;u<i[l].classes.length;u++)c.insertClass(new Rp(i[l].classes[u],this.queryString),this.settings.grouping),r++}let s=-1,a=1;"name"===this.settings.sortBy&&(s="asc"===this.settings.sortOrder?-1:1,a="asc"===this.settings.sortOrder?1:-1),o.sort(function(l,c){return l.name===c.name?0:l.name<c.name?s:a}),vi.sortCodeElementViewModels(o,this.settings.sortBy,"asc"===this.settings.sortOrder);for(let l=0;l<o.length;l++)o[l].changeSorting(this.settings.sortBy,"asc"===this.settings.sortOrder);this.codeElements=o,console.log(`Processing assemblies finished (Duration: ${(new Date).getTime()-t}ms, Assemblies: ${o.length}, Classes: ${r})`),""!==this.settings.historyComparisionDate&&this.updateCurrentHistoricCoverage()}updateCurrentHistoricCoverage(){let t=(new Date).getTime();for(let i=0;i<this.codeElements.length;i++)this.codeElements[i].updateCurrentHistoricCoverage(this.settings.historyComparisionDate);console.log(`Updating current historic coverage finished (Duration: ${(new Date).getTime()-t}ms)`)}collapseAll(t){t.preventDefault();for(let i=0;i<this.codeElements.length;i++)this.codeElements[i].collapse()}expandAll(t){t.preventDefault();for(let i=0;i<this.codeElements.length;i++)this.codeElements[i].expand()}updateSorting(t,i){i.preventDefault(),this.settings.sortOrder=t===this.settings.sortBy&&"asc"===this.settings.sortOrder?"desc":"asc",this.settings.sortBy=t,console.log(`Updating sort column: '${this.settings.sortBy}' (${this.settings.sortOrder})`),vi.sortCodeElementViewModels(this.codeElements,this.settings.sortBy,"asc"===this.settings.sortOrder);for(let o=0;o<this.codeElements.length;o++)this.codeElements[o].changeSorting(this.settings.sortBy,"asc"===this.settings.sortOrder)}saveCollapseState(){this.settings.collapseStates=[];let t=i=>{for(let o=0;o<i.length;o++)this.settings.collapseStates.push(i[o].collapsed),t(i[o].subElements)};t(this.codeElements)}restoreCollapseState(){let t=0,i=o=>{for(let r=0;r<o.length;r++)this.settings.collapseStates.length>t&&(o[r].collapsed=this.settings.collapseStates[t]),t++,i(o[r].subElements)};i(this.codeElements)}static{this.\u0275fac=function(i){return new(i||e)(T(Lp))}}static{this.\u0275cmp=on({type:e,selectors:[["coverage-info"]],hostBindings:function(i,o){1&i&&z("beforeunload",function(){return o.onBeforeUnload()},0,cl)},standalone:!1,decls:1,vars:1,consts:[[4,"ngIf"],[3,"visible","translations","branchCoverageAvailable","methodCoverageAvailable","metrics","showLineCoverage","showBranchCoverage","showMethodCoverage","showMethodFullCoverage","visibleMetrics","visibleChange","showLineCoverageChange","showBranchCoverageChange","showMethodCoverageChange","showMethodFullCoverageChange","visibleMetricsChange",4,"ngIf"],[1,"customizebox"],["href","#",3,"click"],[1,"col-center"],[1,"slider-label"],["type","range","step","1","min","-1",3,"ngModelChange","max","ngModel"],[1,"col-right","right"],["type","button",3,"click"],[1,"icon-cog"],[1,"table-responsive"],[1,"overview","table-fixed","stripped"],[1,"column-min-200"],["class","column90",4,"ngIf"],["class","column105",4,"ngIf"],["class","column100",4,"ngIf"],["class","column70",4,"ngIf"],["class","column98",4,"ngIf"],["class","column112",4,"ngIf"],["class","column112",4,"ngFor","ngForOf"],[1,"header"],["class","center","colspan","6",4,"ngIf"],["class","center","colspan","4",4,"ngIf"],["class","center",4,"ngIf"],[1,"filterbar"],["type","text",3,"ngModelChange","ngModel","placeholder"],[3,"ngClass"],["class","right",4,"ngIf"],["class","center","colspan","2",4,"ngIf"],[4,"ngFor","ngForOf"],[3,"visibleChange","showLineCoverageChange","showBranchCoverageChange","showMethodCoverageChange","showMethodFullCoverageChange","visibleMetricsChange","visible","translations","branchCoverageAvailable","methodCoverageAvailable","metrics","showLineCoverage","showBranchCoverage","showMethodCoverage","showMethodFullCoverage","visibleMetrics"],[3,"ngModelChange","ngModel"],["value",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["value","allChanges"],["value","lineCoverageIncreaseOnly"],["value","lineCoverageDecreaseOnly"],["value","branchCoverageIncreaseOnly",4,"ngIf"],["value","branchCoverageDecreaseOnly",4,"ngIf"],["value","methodCoverageIncreaseOnly",4,"ngIf"],["value","methodCoverageDecreaseOnly",4,"ngIf"],["value","fullMethodCoverageIncreaseOnly",4,"ngIf"],["value","fullMethodCoverageDecreaseOnly",4,"ngIf"],["value","branchCoverageIncreaseOnly"],["value","branchCoverageDecreaseOnly"],["value","methodCoverageIncreaseOnly"],["value","methodCoverageDecreaseOnly"],["value","fullMethodCoverageIncreaseOnly"],["value","fullMethodCoverageDecreaseOnly"],[1,"column90"],[1,"column105"],[1,"column100"],[1,"column70"],[1,"column98"],[1,"column112"],["colspan","6",1,"center"],["colspan","4",1,"center"],[1,"center"],[3,"valueChange","highValueChange","value","highValue","options"],[1,"right"],["colspan","2",1,"center"],["target","_blank",3,"href"],[1,"icon-info-circled"],["codeelement-row","",3,"element","collapsed","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics",4,"ngIf"],["codeelement-row","",3,"element","collapsed","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics"],["class-row","",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics","historyComparisionDate",4,"ngIf"],["class-row","",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics","historyComparisionDate"],["codeelement-row","",1,"namespace",3,"element","collapsed","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics"],["class","namespace","class-row","",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics","historyComparisionDate",4,"ngIf"],["class-row","",1,"namespace",3,"clazz","translations","lineCoverageAvailable","branchCoverageAvailable","methodCoverageAvailable","methodFullCoverageAvailable","visibleMetrics","historyComparisionDate"]],template:function(i,o){1&i&&L(0,VU,84,64,"div",0),2&i&&g("ngIf",o.codeElements.length>0)},dependencies:[hr,Ki,qn,xp,Op,Hs,Sp,qs,mc,zs,xM,KB,wj,M3],encapsulation:2})}}return e})();class BU{constructor(){this.assembly="",this.numberOfRiskHotspots=10,this.filter="",this.sortBy="",this.sortOrder="asc"}}const Rc=(e,n,t)=>({"icon-up-dir_active":e,"icon-down-dir_active":n,"icon-up-down-dir":t}),jU=(e,n)=>({lightred:e,lightgreen:n});function UU(e,n){if(1&e&&(y(0,"option",16),w(1),_()),2&e){const t=n.$implicit;g("value",t),f(),O(t)}}function $U(e,n){if(1&e&&(y(0,"span"),w(1),_()),2&e){const t=v(2);f(),O(t.translations.top)}}function zU(e,n){1&e&&(y(0,"option",23),w(1,"20"),_())}function GU(e,n){1&e&&(y(0,"option",24),w(1,"50"),_())}function qU(e,n){1&e&&(y(0,"option",25),w(1,"100"),_())}function WU(e,n){if(1&e&&(y(0,"option",16),w(1),_()),2&e){const t=v(3);g("value",t.totalNumberOfRiskHotspots),f(),O(t.translations.all)}}function ZU(e,n){if(1&e){const t=me();y(0,"select",17),Ke("ngModelChange",function(o){H(t);const r=v(2);return be(r.settings.numberOfRiskHotspots,o)||(r.settings.numberOfRiskHotspots=o),B(o)}),y(1,"option",18),w(2,"10"),_(),L(3,zU,2,0,"option",19)(4,GU,2,0,"option",20)(5,qU,2,0,"option",21)(6,WU,2,2,"option",22),_()}if(2&e){const t=v(2);Ze("ngModel",t.settings.numberOfRiskHotspots),f(3),g("ngIf",t.totalNumberOfRiskHotspots>10),f(),g("ngIf",t.totalNumberOfRiskHotspots>20),f(),g("ngIf",t.totalNumberOfRiskHotspots>50),f(),g("ngIf",t.totalNumberOfRiskHotspots>100)}}function QU(e,n){1&e&&N(0,"col",26)}function YU(e,n){if(1&e){const t=me();y(0,"th")(1,"a",13),z("click",function(o){const r=H(t).index;return B(v(2).updateSorting(""+r,o))}),N(2,"i",14),w(3),_(),y(4,"a",27),N(5,"i",28),_()()}if(2&e){const t=n.$implicit,i=n.index,o=v(2);f(2),g("ngClass",Le(3,Rc,o.settings.sortBy===""+i&&"asc"===o.settings.sortOrder,o.settings.sortBy===""+i&&"desc"===o.settings.sortOrder,o.settings.sortBy!==""+i)),f(),O(t.name),f(),bn("href",t.explanationUrl,ni)}}function KU(e,n){if(1&e&&(y(0,"td",32),w(1),_()),2&e){const t=n.$implicit;g("ngClass",Ch(2,jU,t.exceeded,!t.exceeded)),f(),O(t.value)}}function XU(e,n){if(1&e&&(y(0,"tr")(1,"td"),w(2),_(),y(3,"td")(4,"a",29),w(5),_()(),y(6,"td",30)(7,"a",29),w(8),_()(),L(9,KU,2,5,"td",31),_()),2&e){const t=n.$implicit,i=v(2);f(2),O(t.assembly),f(2),g("href",t.reportPath+i.queryString,ni),f(),O(t.class),f(),g("title",t.methodName),f(),g("href",t.reportPath+i.queryString+"#file"+t.fileIndex+"_line"+t.line,ni),f(),j(" ",t.methodShortName," "),f(),g("ngForOf",t.metrics)}}function JU(e,n){if(1&e){const t=me();y(0,"div")(1,"div",1)(2,"div")(3,"select",2),Ke("ngModelChange",function(o){H(t);const r=v();return be(r.settings.assembly,o)||(r.settings.assembly=o),B(o)}),z("ngModelChange",function(){return H(t),B(v().updateRiskHotpots())}),y(4,"option",3),w(5),_(),L(6,UU,2,2,"option",4),_()(),y(7,"div",5),L(8,$U,2,1,"span",0)(9,ZU,7,5,"select",6),_(),N(10,"div",5),y(11,"div",7)(12,"span"),w(13),_(),y(14,"input",8),Ke("ngModelChange",function(o){H(t);const r=v();return be(r.settings.filter,o)||(r.settings.filter=o),B(o)}),z("ngModelChange",function(){return H(t),B(v().updateRiskHotpots())}),_()()(),y(15,"div",9)(16,"table",10)(17,"colgroup"),N(18,"col",11)(19,"col",11)(20,"col",11),L(21,QU,1,0,"col",12),_(),y(22,"thead")(23,"tr")(24,"th")(25,"a",13),z("click",function(o){return H(t),B(v().updateSorting("assembly",o))}),N(26,"i",14),w(27),_()(),y(28,"th")(29,"a",13),z("click",function(o){return H(t),B(v().updateSorting("class",o))}),N(30,"i",14),w(31),_()(),y(32,"th")(33,"a",13),z("click",function(o){return H(t),B(v().updateSorting("method",o))}),N(34,"i",14),w(35),_()(),L(36,YU,6,7,"th",15),_()(),y(37,"tbody"),L(38,XU,10,7,"tr",15),function cw(e,n){const t=G();let i;const o=e+A;t.firstCreatePass?(i=function fR(e,n){if(n)for(let t=n.length-1;t>=0;t--){const i=n[t];if(e===i.name)return i}}(n,t.pipeRegistry),t.data[o]=i,i.onDestroy&&(t.destroyHooks??=[]).push(o,i.onDestroy)):i=t.data[o];const r=i.factory||(i.factory=Ei(i.type)),a=_t(T);try{const l=Fa(!1),c=r();return Fa(l),function gh(e,n,t,i){t>=e.data.length&&(e.data[t]=null,e.blueprint[t]=null),n[t]=i}(t,D(),o,c),c}finally{_t(a)}}(39,"slice"),_()()()()}if(2&e){const t=v();f(3),Ze("ngModel",t.settings.assembly),f(2),O(t.translations.assembly),f(),g("ngForOf",t.assemblies),f(2),g("ngIf",t.totalNumberOfRiskHotspots>10),f(),g("ngIf",t.totalNumberOfRiskHotspots>10),f(4),j("",t.translations.filter," "),f(),Ze("ngModel",t.settings.filter),f(7),g("ngForOf",t.riskHotspotMetrics),f(5),g("ngClass",Le(20,Rc,"assembly"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"assembly"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"assembly"!==t.settings.sortBy)),f(),O(t.translations.assembly),f(3),g("ngClass",Le(24,Rc,"class"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"class"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"class"!==t.settings.sortBy)),f(),O(t.translations.class),f(3),g("ngClass",Le(28,Rc,"method"===t.settings.sortBy&&"asc"===t.settings.sortOrder,"method"===t.settings.sortBy&&"desc"===t.settings.sortOrder,"method"!==t.settings.sortBy)),f(),O(t.translations.method),f(),g("ngForOf",t.riskHotspotMetrics),f(2),g("ngForOf",function uw(e,n,t,i,o){const r=e+A,s=D(),a=function Ai(e,n){return e[n]}(s,r);return function Ns(e,n){return e[1].data[n].pure}(s,r)?sw(s,ct(),n,a.transform,t,i,o,a):a.transform(t,i,o)}(39,16,t.riskHotspots,0,t.settings.numberOfRiskHotspots))}}let e8=(()=>{class e{constructor(t){this.queryString="",this.riskHotspotMetrics=[],this.riskHotspots=[],this.totalNumberOfRiskHotspots=0,this.assemblies=[],this.translations={},this.settings=new BU,this.window=t.nativeWindow}ngOnInit(){this.riskHotspotMetrics=this.window.riskHotspotMetrics,this.translations=this.window.translations,void 0!==this.window.history&&void 0!==this.window.history.replaceState&&null!==this.window.history.state&&null!=this.window.history.state.riskHotspotsSettings&&(console.log("Risk hotspots: Restoring from history",this.window.history.state.riskHotspotsSettings),this.settings=JSON.parse(JSON.stringify(this.window.history.state.riskHotspotsSettings)));const t=window.location.href.indexOf("?");t>-1&&(this.queryString=window.location.href.substring(t)),this.updateRiskHotpots()}onDonBeforeUnlodad(){if(void 0!==this.window.history&&void 0!==this.window.history.replaceState){console.log("Risk hotspots: Updating history",this.settings);let t=new NM;null!==window.history.state&&(t=JSON.parse(JSON.stringify(this.window.history.state))),t.riskHotspotsSettings=JSON.parse(JSON.stringify(this.settings)),window.history.replaceState(t,"")}}updateRiskHotpots(){const t=this.window.riskHotspots;if(this.totalNumberOfRiskHotspots=t.length,0===this.assemblies.length){let s=[];for(let a=0;a<t.length;a++)-1===s.indexOf(t[a].assembly)&&s.push(t[a].assembly);this.assemblies=s.sort()}let i=[];for(let s=0;s<t.length;s++)""!==this.settings.filter&&-1===t[s].class.toLowerCase().indexOf(this.settings.filter.toLowerCase())||""!==this.settings.assembly&&t[s].assembly!==this.settings.assembly||i.push(t[s]);let o="asc"===this.settings.sortOrder?-1:1,r="asc"===this.settings.sortOrder?1:-1;if("assembly"===this.settings.sortBy)i.sort(function(s,a){return s.assembly===a.assembly?0:s.assembly<a.assembly?o:r});else if("class"===this.settings.sortBy)i.sort(function(s,a){return s.class===a.class?0:s.class<a.class?o:r});else if("method"===this.settings.sortBy)i.sort(function(s,a){return s.methodShortName===a.methodShortName?0:s.methodShortName<a.methodShortName?o:r});else if(""!==this.settings.sortBy){let s=parseInt(this.settings.sortBy,10);i.sort(function(a,l){return a.metrics[s].value===l.metrics[s].value?0:a.metrics[s].value<l.metrics[s].value?o:r})}this.riskHotspots=i}updateSorting(t,i){i.preventDefault(),this.settings.sortOrder=t===this.settings.sortBy&&"asc"===this.settings.sortOrder?"desc":"asc",this.settings.sortBy=t,console.log(`Updating sort column: '${this.settings.sortBy}' (${this.settings.sortOrder})`),this.updateRiskHotpots()}static{this.\u0275fac=function(i){return new(i||e)(T(Lp))}}static{this.\u0275cmp=on({type:e,selectors:[["risk-hotspots"]],hostBindings:function(i,o){1&i&&z("beforeunload",function(){return o.onDonBeforeUnlodad()},0,cl)},standalone:!1,decls:1,vars:1,consts:[[4,"ngIf"],[1,"customizebox"],["name","assembly",3,"ngModelChange","ngModel"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"col-center"],[3,"ngModel","ngModelChange",4,"ngIf"],[1,"col-right"],["type","text",3,"ngModelChange","ngModel"],[1,"table-responsive"],[1,"overview","table-fixed","stripped"],[1,"column-min-200"],["class","column105",4,"ngFor","ngForOf"],["href","#",3,"click"],[3,"ngClass"],[4,"ngFor","ngForOf"],[3,"value"],[3,"ngModelChange","ngModel"],["value","10"],["value","20",4,"ngIf"],["value","50",4,"ngIf"],["value","100",4,"ngIf"],[3,"value",4,"ngIf"],["value","20"],["value","50"],["value","100"],[1,"column105"],["target","_blank",3,"href"],[1,"icon-info-circled"],[3,"href"],[3,"title"],["class","right",3,"ngClass",4,"ngFor","ngForOf"],[1,"right",3,"ngClass"]],template:function(i,o){1&i&&L(0,JU,40,32,"div",0),2&i&&g("ngIf",o.totalNumberOfRiskHotspots>0)},dependencies:[hr,Ki,qn,xp,Op,Hs,qs,mc,zs,kb],encapsulation:2})}}return e})(),t8=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=si({type:e,bootstrap:[e8,HU]})}static{this.\u0275inj=Tn({providers:[Lp],imports:[KV,YH,HB]})}}return e})();YV().bootstrapModule(t8).catch(e=>console.error(e))}},yr=>{yr(yr.s=663)}]);