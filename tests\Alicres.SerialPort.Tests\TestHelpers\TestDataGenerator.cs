using System.Text;
using Alicres.SerialPort.Models;

namespace Alicres.SerialPort.Tests.TestHelpers;

/// <summary>
/// 测试数据生成器
/// </summary>
public static class TestDataGenerator
{
    private static readonly Random _random = new();

    /// <summary>
    /// 生成随机字节数组
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>随机字节数组</returns>
    public static byte[] GenerateRandomBytes(int length)
    {
        var bytes = new byte[length];
        _random.NextBytes(bytes);
        return bytes;
    }

    /// <summary>
    /// 生成随机文本
    /// </summary>
    /// <param name="length">长度</param>
    /// <returns>随机文本</returns>
    public static string GenerateRandomText(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var result = new StringBuilder(length);
        
        for (int i = 0; i < length; i++)
        {
            result.Append(chars[_random.Next(chars.Length)]);
        }
        
        return result.ToString();
    }

    /// <summary>
    /// 生成序列化字节数组
    /// </summary>
    /// <param name="length">长度</param>
    /// <param name="startValue">起始值</param>
    /// <returns>序列化字节数组</returns>
    public static byte[] GenerateSequentialBytes(int length, byte startValue = 0)
    {
        var bytes = new byte[length];
        for (int i = 0; i < length; i++)
        {
            bytes[i] = (byte)((startValue + i) % 256);
        }
        return bytes;
    }

    /// <summary>
    /// 生成重复字节数组
    /// </summary>
    /// <param name="pattern">重复模式</param>
    /// <param name="totalLength">总长度</param>
    /// <returns>重复字节数组</returns>
    public static byte[] GenerateRepeatingBytes(byte[] pattern, int totalLength)
    {
        var result = new byte[totalLength];
        for (int i = 0; i < totalLength; i++)
        {
            result[i] = pattern[i % pattern.Length];
        }
        return result;
    }

    /// <summary>
    /// 生成测试帧数据
    /// </summary>
    /// <param name="header">帧头</param>
    /// <param name="payload">载荷</param>
    /// <param name="footer">帧尾</param>
    /// <returns>完整帧数据</returns>
    public static byte[] GenerateFrameData(byte[] header, byte[] payload, byte[] footer)
    {
        var frame = new byte[header.Length + payload.Length + footer.Length];
        Array.Copy(header, 0, frame, 0, header.Length);
        Array.Copy(payload, 0, frame, header.Length, payload.Length);
        Array.Copy(footer, 0, frame, header.Length + payload.Length, footer.Length);
        return frame;
    }

    /// <summary>
    /// 创建标准配置
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>标准配置</returns>
    public static SerialPortConfiguration CreateStandardConfiguration(string portName = "COM_TEST")
    {
        return new SerialPortConfiguration
        {
            PortName = portName,
            BaudRate = 9600,
            DataBits = 8,
            StopBits = System.IO.Ports.StopBits.One,
            Parity = System.IO.Ports.Parity.None
        };
    }

    /// <summary>
    /// 创建高级缓冲配置
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>高级缓冲配置</returns>
    public static SerialPortConfiguration CreateAdvancedBufferingConfiguration(string portName = "COM_TEST")
    {
        var config = CreateStandardConfiguration(portName);
        config.EnableAdvancedBuffering = true;
        config.DataQueueMaxLength = 1000;
        config.BufferWarningThreshold = 80;
        return config;
    }

    /// <summary>
    /// 生成测试数据
    /// </summary>
    /// <param name="length">数据长度</param>
    /// <returns>测试数据</returns>
    public static byte[] GenerateTestData(int length = 100)
    {
        return GenerateRandomBytes(length);
    }
}
