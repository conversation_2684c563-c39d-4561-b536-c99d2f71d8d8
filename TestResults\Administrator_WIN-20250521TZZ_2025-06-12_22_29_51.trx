﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="7d4e8318-9afa-490b-a5d1-d95f5ce52b99" name="Administrator@WIN-20250521TZZ 2025-06-12 22:29:51" runUser="WIN-20250521TZZ\Administrator" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-06-12T22:29:51.9877862+08:00" queuing="2025-06-12T22:29:51.9877863+08:00" start="2025-06-12T22:29:50.6650538+08:00" finish="2025-06-12T22:29:52.3323007+08:00" />
  <TestSettings name="default" id="8f5f5e6d-53dc-4e60-bf4f-5b68eeaed2b6">
    <Deployment runDeploymentRoot="Administrator_WIN-20250521TZZ_2025-06-12_22_29_51" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="b9598949-8b58-4146-986c-7c8328801837" testId="f521d45f-97f3-b596-2e05-974f68bf56a3" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReadAllAvailableAsync_WithPartialRead_ShouldResizeBuffer" computerName="WIN-20250521TZZ" duration="00:00:00.0106608" startTime="2025-06-12T22:29:51.6487403+08:00" endTime="2025-06-12T22:29:51.6487404+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b9598949-8b58-4146-986c-7c8328801837" />
    <UnitTestResult executionId="c8f6bffc-0302-4bc4-9fb1-2346e9f7174b" testId="bf57e081-e610-f743-9940-8ff396bca294" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.CloseAsync_WhenAlreadyClosed_ShouldReturnTrueAndLogWarning" computerName="WIN-20250521TZZ" duration="00:00:00.0023027" startTime="2025-06-12T22:29:51.9619498+08:00" endTime="2025-06-12T22:29:51.9619500+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c8f6bffc-0302-4bc4-9fb1-2346e9f7174b" />
    <UnitTestResult executionId="244cc910-79d4-4b63-89f9-ac27064856be" testId="8fa12405-5d35-3817-a4c9-0074201eaed0" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReadAllAvailableAsync_WhenSerialPortReadThrows_ShouldThrowException" computerName="WIN-20250521TZZ" duration="00:00:00.0011881" startTime="2025-06-12T22:29:51.9632169+08:00" endTime="2025-06-12T22:29:51.9632171+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="244cc910-79d4-4b63-89f9-ac27064856be" />
    <UnitTestResult executionId="1ce7ed72-c148-4185-8c1a-e4e5d027970f" testId="266c21f7-57db-5e1b-95e2-7c3393544716" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.Service_WithFlowControlEnabled_ShouldInitializeFlowControlManager" computerName="WIN-20250521TZZ" duration="00:00:00.0010801" startTime="2025-06-12T22:29:52.0698545+08:00" endTime="2025-06-12T22:29:52.0698546+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1ce7ed72-c148-4185-8c1a-e4e5d027970f" />
    <UnitTestResult executionId="9998f629-3812-487a-9769-eea720f78934" testId="e877e7d8-a00f-9981-151d-8014c3e1548a" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.Service_WithAdvancedBufferingEnabled_ShouldInitializeBufferManager" computerName="WIN-20250521TZZ" duration="00:00:00.0009932" startTime="2025-06-12T22:29:51.6402037+08:00" endTime="2025-06-12T22:29:51.6402038+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9998f629-3812-487a-9769-eea720f78934" />
    <UnitTestResult executionId="d1806cc8-0c37-499b-8760-158ec55dd36e" testId="4aeac5e6-1cce-17b2-713c-d967a1b43599" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OnErrorReceived_WithAutoReconnectDisabled_ShouldNotStartReconnect" computerName="WIN-20250521TZZ" duration="00:00:00.1029863" startTime="2025-06-12T22:29:52.0663578+08:00" endTime="2025-06-12T22:29:52.0663580+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d1806cc8-0c37-499b-8760-158ec55dd36e" />
    <UnitTestResult executionId="fccc730c-26b7-4b9f-b8fc-c24c1e03faeb" testId="6c1cd536-a4df-d84a-13ca-c5166750bca2" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReconnectAsync_WhenMaxAttemptsReached_ShouldSetErrorState" computerName="WIN-20250521TZZ" duration="00:00:00.2011472" startTime="2025-06-12T22:29:51.8526021+08:00" endTime="2025-06-12T22:29:51.8526023+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fccc730c-26b7-4b9f-b8fc-c24c1e03faeb" />
    <UnitTestResult executionId="07f4c5cf-9418-4cab-ac0b-133d6fd1ed53" testId="d472286c-dcbd-2a8c-6dfe-a382dac016df" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReadAsync_WhenSerialPortReadThrows_ShouldThrowException" computerName="WIN-20250521TZZ" duration="00:00:00.0025709" startTime="2025-06-12T22:29:51.6513616+08:00" endTime="2025-06-12T22:29:51.6513617+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="07f4c5cf-9418-4cab-ac0b-133d6fd1ed53" />
    <UnitTestResult executionId="ea3b22a8-410e-42fd-9cd9-73f341485bb0" testId="745627a8-4f78-4a30-1a76-df4413025c6b" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.SendAsync_WhenSerialPortWriteThrows_ShouldThrowException" computerName="WIN-20250521TZZ" duration="00:00:00.0037201" startTime="2025-06-12T22:29:51.9595265+08:00" endTime="2025-06-12T22:29:51.9595268+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ea3b22a8-410e-42fd-9cd9-73f341485bb0" />
    <UnitTestResult executionId="80878eac-620f-4178-924d-cb9e3f836275" testId="eabf7d10-3a5c-81de-580f-e2b270f88a5d" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OnErrorReceived_WithAutoReconnectEnabled_ShouldStartReconnect" computerName="WIN-20250521TZZ" duration="00:00:00.2020544" startTime="2025-06-12T22:29:52.2720772+08:00" endTime="2025-06-12T22:29:52.2720774+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="80878eac-620f-4178-924d-cb9e3f836275" />
    <UnitTestResult executionId="cf5bb639-a7fc-4ebd-87c7-8f56b06dd995" testId="6c2979f3-bd04-ef64-1ad9-255415f8c833" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.SendAsync_WithCancellation_ShouldRespectCancellationToken" computerName="WIN-20250521TZZ" duration="00:00:00.0021271" startTime="2025-06-12T22:29:52.0686482+08:00" endTime="2025-06-12T22:29:52.0686484+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cf5bb639-a7fc-4ebd-87c7-8f56b06dd995" />
    <UnitTestResult executionId="393d0d4d-9307-4bb5-bd93-09fe3d0cc4a7" testId="c5be5af0-49eb-9737-f564-b4d89192ffc1" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OnDataReceived_WhenReadAllAvailableThrows_ShouldHandleException" computerName="WIN-20250521TZZ" duration="00:00:00.1027991" startTime="2025-06-12T22:29:51.9556361+08:00" endTime="2025-06-12T22:29:51.9556364+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="393d0d4d-9307-4bb5-bd93-09fe3d0cc4a7" />
    <UnitTestResult executionId="a4847840-4e83-4c5e-a741-3a589b561919" testId="d4876db0-9ac0-fd33-0f1b-0f8ec5143fe2" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OpenAsync_WhenAlreadyConnected_ShouldReturnTrueAndLogWarning" computerName="WIN-20250521TZZ" duration="00:00:00.0320513" startTime="2025-06-12T22:29:51.6368041+08:00" endTime="2025-06-12T22:29:51.6368120+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a4847840-4e83-4c5e-a741-3a589b561919" />
  </Results>
  <TestDefinitions>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReadAllAvailableAsync_WhenSerialPortReadThrows_ShouldThrowException" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8fa12405-5d35-3817-a4c9-0074201eaed0">
      <Execution id="244cc910-79d4-4b63-89f9-ac27064856be" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="ReadAllAvailableAsync_WhenSerialPortReadThrows_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OpenAsync_WhenAlreadyConnected_ShouldReturnTrueAndLogWarning" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d4876db0-9ac0-fd33-0f1b-0f8ec5143fe2">
      <Execution id="a4847840-4e83-4c5e-a741-3a589b561919" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="OpenAsync_WhenAlreadyConnected_ShouldReturnTrueAndLogWarning" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReadAllAvailableAsync_WithPartialRead_ShouldResizeBuffer" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f521d45f-97f3-b596-2e05-974f68bf56a3">
      <Execution id="b9598949-8b58-4146-986c-7c8328801837" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="ReadAllAvailableAsync_WithPartialRead_ShouldResizeBuffer" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.SendAsync_WhenSerialPortWriteThrows_ShouldThrowException" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="745627a8-4f78-4a30-1a76-df4413025c6b">
      <Execution id="ea3b22a8-410e-42fd-9cd9-73f341485bb0" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="SendAsync_WhenSerialPortWriteThrows_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.Service_WithFlowControlEnabled_ShouldInitializeFlowControlManager" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="266c21f7-57db-5e1b-95e2-7c3393544716">
      <Execution id="1ce7ed72-c148-4185-8c1a-e4e5d027970f" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="Service_WithFlowControlEnabled_ShouldInitializeFlowControlManager" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OnDataReceived_WhenReadAllAvailableThrows_ShouldHandleException" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c5be5af0-49eb-9737-f564-b4d89192ffc1">
      <Execution id="393d0d4d-9307-4bb5-bd93-09fe3d0cc4a7" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="OnDataReceived_WhenReadAllAvailableThrows_ShouldHandleException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.SendAsync_WithCancellation_ShouldRespectCancellationToken" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="6c2979f3-bd04-ef64-1ad9-255415f8c833">
      <Execution id="cf5bb639-a7fc-4ebd-87c7-8f56b06dd995" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="SendAsync_WithCancellation_ShouldRespectCancellationToken" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReadAsync_WhenSerialPortReadThrows_ShouldThrowException" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d472286c-dcbd-2a8c-6dfe-a382dac016df">
      <Execution id="07f4c5cf-9418-4cab-ac0b-133d6fd1ed53" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="ReadAsync_WhenSerialPortReadThrows_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.CloseAsync_WhenAlreadyClosed_ShouldReturnTrueAndLogWarning" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bf57e081-e610-f743-9940-8ff396bca294">
      <Execution id="c8f6bffc-0302-4bc4-9fb1-2346e9f7174b" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="CloseAsync_WhenAlreadyClosed_ShouldReturnTrueAndLogWarning" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OnErrorReceived_WithAutoReconnectEnabled_ShouldStartReconnect" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eabf7d10-3a5c-81de-580f-e2b270f88a5d">
      <Execution id="80878eac-620f-4178-924d-cb9e3f836275" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="OnErrorReceived_WithAutoReconnectEnabled_ShouldStartReconnect" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.ReconnectAsync_WhenMaxAttemptsReached_ShouldSetErrorState" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="6c1cd536-a4df-d84a-13ca-c5166750bca2">
      <Execution id="fccc730c-26b7-4b9f-b8fc-c24c1e03faeb" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="ReconnectAsync_WhenMaxAttemptsReached_ShouldSetErrorState" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.OnErrorReceived_WithAutoReconnectDisabled_ShouldNotStartReconnect" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4aeac5e6-1cce-17b2-713c-d967a1b43599">
      <Execution id="d1806cc8-0c37-499b-8760-158ec55dd36e" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="OnErrorReceived_WithAutoReconnectDisabled_ShouldNotStartReconnect" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests.Service_WithAdvancedBufferingEnabled_ShouldInitializeBufferManager" storage="d:\project\00 alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e877e7d8-a00f-9981-151d-8014c3e1548a">
      <Execution id="9998f629-3812-487a-9769-eea720f78934" />
      <TestMethod codeBase="D:\Project\00 Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceEnhancedTests" name="Service_WithAdvancedBufferingEnabled_ShouldInitializeBufferManager" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="f521d45f-97f3-b596-2e05-974f68bf56a3" executionId="b9598949-8b58-4146-986c-7c8328801837" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bf57e081-e610-f743-9940-8ff396bca294" executionId="c8f6bffc-0302-4bc4-9fb1-2346e9f7174b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8fa12405-5d35-3817-a4c9-0074201eaed0" executionId="244cc910-79d4-4b63-89f9-ac27064856be" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="266c21f7-57db-5e1b-95e2-7c3393544716" executionId="1ce7ed72-c148-4185-8c1a-e4e5d027970f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e877e7d8-a00f-9981-151d-8014c3e1548a" executionId="9998f629-3812-487a-9769-eea720f78934" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4aeac5e6-1cce-17b2-713c-d967a1b43599" executionId="d1806cc8-0c37-499b-8760-158ec55dd36e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6c1cd536-a4df-d84a-13ca-c5166750bca2" executionId="fccc730c-26b7-4b9f-b8fc-c24c1e03faeb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d472286c-dcbd-2a8c-6dfe-a382dac016df" executionId="07f4c5cf-9418-4cab-ac0b-133d6fd1ed53" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="745627a8-4f78-4a30-1a76-df4413025c6b" executionId="ea3b22a8-410e-42fd-9cd9-73f341485bb0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eabf7d10-3a5c-81de-580f-e2b270f88a5d" executionId="80878eac-620f-4178-924d-cb9e3f836275" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6c2979f3-bd04-ef64-1ad9-255415f8c833" executionId="cf5bb639-a7fc-4ebd-87c7-8f56b06dd995" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c5be5af0-49eb-9737-f564-b4d89192ffc1" executionId="393d0d4d-9307-4bb5-bd93-09fe3d0cc4a7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d4876db0-9ac0-fd33-0f1b-0f8ec5143fe2" executionId="a4847840-4e83-4c5e-a741-3a589b561919" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="列表中未列出的结果" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="所有已加载的结果" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Completed">
    <Counters total="13" executed="13" passed="13" failed="0" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.4.5+1caef2f33e (64-bit .NET 8.0.17)&#xD;
[xUnit.net 00:00:00.17]   Discovering: Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.21]   Discovered:  Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.21]   Starting:    Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.90]   Finished:    Alicres.SerialPort.Tests&#xD;
</StdOut>
    </Output>
    <CollectorDataEntries>
      <Collector agentName="WIN-20250521TZZ" uri="datacollector://microsoft/CoverletCodeCoverage/1.0" collectorDisplayName="XPlat code coverage">
        <UriAttachments>
          <UriAttachment>
            <A href="WIN-20250521TZZ\coverage.cobertura.xml"></A>
          </UriAttachment>
        </UriAttachments>
      </Collector>
    </CollectorDataEntries>
  </ResultSummary>
</TestRun>