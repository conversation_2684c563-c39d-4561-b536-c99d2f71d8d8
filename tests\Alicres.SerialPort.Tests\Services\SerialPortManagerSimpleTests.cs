using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.SerialPort.Tests.TestHelpers;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// SerialPortManager 简单测试类
/// 提升 SerialPortManager 类的覆盖率
/// </summary>
public class SerialPortManagerSimpleTests : IDisposable
{
    private readonly Mock<ILogger<SerialPortManager>> _mockLogger;
    private readonly Mock<ILogger<MockSerialPortService>> _mockServiceLogger;
    private readonly IServiceProvider _serviceProvider;
    private readonly SerialPortManager _manager;

    public SerialPortManagerSimpleTests()
    {
        _mockLogger = new Mock<ILogger<SerialPortManager>>();
        _mockServiceLogger = new Mock<ILogger<MockSerialPortService>>();

        var services = new ServiceCollection();
        services.AddLogging();
        _serviceProvider = services.BuildServiceProvider();

        _manager = new SerialPortManager(_mockLogger.Object, _serviceProvider);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Assert
        _manager.SerialPorts.Should().NotBeNull();
        _manager.SerialPorts.Should().BeEmpty();
    }

    [Fact]
    public void CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort()
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600
        };

        // Act
        var service = _manager.CreateSerialPort(config);

        // Assert
        service.Should().NotBeNull();
        service.Configuration.PortName.Should().Be("COM1");
        _manager.SerialPorts.Should().ContainKey("COM1");
        _manager.SerialPorts["COM1"].Should().Be(service);
    }

    [Fact]
    public void CreateSerialPort_WithNullConfiguration_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _manager.CreateSerialPort(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void AddSerialPort_WithValidService_ShouldAddToCollection()
    {
        // Arrange
        var config = new SerialPortConfiguration { PortName = "COM1" };
        var service = new MockSerialPortService(config, _mockServiceLogger.Object);

        // Act
        _manager.AddSerialPort(service);

        // Assert
        _manager.SerialPorts.Should().ContainKey("COM1");
        _manager.SerialPorts["COM1"].Should().Be(service);
    }

    [Fact]
    public void AddSerialPort_WithDuplicatePort_ShouldThrowException()
    {
        // Arrange
        var config = new SerialPortConfiguration { PortName = "COM1" };
        var service1 = new MockSerialPortService(config, _mockServiceLogger.Object);
        var service2 = new MockSerialPortService(config, _mockServiceLogger.Object);

        _manager.AddSerialPort(service1);

        // Act & Assert
        var action = () => _manager.AddSerialPort(service2);
        action.Should().Throw<ArgumentException>()
            .WithMessage("*端口 COM1 已存在*");
    }

    [Fact]
    public void AddSerialPort_WithNullService_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _manager.AddSerialPort(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void RemoveSerialPort_WithExistingPort_ShouldRemoveFromCollection()
    {
        // Arrange
        var config = new SerialPortConfiguration { PortName = "COM1" };
        var service = new MockSerialPortService(config, _mockServiceLogger.Object);
        _manager.AddSerialPort(service);

        // Act
        var result = _manager.RemoveSerialPort("COM1");

        // Assert
        result.Should().BeTrue();
        _manager.SerialPorts.Should().NotContainKey("COM1");
    }

    [Fact]
    public void RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse()
    {
        // Act
        var result = _manager.RemoveSerialPort("COM1");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void RemoveSerialPort_WithNullPortName_ShouldThrowException()
    {
        // Act & Assert
        var action = () => _manager.RemoveSerialPort(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void GetSerialPort_WithExistingPort_ShouldReturnService()
    {
        // Arrange
        var config = new SerialPortConfiguration { PortName = "COM1" };
        var service = new MockSerialPortService(config, _mockServiceLogger.Object);
        _manager.AddSerialPort(service);

        // Act
        var result = _manager.GetSerialPort("COM1");

        // Assert
        result.Should().Be(service);
    }

    [Fact]
    public void GetSerialPort_WithNonExistentPort_ShouldReturnNull()
    {
        // Act
        var result = _manager.GetSerialPort("COM1");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void ContainsPort_WithExistingPort_ShouldReturnTrue()
    {
        // Arrange
        var config = new SerialPortConfiguration { PortName = "COM1" };
        var service = new MockSerialPortService(config, _mockServiceLogger.Object);
        _manager.AddSerialPort(service);

        // Act
        var result = _manager.ContainsPort("COM1");

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ContainsPort_WithNonExistentPort_ShouldReturnFalse()
    {
        // Act
        var result = _manager.ContainsPort("COM1");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task OpenAllAsync_WithMultiplePorts_ShouldOpenAllPorts()
    {
        // Arrange
        var config1 = new SerialPortConfiguration { PortName = "COM1" };
        var config2 = new SerialPortConfiguration { PortName = "COM2" };
        var service1 = new MockSerialPortService(config1, _mockServiceLogger.Object);
        var service2 = new MockSerialPortService(config2, _mockServiceLogger.Object);

        _manager.AddSerialPort(service1);
        _manager.AddSerialPort(service2);

        // Act
        var result = await _manager.OpenAllAsync();

        // Assert
        result.Should().Be(2);
        service1.IsConnected.Should().BeTrue();
        service2.IsConnected.Should().BeTrue();
    }

    [Fact]
    public async Task CloseAllAsync_WithMultiplePorts_ShouldCloseAllPorts()
    {
        // Arrange
        var config1 = new SerialPortConfiguration { PortName = "COM1" };
        var config2 = new SerialPortConfiguration { PortName = "COM2" };
        var service1 = new MockSerialPortService(config1, _mockServiceLogger.Object);
        var service2 = new MockSerialPortService(config2, _mockServiceLogger.Object);

        _manager.AddSerialPort(service1);
        _manager.AddSerialPort(service2);
        await _manager.OpenAllAsync();

        // Act
        var result = await _manager.CloseAllAsync();

        // Assert
        result.Should().Be(2);
        service1.IsConnected.Should().BeFalse();
        service2.IsConnected.Should().BeFalse();
    }

    [Fact]
    public void GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses()
    {
        // Arrange
        var config1 = new SerialPortConfiguration { PortName = "COM1" };
        var config2 = new SerialPortConfiguration { PortName = "COM2" };
        var service1 = new MockSerialPortService(config1, _mockServiceLogger.Object);
        var service2 = new MockSerialPortService(config2, _mockServiceLogger.Object);

        _manager.AddSerialPort(service1);
        _manager.AddSerialPort(service2);

        // Act
        var statuses = _manager.GetAllStatus();

        // Assert
        statuses.Should().HaveCount(2);
        statuses.Should().ContainKey("COM1");
        statuses.Should().ContainKey("COM2");
        statuses["COM1"].PortName.Should().Be("COM1");
        statuses["COM2"].PortName.Should().Be("COM2");
    }

    [Fact]
    public void GetAvailablePorts_ShouldReturnPortArray()
    {
        // Act
        var ports = _manager.GetAvailablePorts();

        // Assert
        ports.Should().NotBeNull();
        ports.Should().BeOfType<string[]>();
    }

    [Fact]
    public async Task BroadcastAsync_WithConnectedPorts_ShouldSendToAllPorts()
    {
        // Arrange
        var config1 = new SerialPortConfiguration { PortName = "COM1" };
        var config2 = new SerialPortConfiguration { PortName = "COM2" };
        var service1 = new MockSerialPortService(config1, _mockServiceLogger.Object);
        var service2 = new MockSerialPortService(config2, _mockServiceLogger.Object);

        _manager.AddSerialPort(service1);
        _manager.AddSerialPort(service2);
        await _manager.OpenAllAsync();

        var testData = new byte[] { 1, 2, 3, 4, 5 };

        // Act
        var result = await _manager.BroadcastAsync(testData);

        // Assert
        result.Should().Be(2);
        service1.Status.BytesSent.Should().Be(testData.Length);
        service2.Status.BytesSent.Should().Be(testData.Length);
    }

    [Fact]
    public async Task BroadcastAsync_WithNoConnectedPorts_ShouldReturnZero()
    {
        // Arrange
        var testData = new byte[] { 1, 2, 3, 4, 5 };

        // Act
        var result = await _manager.BroadcastAsync(testData);

        // Assert
        result.Should().Be(0);
    }

    [Fact]
    public async Task BroadcastAsync_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _manager.BroadcastAsync(null!));
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _manager.Dispose();
        action.Should().NotThrow();
    }

    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act
        _manager.Dispose();

        // Assert
        var action = () => _manager.Dispose();
        action.Should().NotThrow();
    }

    public void Dispose()
    {
        _manager?.Dispose();
        if (_serviceProvider is IDisposable disposableProvider)
        {
            disposableProvider.Dispose();
        }
    }
}
