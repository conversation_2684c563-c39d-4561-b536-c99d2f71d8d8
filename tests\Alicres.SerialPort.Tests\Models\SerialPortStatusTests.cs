using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// SerialPortStatus 测试类
/// 提升 SerialPortStatus 类的覆盖率
/// </summary>
public class SerialPortStatusTests
{
    [Fact]
    public void Create_WithPortName_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";

        // Act
        var status = SerialPortStatus.Create(portName);

        // Assert
        status.PortName.Should().Be(portName);
        status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
        status.IsConnected.Should().BeFalse();
        status.ReconnectAttempts.Should().Be(0);
        status.BytesSent.Should().Be(0);
        status.BytesReceived.Should().Be(0);
        status.ErrorCount.Should().Be(0);
    }

    [Fact]
    public void Create_WithPortNameAndState_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var state = SerialPortConnectionState.Connected;

        // Act
        var status = SerialPortStatus.Create(portName, state);

        // Assert
        status.PortName.Should().Be(portName);
        status.ConnectionState.Should().Be(state);
        status.IsConnected.Should().BeTrue();
    }

    [Fact]
    public void UpdateConnectionState_ToConnected_ShouldSetLastConnectedTime()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");
        var beforeUpdate = DateTime.Now;

        // Act
        status.UpdateConnectionState(SerialPortConnectionState.Connected);

        // Assert
        status.ConnectionState.Should().Be(SerialPortConnectionState.Connected);
        status.IsConnected.Should().BeTrue();
        status.LastConnectedTime.Should().NotBeNull();
        status.LastConnectedTime.Should().BeOnOrAfter(beforeUpdate);
    }

    [Fact]
    public void UpdateConnectionState_FromConnectedToDisconnected_ShouldSetLastDisconnectedTime()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");
        status.UpdateConnectionState(SerialPortConnectionState.Connected);
        var beforeDisconnect = DateTime.Now;

        // Act
        status.UpdateConnectionState(SerialPortConnectionState.Disconnected);

        // Assert
        status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
        status.IsConnected.Should().BeFalse();
        status.LastDisconnectedTime.Should().NotBeNull();
        status.LastDisconnectedTime.Should().BeOnOrAfter(beforeDisconnect);
    }

    [Fact]
    public void RecordError_ShouldUpdateErrorInformation()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");
        var errorMessage = "Test error message";
        var beforeError = DateTime.Now;

        // Act
        status.RecordError(errorMessage);

        // Assert
        status.LastError.Should().Be(errorMessage);
        status.LastErrorTime.Should().NotBeNull();
        status.LastErrorTime.Should().BeOnOrAfter(beforeError);
        status.ErrorCount.Should().Be(1);
        status.ConnectionState.Should().Be(SerialPortConnectionState.Error);
    }

    [Fact]
    public void RecordError_MultipleTimes_ShouldIncrementErrorCount()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.RecordError("Error 1");
        status.RecordError("Error 2");
        status.RecordError("Error 3");

        // Assert
        status.ErrorCount.Should().Be(3);
        status.LastError.Should().Be("Error 3");
    }

    [Fact]
    public void IncrementReconnectAttempts_ShouldIncreaseCount()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.IncrementReconnectAttempts();
        status.IncrementReconnectAttempts();

        // Assert
        status.ReconnectAttempts.Should().Be(2);
    }

    [Fact]
    public void ResetReconnectAttempts_ShouldSetToZero()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");
        status.IncrementReconnectAttempts();
        status.IncrementReconnectAttempts();

        // Act
        status.ResetReconnectAttempts();

        // Assert
        status.ReconnectAttempts.Should().Be(0);
    }

    [Fact]
    public void UpdateDataStatistics_WithSentBytes_ShouldUpdateSentStatistics()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.UpdateDataStatistics(sentBytes: 100);

        // Assert
        status.BytesSent.Should().Be(100);
        status.BytesReceived.Should().Be(0);
    }

    [Fact]
    public void UpdateDataStatistics_WithReceivedBytes_ShouldUpdateReceivedStatistics()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.UpdateDataStatistics(receivedBytes: 200);

        // Assert
        status.BytesSent.Should().Be(0);
        status.BytesReceived.Should().Be(200);
    }

    [Fact]
    public void UpdateDataStatistics_WithBothSentAndReceived_ShouldUpdateBothStatistics()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.UpdateDataStatistics(sentBytes: 100, receivedBytes: 200);

        // Assert
        status.BytesSent.Should().Be(100);
        status.BytesReceived.Should().Be(200);
    }

    [Fact]
    public void UpdateDataStatistics_MultipleTimes_ShouldAccumulate()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.UpdateDataStatistics(sentBytes: 100, receivedBytes: 200);
        status.UpdateDataStatistics(sentBytes: 50, receivedBytes: 75);

        // Assert
        status.BytesSent.Should().Be(150);
        status.BytesReceived.Should().Be(275);
    }

    [Fact]
    public void IsConnected_WhenStateIsConnected_ShouldReturnTrue()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act
        status.UpdateConnectionState(SerialPortConnectionState.Connected);

        // Assert
        status.IsConnected.Should().BeTrue();
    }

    [Fact]
    public void IsConnected_WhenStateIsNotConnected_ShouldReturnFalse()
    {
        // Arrange
        var status = SerialPortStatus.Create("COM1");

        // Act & Assert
        status.UpdateConnectionState(SerialPortConnectionState.Disconnected);
        status.IsConnected.Should().BeFalse();

        status.UpdateConnectionState(SerialPortConnectionState.Connecting);
        status.IsConnected.Should().BeFalse();

        status.UpdateConnectionState(SerialPortConnectionState.Disconnecting);
        status.IsConnected.Should().BeFalse();

        status.UpdateConnectionState(SerialPortConnectionState.Error);
        status.IsConnected.Should().BeFalse();

        status.UpdateConnectionState(SerialPortConnectionState.Reconnecting);
        status.IsConnected.Should().BeFalse();
    }

    [Fact]
    public void DefaultConstructor_ShouldInitializeWithDefaults()
    {
        // Act
        var status = new SerialPortStatus();

        // Assert
        status.PortName.Should().Be(string.Empty);
        status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
        status.IsConnected.Should().BeFalse();
        status.ReconnectAttempts.Should().Be(0);
        status.BytesSent.Should().Be(0);
        status.BytesReceived.Should().Be(0);
        status.ErrorCount.Should().Be(0);
        status.LastConnectedTime.Should().BeNull();
        status.LastDisconnectedTime.Should().BeNull();
        status.LastError.Should().BeNull();
        status.LastErrorTime.Should().BeNull();
    }

    [Fact]
    public void Properties_ShouldBeSettable()
    {
        // Arrange
        var status = new SerialPortStatus();
        var now = DateTime.Now;

        // Act
        status.PortName = "COM2";
        status.ConnectionState = SerialPortConnectionState.Connected;
        status.LastConnectedTime = now;
        status.LastDisconnectedTime = now;
        status.ReconnectAttempts = 5;
        status.BytesSent = 1000;
        status.BytesReceived = 2000;
        status.LastError = "Test error";
        status.LastErrorTime = now;
        status.ErrorCount = 3;

        // Assert
        status.PortName.Should().Be("COM2");
        status.ConnectionState.Should().Be(SerialPortConnectionState.Connected);
        status.LastConnectedTime.Should().Be(now);
        status.LastDisconnectedTime.Should().Be(now);
        status.ReconnectAttempts.Should().Be(5);
        status.BytesSent.Should().Be(1000);
        status.BytesReceived.Should().Be(2000);
        status.LastError.Should().Be("Test error");
        status.LastErrorTime.Should().Be(now);
        status.ErrorCount.Should().Be(3);
    }
}
