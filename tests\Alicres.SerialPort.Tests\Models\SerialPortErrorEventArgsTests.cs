using Alicres.SerialPort.Models;
using FluentAssertions;
using System;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// SerialPortErrorEventArgs 类的单元测试
/// </summary>
public class SerialPortErrorEventArgsTests
{
    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Should().Be(exception);
        eventArgs.ErrorLevel.Should().Be("Error");
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void Constructor_WithCustomErrorLevel_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");
        var errorLevel = "Warning";

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception, errorLevel);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Should().Be(exception);
        eventArgs.ErrorLevel.Should().Be(errorLevel);
    }

    [Fact]
    public void Constructor_WithNullPortName_ShouldThrowArgumentNullException()
    {
        // Arrange
        var exception = new InvalidOperationException("Test error");

        // Act & Assert
        var action = () => new SerialPortErrorEventArgs(null!, exception);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("portName");
    }

    [Fact]
    public void Constructor_WithNullException_ShouldThrowArgumentNullException()
    {
        // Arrange
        var portName = "COM1";

        // Act & Assert
        var action = () => new SerialPortErrorEventArgs(portName, null!);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("exception");
    }

    [Fact]
    public void Constructor_WithNullErrorLevel_ShouldUseDefaultErrorLevel()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception, null!);

        // Assert
        eventArgs.ErrorLevel.Should().Be("Error");
    }

    [Fact]
    public void Constructor_WithEmptyPortName_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "";
        var exception = new InvalidOperationException("Test error");

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Should().Be(exception);
    }

    [Fact]
    public void Properties_ShouldBeReadOnly()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Act & Assert
        var portNameProperty = typeof(SerialPortErrorEventArgs).GetProperty(nameof(eventArgs.PortName));
        var exceptionProperty = typeof(SerialPortErrorEventArgs).GetProperty(nameof(eventArgs.Exception));
        var errorLevelProperty = typeof(SerialPortErrorEventArgs).GetProperty(nameof(eventArgs.ErrorLevel));
        var timestampProperty = typeof(SerialPortErrorEventArgs).GetProperty(nameof(eventArgs.Timestamp));

        portNameProperty?.CanWrite.Should().BeFalse();
        exceptionProperty?.CanWrite.Should().BeFalse();
        errorLevelProperty?.CanWrite.Should().BeFalse();
        timestampProperty?.CanWrite.Should().BeFalse();
    }

    [Theory]
    [InlineData("COM1")]
    [InlineData("COM10")]
    [InlineData("/dev/ttyUSB0")]
    [InlineData("")]
    public void Constructor_WithVariousPortNames_ShouldInitializeCorrectly(string portName)
    {
        // Arrange
        var exception = new InvalidOperationException("Test error");

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Should().Be(exception);
    }

    [Theory]
    [InlineData("Error")]
    [InlineData("Warning")]
    [InlineData("Info")]
    [InlineData("Critical")]
    [InlineData("")]
    public void Constructor_WithVariousErrorLevels_ShouldInitializeCorrectly(string errorLevel)
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception, errorLevel);

        // Assert
        eventArgs.ErrorLevel.Should().Be(errorLevel ?? "Error");
    }

    [Fact]
    public void Constructor_WithDifferentExceptionTypes_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";

        // Act & Assert
        var invalidOpEventArgs = new SerialPortErrorEventArgs(portName, new InvalidOperationException("Invalid operation"));
        invalidOpEventArgs.Exception.Should().BeOfType<InvalidOperationException>();

        var argumentEventArgs = new SerialPortErrorEventArgs(portName, new ArgumentException("Argument error"));
        argumentEventArgs.Exception.Should().BeOfType<ArgumentException>();

        var ioEventArgs = new SerialPortErrorEventArgs(portName, new System.IO.IOException("IO error"));
        ioEventArgs.Exception.Should().BeOfType<System.IO.IOException>();

        var timeoutEventArgs = new SerialPortErrorEventArgs(portName, new TimeoutException("Timeout error"));
        timeoutEventArgs.Exception.Should().BeOfType<TimeoutException>();
    }

    [Fact]
    public void Constructor_WithInnerException_ShouldPreserveInnerException()
    {
        // Arrange
        var portName = "COM1";
        var innerException = new ArgumentException("Inner exception");
        var outerException = new InvalidOperationException("Outer exception", innerException);

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, outerException);

        // Assert
        eventArgs.Exception.Should().Be(outerException);
        eventArgs.Exception.InnerException.Should().Be(innerException);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error message");
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("端口 COM1");
        result.Should().Contain("发生 Error 错误");
        result.Should().Contain("Test error message");
    }

    [Fact]
    public void ToString_WithCustomErrorLevel_ShouldShowCorrectLevel()
    {
        // Arrange
        var portName = "COM2";
        var exception = new InvalidOperationException("Warning message");
        var eventArgs = new SerialPortErrorEventArgs(portName, exception, "Warning");

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("端口 COM2");
        result.Should().Contain("发生 Warning 错误");
        result.Should().Contain("Warning message");
    }

    [Fact]
    public void Timestamp_ShouldBeSetToCurrentTime()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");
        var beforeCreation = DateTime.Now;

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);
        var afterCreation = DateTime.Now;

        // Assert
        eventArgs.Timestamp.Should().BeOnOrAfter(beforeCreation);
        eventArgs.Timestamp.Should().BeOnOrBefore(afterCreation);
    }

    [Fact]
    public void Constructor_WithLongErrorMessage_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var longMessage = new string('A', 1000);
        var exception = new InvalidOperationException(longMessage);

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Message.Should().Be(longMessage);
        eventArgs.Exception.Message.Length.Should().Be(1000);
    }

    [Fact]
    public void Constructor_WithSpecialCharactersInMessage_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var specialMessage = "Error with special chars: 中文, émojis 🚀, symbols @#$%^&*()";
        var exception = new InvalidOperationException(specialMessage);

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Message.Should().Be(specialMessage);
    }
}
