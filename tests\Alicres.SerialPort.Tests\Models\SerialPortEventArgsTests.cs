using Alicres.SerialPort.Models;
using Alicres.SerialPort.Interfaces;
using FluentAssertions;
using Moq;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// 串口事件参数测试类
/// 提升事件参数类的覆盖率
/// </summary>
public class SerialPortEventArgsTests
{
    #region SerialPortDataReceivedEventArgs Tests

    [Fact]
    public void SerialPortDataReceivedEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Should().Be(data);
        eventArgs.Data.RawData.Should().BeEquivalentTo(new byte[] { 1, 2, 3 });
        eventArgs.Data.PortName.Should().Be("COM1");
        eventArgs.Data.Direction.Should().Be(SerialPortDataDirection.Received);
    }

    [Fact]
    public void SerialPortDataReceivedEventArgs_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortDataReceivedEventArgs(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SerialPortDataReceivedEventArgs_ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 1, 2, 3 }, "COM1", SerialPortDataDirection.Received);
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("COM1");
        result.Should().Contain("3");
        result.Should().Contain("Received");
    }

    #endregion

    #region SerialPortErrorEventArgs Tests

    [Fact]
    public void SerialPortErrorEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");

        // Act
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.Exception.Should().Be(exception);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SerialPortErrorEventArgs_WithNullPortName_ShouldThrowException()
    {
        // Arrange
        var exception = new InvalidOperationException("Test error");

        // Act & Assert
        var action = () => new SerialPortErrorEventArgs(null!, exception);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SerialPortErrorEventArgs_WithNullException_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortErrorEventArgs("COM1", null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SerialPortErrorEventArgs_ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var portName = "COM1";
        var exception = new InvalidOperationException("Test error");
        var eventArgs = new SerialPortErrorEventArgs(portName, exception);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("COM1");
        result.Should().Contain("Test error");
    }

    #endregion

    #region SerialPortStatusChangedEventArgs Tests

    [Fact]
    public void SerialPortStatusChangedEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";
        var previousState = SerialPortConnectionState.Disconnected;
        var currentState = SerialPortConnectionState.Connected;

        // Act
        var eventArgs = new SerialPortStatusChangedEventArgs(portName, previousState, currentState);

        // Assert
        eventArgs.PortName.Should().Be(portName);
        eventArgs.PreviousState.Should().Be(previousState);
        eventArgs.CurrentState.Should().Be(currentState);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void SerialPortStatusChangedEventArgs_WithNullPortName_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new SerialPortStatusChangedEventArgs(null!, 
            SerialPortConnectionState.Disconnected, 
            SerialPortConnectionState.Connected);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void SerialPortStatusChangedEventArgs_ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var eventArgs = new SerialPortStatusChangedEventArgs("COM1", 
            SerialPortConnectionState.Disconnected, 
            SerialPortConnectionState.Connected);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("COM1");
        result.Should().Contain("Disconnected");
        result.Should().Contain("Connected");
    }

    #endregion

    #region SerialPortAddedEventArgs Tests

    [Fact]
    public void SerialPortAddedEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        var mockService = new Mock<ISerialPortService>();
        mockService.Setup(s => s.Configuration).Returns(new SerialPortConfiguration { PortName = "COM1" });

        // Act
        var eventArgs = new SerialPortAddedEventArgs(mockService.Object);

        // Assert
        eventArgs.SerialPortService.Should().Be(mockService.Object);
    }

    #endregion

    #region SerialPortRemovedEventArgs Tests

    [Fact]
    public void SerialPortRemovedEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        var portName = "COM1";

        // Act
        var eventArgs = new SerialPortRemovedEventArgs(portName);

        // Assert
        eventArgs.PortName.Should().Be(portName);
    }

    #endregion
}
