using System.Globalization;

namespace Alicres.SerialPort.Exceptions;

/// <summary>
/// 串口通讯异常严重级别
/// </summary>
public enum SerialPortErrorLevel
{
    /// <summary>
    /// 信息级别 - 不影响正常操作
    /// </summary>
    Information = 0,

    /// <summary>
    /// 警告级别 - 可能影响性能但不中断操作
    /// </summary>
    Warning = 1,

    /// <summary>
    /// 错误级别 - 影响当前操作但可恢复
    /// </summary>
    Error = 2,

    /// <summary>
    /// 严重级别 - 导致连接中断或数据丢失
    /// </summary>
    Critical = 3,

    /// <summary>
    /// 致命级别 - 无法恢复的系统错误
    /// </summary>
    Fatal = 4
}

/// <summary>
/// 串口通讯基础异常类
/// 提供统一的异常处理机制和本地化支持
/// </summary>
public class SerialPortException : Exception
{
    /// <summary>
    /// 端口名称
    /// </summary>
    public string? PortName { get; }

    /// <summary>
    /// 错误级别
    /// </summary>
    public SerialPortErrorLevel ErrorLevel { get; }

    /// <summary>
    /// 错误代码，用于程序化处理
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// 异常发生时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortException() : this("串口通讯发生未知错误")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortException(string message) : this(message, SerialPortErrorLevel.Error, "SERIAL_UNKNOWN")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string message, Exception innerException)
        : this(message, SerialPortErrorLevel.Error, "SERIAL_UNKNOWN", null, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortException(string message, string portName)
        : this(message, SerialPortErrorLevel.Error, "SERIAL_UNKNOWN", portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortException(string message, string portName, Exception innerException)
        : this(message, SerialPortErrorLevel.Error, "SERIAL_UNKNOWN", portName, innerException)
    {
    }

    /// <summary>
    /// 完整构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="errorLevel">错误级别</param>
    /// <param name="errorCode">错误代码</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    protected SerialPortException(string message, SerialPortErrorLevel errorLevel, string errorCode,
        string? portName = null, Exception? innerException = null) : base(message, innerException)
    {
        PortName = portName;
        ErrorLevel = errorLevel;
        ErrorCode = errorCode;
        Timestamp = DateTime.UtcNow;
    }

    /// <summary>
    /// 获取本地化的错误消息
    /// </summary>
    /// <param name="culture">文化信息，为 null 时使用当前文化</param>
    /// <returns>本地化的错误消息</returns>
    public virtual string GetLocalizedMessage(CultureInfo? culture = null)
    {
        culture ??= CultureInfo.CurrentCulture;

        // 基础实现，子类可以重写以提供更具体的本地化消息
        var baseMessage = Message;

        if (!string.IsNullOrEmpty(PortName))
        {
            baseMessage = $"[{PortName}] {baseMessage}";
        }

        return $"[{ErrorCode}] {baseMessage}";
    }

    /// <summary>
    /// 获取详细的错误信息，包含所有上下文
    /// </summary>
    /// <returns>详细错误信息</returns>
    public virtual string GetDetailedErrorInfo()
    {
        var details = new List<string>
        {
            $"错误代码: {ErrorCode}",
            $"错误级别: {ErrorLevel}",
            $"发生时间: {Timestamp:yyyy-MM-dd HH:mm:ss.fff} UTC"
        };

        if (!string.IsNullOrEmpty(PortName))
        {
            details.Add($"端口名称: {PortName}");
        }

        details.Add($"错误消息: {Message}");

        if (InnerException != null)
        {
            details.Add($"内部异常: {InnerException.GetType().Name} - {InnerException.Message}");
        }

        return string.Join(Environment.NewLine, details);
    }
}

/// <summary>
/// 串口连接异常
/// 当串口连接、断开或连接状态相关操作失败时抛出
/// </summary>
public class SerialPortConnectionException : SerialPortException
{
    /// <summary>
    /// 连接失败的原因类型
    /// </summary>
    public ConnectionFailureReason FailureReason { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortConnectionException() : this("串口连接失败")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortConnectionException(string message)
        : this(message, ConnectionFailureReason.Unknown)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string message, Exception innerException)
        : this(message, ConnectionFailureReason.Unknown, null, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortConnectionException(string message, string portName)
        : this(message, ConnectionFailureReason.Unknown, portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string message, string portName, Exception innerException)
        : this(message, ConnectionFailureReason.Unknown, portName, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="failureReason">失败原因</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConnectionException(string message, ConnectionFailureReason failureReason,
        string? portName = null, Exception? innerException = null)
        : base(message, SerialPortErrorLevel.Critical, GetErrorCode(failureReason), portName, innerException)
    {
        FailureReason = failureReason;
    }

    /// <summary>
    /// 根据失败原因获取错误代码
    /// </summary>
    /// <param name="reason">失败原因</param>
    /// <returns>错误代码</returns>
    private static string GetErrorCode(ConnectionFailureReason reason)
    {
        return reason switch
        {
            ConnectionFailureReason.PortNotFound => "CONN_PORT_NOT_FOUND",
            ConnectionFailureReason.PortInUse => "CONN_PORT_IN_USE",
            ConnectionFailureReason.AccessDenied => "CONN_ACCESS_DENIED",
            ConnectionFailureReason.InvalidConfiguration => "CONN_INVALID_CONFIG",
            ConnectionFailureReason.HardwareError => "CONN_HARDWARE_ERROR",
            ConnectionFailureReason.DriverError => "CONN_DRIVER_ERROR",
            ConnectionFailureReason.Timeout => "CONN_TIMEOUT",
            ConnectionFailureReason.NetworkError => "CONN_NETWORK_ERROR",
            _ => "CONN_UNKNOWN"
        };
    }

    /// <summary>
    /// 获取本地化的错误消息
    /// </summary>
    /// <param name="culture">文化信息</param>
    /// <returns>本地化的错误消息</returns>
    public override string GetLocalizedMessage(CultureInfo? culture = null)
    {
        culture ??= CultureInfo.CurrentCulture;

        var reasonMessage = FailureReason switch
        {
            ConnectionFailureReason.PortNotFound => "指定的串口不存在",
            ConnectionFailureReason.PortInUse => "串口正被其他应用程序使用",
            ConnectionFailureReason.AccessDenied => "没有访问串口的权限",
            ConnectionFailureReason.InvalidConfiguration => "串口配置参数无效",
            ConnectionFailureReason.HardwareError => "硬件设备错误",
            ConnectionFailureReason.DriverError => "驱动程序错误",
            ConnectionFailureReason.Timeout => "连接超时",
            ConnectionFailureReason.NetworkError => "网络连接错误",
            _ => "未知连接错误"
        };

        var baseMessage = $"{reasonMessage}: {Message}";

        if (!string.IsNullOrEmpty(PortName))
        {
            baseMessage = $"[{PortName}] {baseMessage}";
        }

        return $"[{ErrorCode}] {baseMessage}";
    }
}

/// <summary>
/// 连接失败原因枚举
/// </summary>
public enum ConnectionFailureReason
{
    /// <summary>
    /// 未知原因
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// 端口不存在
    /// </summary>
    PortNotFound = 1,

    /// <summary>
    /// 端口被占用
    /// </summary>
    PortInUse = 2,

    /// <summary>
    /// 访问被拒绝
    /// </summary>
    AccessDenied = 3,

    /// <summary>
    /// 配置无效
    /// </summary>
    InvalidConfiguration = 4,

    /// <summary>
    /// 硬件错误
    /// </summary>
    HardwareError = 5,

    /// <summary>
    /// 驱动错误
    /// </summary>
    DriverError = 6,

    /// <summary>
    /// 连接超时
    /// </summary>
    Timeout = 7,

    /// <summary>
    /// 网络错误（用于网络串口）
    /// </summary>
    NetworkError = 8
}

/// <summary>
/// 串口配置异常
/// 当串口配置参数无效或配置操作失败时抛出
/// </summary>
public class SerialPortConfigurationException : SerialPortException
{
    /// <summary>
    /// 配置错误的参数名称
    /// </summary>
    public string? ParameterName { get; }

    /// <summary>
    /// 配置错误的参数值
    /// </summary>
    public object? ParameterValue { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortConfigurationException() : this("串口配置无效")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortConfigurationException(string message) : base(message, SerialPortErrorLevel.Error, "CONFIG_INVALID")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConfigurationException(string message, Exception innerException)
        : base(message, SerialPortErrorLevel.Error, "CONFIG_INVALID", null, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortConfigurationException(string message, string portName)
        : base(message, SerialPortErrorLevel.Error, "CONFIG_INVALID", portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortConfigurationException(string message, string portName, Exception innerException)
        : base(message, SerialPortErrorLevel.Error, "CONFIG_INVALID", portName, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="parameterName">参数名称</param>
    /// <param name="parameterValue">参数值</param>
    /// <param name="portName">端口名称</param>
    public SerialPortConfigurationException(string message, string parameterName, object? parameterValue, string? portName = null)
        : base(message, SerialPortErrorLevel.Error, "CONFIG_INVALID_PARAM", portName)
    {
        ParameterName = parameterName;
        ParameterValue = parameterValue;
    }

    /// <summary>
    /// 获取本地化的错误消息
    /// </summary>
    /// <param name="culture">文化信息</param>
    /// <returns>本地化的错误消息</returns>
    public override string GetLocalizedMessage(CultureInfo? culture = null)
    {
        culture ??= CultureInfo.CurrentCulture;

        var baseMessage = Message;

        if (!string.IsNullOrEmpty(ParameterName))
        {
            baseMessage = $"参数 '{ParameterName}' 配置错误";
            if (ParameterValue != null)
            {
                baseMessage += $"，当前值: {ParameterValue}";
            }
            baseMessage += $" - {Message}";
        }

        if (!string.IsNullOrEmpty(PortName))
        {
            baseMessage = $"[{PortName}] {baseMessage}";
        }

        return $"[{ErrorCode}] {baseMessage}";
    }

    /// <summary>
    /// 获取详细的错误信息
    /// </summary>
    /// <returns>详细错误信息</returns>
    public override string GetDetailedErrorInfo()
    {
        var details = new List<string>
        {
            $"错误代码: {ErrorCode}",
            $"错误级别: {ErrorLevel}",
            $"发生时间: {Timestamp:yyyy-MM-dd HH:mm:ss.fff} UTC"
        };

        if (!string.IsNullOrEmpty(PortName))
        {
            details.Add($"端口名称: {PortName}");
        }

        if (!string.IsNullOrEmpty(ParameterName))
        {
            details.Add($"错误参数: {ParameterName}");
            if (ParameterValue != null)
            {
                details.Add($"参数值: {ParameterValue}");
            }
        }

        details.Add($"错误消息: {Message}");

        if (InnerException != null)
        {
            details.Add($"内部异常: {InnerException.GetType().Name} - {InnerException.Message}");
        }

        return string.Join(Environment.NewLine, details);
    }
}

/// <summary>
/// 串口数据传输异常
/// 当数据发送、接收或处理过程中发生错误时抛出
/// </summary>
public class SerialPortDataException : SerialPortException
{
    /// <summary>
    /// 数据操作类型
    /// </summary>
    public DataOperationType OperationType { get; }

    /// <summary>
    /// 相关的数据长度（字节数）
    /// </summary>
    public int? DataLength { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public SerialPortDataException() : this("数据传输失败")
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    public SerialPortDataException(string message)
        : this(message, DataOperationType.Unknown)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortDataException(string message, Exception innerException)
        : this(message, DataOperationType.Unknown, null, null, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    public SerialPortDataException(string message, string portName)
        : this(message, DataOperationType.Unknown, portName)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="portName">端口名称</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortDataException(string message, string portName, Exception innerException)
        : this(message, DataOperationType.Unknown, portName, null, innerException)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">异常消息</param>
    /// <param name="operationType">操作类型</param>
    /// <param name="portName">端口名称</param>
    /// <param name="dataLength">数据长度</param>
    /// <param name="innerException">内部异常</param>
    public SerialPortDataException(string message, DataOperationType operationType,
        string? portName = null, int? dataLength = null, Exception? innerException = null)
        : base(message, SerialPortErrorLevel.Error, GetErrorCode(operationType), portName, innerException)
    {
        OperationType = operationType;
        DataLength = dataLength;
    }

    /// <summary>
    /// 根据操作类型获取错误代码
    /// </summary>
    /// <param name="operationType">操作类型</param>
    /// <returns>错误代码</returns>
    private static string GetErrorCode(DataOperationType operationType)
    {
        return operationType switch
        {
            DataOperationType.Send => "DATA_SEND_FAILED",
            DataOperationType.Receive => "DATA_RECEIVE_FAILED",
            DataOperationType.Parse => "DATA_PARSE_FAILED",
            DataOperationType.Validate => "DATA_VALIDATE_FAILED",
            DataOperationType.Buffer => "DATA_BUFFER_ERROR",
            DataOperationType.Timeout => "DATA_TIMEOUT",
            DataOperationType.Overflow => "DATA_OVERFLOW",
            DataOperationType.Corruption => "DATA_CORRUPTION",
            _ => "DATA_UNKNOWN"
        };
    }

    /// <summary>
    /// 获取本地化的错误消息
    /// </summary>
    /// <param name="culture">文化信息</param>
    /// <returns>本地化的错误消息</returns>
    public override string GetLocalizedMessage(CultureInfo? culture = null)
    {
        culture ??= CultureInfo.CurrentCulture;

        var operationMessage = OperationType switch
        {
            DataOperationType.Send => "数据发送",
            DataOperationType.Receive => "数据接收",
            DataOperationType.Parse => "数据解析",
            DataOperationType.Validate => "数据验证",
            DataOperationType.Buffer => "缓冲区操作",
            DataOperationType.Timeout => "数据传输超时",
            DataOperationType.Overflow => "数据溢出",
            DataOperationType.Corruption => "数据损坏",
            _ => "数据操作"
        };

        var baseMessage = $"{operationMessage}失败: {Message}";

        if (DataLength.HasValue)
        {
            baseMessage += $" (数据长度: {DataLength} 字节)";
        }

        if (!string.IsNullOrEmpty(PortName))
        {
            baseMessage = $"[{PortName}] {baseMessage}";
        }

        return $"[{ErrorCode}] {baseMessage}";
    }

    /// <summary>
    /// 获取详细的错误信息
    /// </summary>
    /// <returns>详细错误信息</returns>
    public override string GetDetailedErrorInfo()
    {
        var details = new List<string>
        {
            $"错误代码: {ErrorCode}",
            $"错误级别: {ErrorLevel}",
            $"操作类型: {OperationType}",
            $"发生时间: {Timestamp:yyyy-MM-dd HH:mm:ss.fff} UTC"
        };

        if (!string.IsNullOrEmpty(PortName))
        {
            details.Add($"端口名称: {PortName}");
        }

        if (DataLength.HasValue)
        {
            details.Add($"数据长度: {DataLength} 字节");
        }

        details.Add($"错误消息: {Message}");

        if (InnerException != null)
        {
            details.Add($"内部异常: {InnerException.GetType().Name} - {InnerException.Message}");
        }

        return string.Join(Environment.NewLine, details);
    }
}

/// <summary>
/// 数据操作类型枚举
/// </summary>
public enum DataOperationType
{
    /// <summary>
    /// 未知操作
    /// </summary>
    Unknown = 0,

    /// <summary>
    /// 数据发送
    /// </summary>
    Send = 1,

    /// <summary>
    /// 数据接收
    /// </summary>
    Receive = 2,

    /// <summary>
    /// 数据解析
    /// </summary>
    Parse = 3,

    /// <summary>
    /// 数据验证
    /// </summary>
    Validate = 4,

    /// <summary>
    /// 缓冲区操作
    /// </summary>
    Buffer = 5,

    /// <summary>
    /// 超时错误
    /// </summary>
    Timeout = 6,

    /// <summary>
    /// 数据溢出
    /// </summary>
    Overflow = 7,

    /// <summary>
    /// 数据损坏
    /// </summary>
    Corruption = 8
}
