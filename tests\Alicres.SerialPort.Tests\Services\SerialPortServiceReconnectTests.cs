using Alicres.SerialPort.Exceptions;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// SerialPortService 重连逻辑的单元测试
/// </summary>
public class SerialPortServiceReconnectTests : IDisposable
{
    private readonly Mock<ILogger<SerialPortService>> _mockLogger;
    private readonly SerialPortConfiguration _configuration;
    private SerialPortService? _serialPortService;

    public SerialPortServiceReconnectTests()
    {
        _mockLogger = new Mock<ILogger<SerialPortService>>();
        _configuration = new SerialPortConfiguration
        {
            PortName = "COM_TEST_RECONNECT",
            BaudRate = 9600,
            EnableAutoReconnect = true,
            ReconnectInterval = 100, // 短间隔用于测试
            MaxReconnectAttempts = 3
        };
    }

    [Fact]
    public void Configuration_WithAutoReconnectEnabled_ShouldHaveCorrectSettings()
    {
        // Arrange & Act
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);

        // Assert
        _serialPortService.Configuration.EnableAutoReconnect.Should().BeTrue();
        _serialPortService.Configuration.ReconnectInterval.Should().Be(100);
        _serialPortService.Configuration.MaxReconnectAttempts.Should().Be(3);
    }

    [Fact]
    public void Configuration_WithAutoReconnectDisabled_ShouldHaveCorrectSettings()
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM_TEST",
            BaudRate = 9600,
            EnableAutoReconnect = false
        };

        // Act
        _serialPortService = new SerialPortService(config, _mockLogger.Object);

        // Assert
        _serialPortService.Configuration.EnableAutoReconnect.Should().BeFalse();
    }

    [Fact]
    public void Status_InitialReconnectAttempts_ShouldBeZero()
    {
        // Arrange & Act
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);

        // Assert
        _serialPortService.Status.ReconnectAttempts.Should().Be(0);
    }

    [Fact]
    public async Task OpenAsync_WithInvalidPort_ShouldNotTriggerReconnect()
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "INVALID_PORT_NAME_12345",
            BaudRate = 9600,
            EnableAutoReconnect = true,
            ReconnectInterval = 100,
            MaxReconnectAttempts = 2
        };
        _serialPortService = new SerialPortService(config, _mockLogger.Object);

        // Act
        var result = await _serialPortService.OpenAsync();

        // Assert
        result.Should().BeFalse();
        _serialPortService.Status.ReconnectAttempts.Should().Be(0);
        _serialPortService.Status.ConnectionState.Should().Be(SerialPortConnectionState.Error);
    }

    [Fact]
    public void Configure_WithDifferentReconnectSettings_ShouldUpdateConfiguration()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
        var newConfig = new SerialPortConfiguration
        {
            PortName = "COM_NEW",
            BaudRate = 115200,
            EnableAutoReconnect = false,
            ReconnectInterval = 5000,
            MaxReconnectAttempts = 10
        };

        // Act
        _serialPortService.Configure(newConfig);

        // Assert
        _serialPortService.Configuration.EnableAutoReconnect.Should().BeFalse();
        _serialPortService.Configuration.ReconnectInterval.Should().Be(5000);
        _serialPortService.Configuration.MaxReconnectAttempts.Should().Be(10);
    }

    [Fact]
    public async Task CloseAsync_ShouldStopReconnectAttempts()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);

        // Act
        var result = await _serialPortService.CloseAsync();

        // Assert
        result.Should().BeTrue();
        _serialPortService.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
    }

    [Fact]
    public void Status_ReconnectAttempts_ShouldNotExceedMaximum()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);

        // Act & Assert
        // 模拟多次重连尝试
        for (int i = 0; i < 5; i++)
        {
            _serialPortService.Status.IncrementReconnectAttempts();
        }

        // 重连次数应该能够超过最大值（由调用者控制）
        _serialPortService.Status.ReconnectAttempts.Should().Be(5);
    }

    [Fact]
    public void Status_ResetReconnectAttempts_ShouldSetToZero()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
        
        // 先增加重连次数
        _serialPortService.Status.IncrementReconnectAttempts();
        _serialPortService.Status.IncrementReconnectAttempts();
        _serialPortService.Status.ReconnectAttempts.Should().Be(2);

        // Act
        _serialPortService.Status.ResetReconnectAttempts();

        // Assert
        _serialPortService.Status.ReconnectAttempts.Should().Be(0);
    }

    [Fact]
    public async Task SendAsync_WhenNotConnected_ShouldThrowException()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
        var data = new byte[] { 1, 2, 3 };

        // Act & Assert
        var action = async () => await _serialPortService.SendAsync(data);
        await action.Should().ThrowAsync<SerialPortDataException>();
    }

    [Fact]
    public async Task SendTextAsync_WhenNotConnected_ShouldThrowException()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
        var text = "Test message";

        // Act & Assert
        var action = async () => await _serialPortService.SendTextAsync(text);
        await action.Should().ThrowAsync<SerialPortDataException>();
    }

    [Fact]
    public void ErrorOccurred_Event_ShouldBeTriggerable()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
        var eventTriggered = false;
        Exception? receivedException = null;

        _serialPortService.ErrorOccurred += (sender, e) =>
        {
            eventTriggered = true;
            receivedException = e.Exception;
        };

        // Act
        // 由于我们无法直接触发内部错误事件，我们测试事件订阅是否正常工作
        // 这里我们验证事件处理器已正确设置

        // Assert
        eventTriggered.Should().BeFalse(); // 初始状态
        receivedException.Should().BeNull();
    }

    [Fact]
    public void StatusChanged_Event_ShouldBeTriggerable()
    {
        // Arrange
        _serialPortService = new SerialPortService(_configuration, _mockLogger.Object);
        var eventTriggered = false;
        SerialPortConnectionState? oldState = null;
        SerialPortConnectionState? newState = null;

        _serialPortService.StatusChanged += (sender, e) =>
        {
            eventTriggered = true;
            oldState = e.PreviousState;
            newState = e.CurrentState;
        };

        // Act
        // 由于我们无法直接触发内部状态变化事件，我们测试事件订阅是否正常工作

        // Assert
        eventTriggered.Should().BeFalse(); // 初始状态
        oldState.Should().BeNull();
        newState.Should().BeNull();
    }

    [Fact]
    public void Constructor_WithMinimalConfiguration_ShouldInitializeCorrectly()
    {
        // Arrange
        var minimalConfig = new SerialPortConfiguration
        {
            PortName = "COM1"
            // 其他属性使用默认值
        };

        // Act
        _serialPortService = new SerialPortService(minimalConfig, _mockLogger.Object);

        // Assert
        _serialPortService.Configuration.PortName.Should().Be("COM1");
        _serialPortService.Configuration.BaudRate.Should().Be(9600); // 默认值
        _serialPortService.Configuration.EnableAutoReconnect.Should().BeFalse(); // 默认值
        _serialPortService.Status.ConnectionState.Should().Be(SerialPortConnectionState.Disconnected);
    }

    public void Dispose()
    {
        _serialPortService?.Dispose();
    }
}
