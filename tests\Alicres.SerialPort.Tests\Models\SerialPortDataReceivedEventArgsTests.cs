using Alicres.SerialPort.Models;
using FluentAssertions;
using System;
using System.Text;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// SerialPortDataReceivedEventArgs 类的单元测试
/// </summary>
public class SerialPortDataReceivedEventArgsTests
{
    [Fact]
    public void Constructor_WithValidData_ShouldInitializeCorrectly()
    {
        // Arrange
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Test data"), "COM1", SerialPortDataDirection.Received);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Should().Be(data);
        eventArgs.Data.PortName.Should().Be("COM1");
        eventArgs.Data.Direction.Should().Be(SerialPortDataDirection.Received);
    }

    [Fact]
    public void Constructor_WithNullData_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var action = () => new SerialPortDataReceivedEventArgs(null!);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("data");
    }

    [Fact]
    public void Constructor_WithSentData_ShouldInitializeCorrectly()
    {
        // Arrange
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Sent data"), "COM2", SerialPortDataDirection.Sent);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Should().Be(data);
        eventArgs.Data.PortName.Should().Be("COM2");
        eventArgs.Data.Direction.Should().Be(SerialPortDataDirection.Sent);
    }

    [Fact]
    public void Constructor_WithEmptyData_ShouldInitializeCorrectly()
    {
        // Arrange
        var data = new SerialPortData(Array.Empty<byte>(), "COM1", SerialPortDataDirection.Received);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Should().Be(data);
        eventArgs.Data.Length.Should().Be(0);
    }

    [Fact]
    public void Constructor_WithLargeData_ShouldInitializeCorrectly()
    {
        // Arrange
        var largeData = new byte[1024];
        for (int i = 0; i < largeData.Length; i++)
        {
            largeData[i] = (byte)(i % 256);
        }
        var data = new SerialPortData(largeData, "COM1", SerialPortDataDirection.Received);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Should().Be(data);
        eventArgs.Data.Length.Should().Be(1024);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Test"), "COM1", SerialPortDataDirection.Received);
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("数据接收事件");
        result.Should().Contain("端口 COM1");
        result.Should().Contain("长度 4 字节");
        result.Should().Contain("方向 Received");
    }

    [Fact]
    public void ToString_WithSentData_ShouldShowCorrectDirection()
    {
        // Arrange
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Hello"), "COM2", SerialPortDataDirection.Sent);
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("数据接收事件");
        result.Should().Contain("端口 COM2");
        result.Should().Contain("长度 5 字节");
        result.Should().Contain("方向 Sent");
    }

    [Fact]
    public void Data_Property_ShouldBeReadOnly()
    {
        // Arrange
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Test"), "COM1", SerialPortDataDirection.Received);
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Act & Assert
        var dataProperty = typeof(SerialPortDataReceivedEventArgs).GetProperty(nameof(eventArgs.Data));
        dataProperty?.CanWrite.Should().BeFalse();
    }

    [Theory]
    [InlineData("COM1")]
    [InlineData("COM10")]
    [InlineData("/dev/ttyUSB0")]
    [InlineData("")]
    public void Constructor_WithVariousPortNames_ShouldInitializeCorrectly(string portName)
    {
        // Arrange
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Test"), portName, SerialPortDataDirection.Received);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.PortName.Should().Be(portName);
    }

    [Fact]
    public void Constructor_WithSpecialCharacterData_ShouldInitializeCorrectly()
    {
        // Arrange
        var specialData = new byte[] { 0x00, 0xFF, 0x7F, 0x80, 0x01, 0xFE };
        var data = new SerialPortData(specialData, "COM1", SerialPortDataDirection.Received);

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Should().Be(data);
        eventArgs.Data.Length.Should().Be(6);
        eventArgs.Data.RawData.Should().BeEquivalentTo(specialData);
    }

    [Fact]
    public void Constructor_WithDifferentDataDirections_ShouldPreserveDirection()
    {
        // Arrange
        var receivedData = new SerialPortData(Encoding.UTF8.GetBytes("Received"), "COM1", SerialPortDataDirection.Received);
        var sentData = new SerialPortData(Encoding.UTF8.GetBytes("Sent"), "COM1", SerialPortDataDirection.Sent);

        // Act
        var receivedEventArgs = new SerialPortDataReceivedEventArgs(receivedData);
        var sentEventArgs = new SerialPortDataReceivedEventArgs(sentData);

        // Assert
        receivedEventArgs.Data.Direction.Should().Be(SerialPortDataDirection.Received);
        sentEventArgs.Data.Direction.Should().Be(SerialPortDataDirection.Sent);
    }

    [Fact]
    public void Constructor_WithTimestampData_ShouldPreserveTimestamp()
    {
        // Arrange
        var testTime = new DateTime(2025, 6, 12, 14, 30, 45, 123);
        var data = new SerialPortData(Encoding.UTF8.GetBytes("Test"), "COM1", SerialPortDataDirection.Received)
        {
            Timestamp = testTime
        };

        // Act
        var eventArgs = new SerialPortDataReceivedEventArgs(data);

        // Assert
        eventArgs.Data.Timestamp.Should().Be(testTime);
    }
}
