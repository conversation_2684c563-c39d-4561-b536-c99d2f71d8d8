using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.SerialPort.Tests.TestHelpers;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace Alicres.SerialPort.Tests.Performance;

/// <summary>
/// SerialPortService 性能基准测试 - 验证流控制和缓冲管理的性能表现
/// </summary>
public class SerialPortPerformanceTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly ILogger<SerialPortService> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="output">测试输出</param>
    public SerialPortPerformanceTests(ITestOutputHelper output)
    {
        _output = output;
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<SerialPortService>();
    }

    /// <summary>
    /// 创建测试配置
    /// </summary>
    /// <returns>测试配置</returns>
    private SerialPortConfiguration CreateTestConfiguration()
    {
        return new SerialPortConfiguration
        {
            PortName = "COM_PERF_TEST",
            BaudRate = 115200,
            DataBits = 8,
            StopBits = System.IO.Ports.StopBits.One,
            Parity = System.IO.Ports.Parity.None
        };
    }

    /// <summary>
    /// 测试缓冲管理器的性能 - 大量数据入队出队操作
    /// </summary>
    [Fact]
    public void BufferManager_PerformanceTest_ShouldHandleLargeVolume()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableAdvancedBuffering = true;
        configuration.DataQueueMaxLength = 10000;

        using var bufferManager = new AdvancedBufferManager(configuration, _logger);
        var testDataSize = 1000;
        var testData = TestDataGenerator.GenerateRandomBytes(100);
        var stopwatch = Stopwatch.StartNew();

        // Act - 大量入队操作
        for (int i = 0; i < testDataSize; i++)
        {
            var data = new SerialPortData(testData, $"COM{i % 10}", SerialPortDataDirection.Received);
            bufferManager.EnqueueData(data);
        }

        var enqueueTime = stopwatch.ElapsedMilliseconds;
        stopwatch.Restart();

        // 大量出队操作
        var dequeuedCount = 0;
        while (bufferManager.TryDequeueData(out var _))
        {
            dequeuedCount++;
        }

        var dequeueTime = stopwatch.ElapsedMilliseconds;
        stopwatch.Stop();

        // Assert - 验证性能指标
        Assert.Equal(testDataSize, dequeuedCount);
        Assert.True(enqueueTime < 1000, $"入队操作耗时过长: {enqueueTime}ms");
        Assert.True(dequeueTime < 1000, $"出队操作耗时过长: {dequeueTime}ms");

        _output.WriteLine($"缓冲管理器性能测试:");
        _output.WriteLine($"  - 入队 {testDataSize} 项耗时: {enqueueTime}ms");
        _output.WriteLine($"  - 出队 {dequeuedCount} 项耗时: {dequeueTime}ms");
        _output.WriteLine($"  - 平均入队速度: {testDataSize / Math.Max(enqueueTime, 1)} 项/ms");
        _output.WriteLine($"  - 平均出队速度: {dequeuedCount / Math.Max(dequeueTime, 1)} 项/ms");
    }

    /// <summary>
    /// 测试流控制管理器的性能 - 大量流控制操作
    /// </summary>
    [Fact]
    public void FlowControlManager_PerformanceTest_ShouldHandleHighFrequency()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableFlowControl = true;
        configuration.SendRateLimit = 10000; // 高速率限制

        using var flowControlManager = new FlowControlManager(configuration, _logger);
        flowControlManager.IsEnabled = true;

        var operationCount = 10000;
        var testData = new byte[100];
        var stopwatch = Stopwatch.StartNew();

        // Act - 大量流控制检查操作
        for (int i = 0; i < operationCount; i++)
        {
            var canSend = flowControlManager.CanSend(testData.Length);
            if (canSend)
            {
                flowControlManager.RecordSend(testData.Length);
            }
            
            // 模拟接收流控制数据
            if (i % 100 == 0)
            {
                flowControlManager.ProcessFlowControlData(new byte[] { 0x11 }); // XON
            }
        }

        stopwatch.Stop();
        var elapsedTime = stopwatch.ElapsedMilliseconds;

        // Assert - 验证性能指标
        Assert.True(elapsedTime < 2000, $"流控制操作耗时过长: {elapsedTime}ms");

        var statistics = flowControlManager.GetStatistics();
        Assert.NotNull(statistics);

        _output.WriteLine($"流控制管理器性能测试:");
        _output.WriteLine($"  - {operationCount} 次操作耗时: {elapsedTime}ms");
        _output.WriteLine($"  - 平均操作速度: {operationCount / Math.Max(elapsedTime, 1)} 操作/ms");
        _output.WriteLine($"  - 发送字节数: {statistics.TotalBytesSent}");
        _output.WriteLine($"  - 接收字节数: {statistics.TotalBytesReceived}");
    }

    /// <summary>
    /// 测试 SerialPortService 的整体性能 - 综合操作性能
    /// </summary>
    [Fact]
    public async Task SerialPortService_PerformanceTest_ShouldHandleComplexOperations()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableFlowControl = true;
        configuration.EnableAdvancedBuffering = true;
        configuration.DataQueueMaxLength = 5000;

        using var service = new SerialPortService(configuration, _logger);
        var operationCount = 1000;
        var stopwatch = Stopwatch.StartNew();

        // Act - 执行大量混合操作
        for (int i = 0; i < operationCount; i++)
        {
            try
            {
                // 配置操作
                if (i % 100 == 0)
                {
                    var newConfig = CreateTestConfiguration();
                    newConfig.BaudRate = 9600 + (i % 3) * 9600;
                    service.Configure(newConfig);
                }

                // 连接操作
                if (i % 50 == 0)
                {
                    await service.OpenAsync();
                    await service.CloseAsync();
                }

                // 缓冲操作
                service.ClearDataQueue();
                var queueLength = service.GetQueueLength();
                var usage = service.GetQueueUsagePercentage();

                // 统计操作
                var bufferStats = service.GetBufferStatistics();
                var flowStats = service.GetFlowControlStatistics();

                // 验证操作结果
                Assert.True(queueLength >= 0);
                Assert.True(usage >= 0 && usage <= 100);
            }
            catch (Exception ex)
            {
                // 记录异常但继续测试
                if (i % 100 == 0) // 只记录部分异常避免输出过多
                {
                    _output.WriteLine($"操作 {i} 异常: {ex.Message}");
                }
            }
        }

        stopwatch.Stop();
        var elapsedTime = stopwatch.ElapsedMilliseconds;

        // Assert - 验证性能指标
        Assert.True(elapsedTime < 5000, $"综合操作耗时过长: {elapsedTime}ms");

        _output.WriteLine($"SerialPortService 综合性能测试:");
        _output.WriteLine($"  - {operationCount} 次混合操作耗时: {elapsedTime}ms");
        _output.WriteLine($"  - 平均操作速度: {operationCount / Math.Max(elapsedTime, 1)} 操作/ms");
        _output.WriteLine($"  - 最终状态: {service.Status.ConnectionState}");
        _output.WriteLine($"  - 队列长度: {service.GetQueueLength()}");
    }

    /// <summary>
    /// 测试内存使用效率 - 验证内存占用和垃圾回收
    /// </summary>
    [Fact]
    public void MemoryEfficiency_ShouldBeOptimal()
    {
        // Arrange
        var configuration = CreateTestConfiguration();
        configuration.EnableAdvancedBuffering = true;
        configuration.DataQueueMaxLength = 1000;

        var initialMemory = GC.GetTotalMemory(true);

        // Act - 创建和销毁多个服务实例
        for (int i = 0; i < 100; i++)
        {
            using var service = new SerialPortService(configuration, _logger);
            
            // 执行一些操作
            service.ClearDataQueue();
            var stats = service.GetBufferStatistics();
            var flowStats = service.GetFlowControlStatistics();
        }

        // 强制垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert - 验证内存使用
        Assert.True(memoryIncrease < 10 * 1024 * 1024, // 10MB 限制
            $"内存增长过多: {memoryIncrease / 1024 / 1024}MB");

        _output.WriteLine($"内存效率测试:");
        _output.WriteLine($"  - 初始内存: {initialMemory / 1024 / 1024}MB");
        _output.WriteLine($"  - 最终内存: {finalMemory / 1024 / 1024}MB");
        _output.WriteLine($"  - 内存增长: {memoryIncrease / 1024 / 1024}MB");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        // 清理资源
    }
}
