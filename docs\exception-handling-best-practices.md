# Alicres.SerialPort 异常处理最佳实践

本文档详细介绍 Alicres.SerialPort 模块的异常处理策略、最佳实践和使用指南。

## 📋 目录

- [异常类型层次结构](#异常类型层次结构)
- [错误级别和错误代码](#错误级别和错误代码)
- [异常处理最佳实践](#异常处理最佳实践)
- [本地化支持](#本地化支持)
- [诊断和调试](#诊断和调试)
- [常见异常场景](#常见异常场景)

---

## 🏗️ 异常类型层次结构

### 基础异常类

```csharp
SerialPortException (基础异常类)
├── SerialPortConnectionException (连接相关异常)
├── SerialPortConfigurationException (配置相关异常)
└── SerialPortDataException (数据传输相关异常)
```

### 异常特性

每个异常类都包含以下核心属性：

- **PortName**: 相关的串口名称
- **ErrorLevel**: 错误严重级别 (Information, Warning, Error, Critical, Fatal)
- **ErrorCode**: 程序化错误代码，便于自动化处理
- **Timestamp**: 异常发生的精确时间
- **本地化支持**: 支持多语言错误消息

---

## 🎯 错误级别和错误代码

### 错误级别定义

```csharp
public enum SerialPortErrorLevel
{
    Information = 0,  // 信息级别 - 不影响正常操作
    Warning = 1,      // 警告级别 - 可能影响性能但不中断操作
    Error = 2,        // 错误级别 - 影响当前操作但可恢复
    Critical = 3,     // 严重级别 - 导致连接中断或数据丢失
    Fatal = 4         // 致命级别 - 无法恢复的系统错误
}
```

### 标准错误代码

#### 连接异常错误代码
- `CONN_PORT_NOT_FOUND`: 指定的串口不存在
- `CONN_PORT_IN_USE`: 串口正被其他应用程序使用
- `CONN_ACCESS_DENIED`: 没有访问串口的权限
- `CONN_INVALID_CONFIG`: 串口配置参数无效
- `CONN_HARDWARE_ERROR`: 硬件设备错误
- `CONN_DRIVER_ERROR`: 驱动程序错误
- `CONN_TIMEOUT`: 连接超时
- `CONN_NETWORK_ERROR`: 网络连接错误

#### 配置异常错误代码
- `CONFIG_INVALID`: 一般配置错误
- `CONFIG_INVALID_PARAM`: 特定参数配置错误

#### 数据异常错误代码
- `DATA_SEND_FAILED`: 数据发送失败
- `DATA_RECEIVE_FAILED`: 数据接收失败
- `DATA_PARSE_FAILED`: 数据解析失败
- `DATA_VALIDATE_FAILED`: 数据验证失败
- `DATA_BUFFER_ERROR`: 缓冲区操作错误
- `DATA_TIMEOUT`: 数据传输超时
- `DATA_OVERFLOW`: 数据溢出
- `DATA_CORRUPTION`: 数据损坏

---

## ✅ 异常处理最佳实践

### 1. 分层异常处理

```csharp
try
{
    await serialPort.OpenAsync();
}
catch (SerialPortConnectionException ex) when (ex.FailureReason == ConnectionFailureReason.PortNotFound)
{
    // 处理端口不存在的情况
    logger.LogWarning("串口 {PortName} 不存在，尝试使用默认端口", ex.PortName);
    await TryAlternativePortAsync();
}
catch (SerialPortConnectionException ex) when (ex.FailureReason == ConnectionFailureReason.PortInUse)
{
    // 处理端口被占用的情况
    logger.LogError("串口 {PortName} 被占用: {Message}", ex.PortName, ex.Message);
    throw; // 重新抛出，让上层处理
}
catch (SerialPortConnectionException ex)
{
    // 处理其他连接异常
    logger.LogError(ex, "连接失败: {DetailedInfo}", ex.GetDetailedErrorInfo());
    await HandleConnectionFailureAsync(ex);
}
```

### 2. 使用错误代码进行程序化处理

```csharp
try
{
    await serialPort.SendAsync(data);
}
catch (SerialPortException ex)
{
    // 根据错误代码采取不同的处理策略
    switch (ex.ErrorCode)
    {
        case "DATA_SEND_FAILED":
            await RetryDataSendAsync(data);
            break;
        case "DATA_TIMEOUT":
            await IncreaseTimeoutAndRetryAsync(data);
            break;
        case "CONN_PORT_IN_USE":
            await WaitAndRetryConnectionAsync();
            break;
        default:
            logger.LogError(ex, "未处理的异常: {ErrorCode}", ex.ErrorCode);
            throw;
    }
}
```

### 3. 错误级别响应策略

```csharp
private async Task HandleSerialPortExceptionAsync(SerialPortException ex)
{
    switch (ex.ErrorLevel)
    {
        case SerialPortErrorLevel.Information:
        case SerialPortErrorLevel.Warning:
            // 记录日志但继续操作
            logger.LogWarning(ex, "串口警告: {Message}", ex.GetLocalizedMessage());
            break;

        case SerialPortErrorLevel.Error:
            // 记录错误并尝试恢复
            logger.LogError(ex, "串口错误: {DetailedInfo}", ex.GetDetailedErrorInfo());
            await AttemptRecoveryAsync(ex);
            break;

        case SerialPortErrorLevel.Critical:
            // 停止当前操作并通知用户
            logger.LogCritical(ex, "严重错误: {DetailedInfo}", ex.GetDetailedErrorInfo());
            await StopOperationAndNotifyAsync(ex);
            break;

        case SerialPortErrorLevel.Fatal:
            // 系统级错误，需要重启或退出
            logger.LogCritical(ex, "致命错误: {DetailedInfo}", ex.GetDetailedErrorInfo());
            await EmergencyShutdownAsync(ex);
            break;
    }
}
```

### 4. 异常聚合和批量处理

```csharp
public class SerialPortExceptionAggregator
{
    private readonly List<SerialPortException> _exceptions = new();
    private readonly object _lock = new();

    public void AddException(SerialPortException exception)
    {
        lock (_lock)
        {
            _exceptions.Add(exception);
        }
    }

    public async Task ProcessAccumulatedExceptionsAsync()
    {
        List<SerialPortException> exceptions;
        lock (_lock)
        {
            exceptions = new List<SerialPortException>(_exceptions);
            _exceptions.Clear();
        }

        // 按错误级别分组处理
        var groupedExceptions = exceptions.GroupBy(ex => ex.ErrorLevel);
        
        foreach (var group in groupedExceptions.OrderByDescending(g => g.Key))
        {
            await ProcessExceptionGroupAsync(group.Key, group.ToList());
        }
    }
}
```

---

## 🌍 本地化支持

### 使用本地化错误消息

```csharp
try
{
    await serialPort.OpenAsync();
}
catch (SerialPortException ex)
{
    // 获取当前文化的本地化消息
    var localizedMessage = ex.GetLocalizedMessage();
    
    // 获取特定文化的本地化消息
    var englishMessage = ex.GetLocalizedMessage(CultureInfo.GetCultureInfo("en-US"));
    var chineseMessage = ex.GetLocalizedMessage(CultureInfo.GetCultureInfo("zh-CN"));
    
    // 显示给用户
    MessageBox.Show(localizedMessage, "串口错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
}
```

### 自定义本地化资源

```csharp
// 扩展异常类以支持自定义本地化
public class CustomSerialPortException : SerialPortException
{
    private readonly IStringLocalizer _localizer;

    public CustomSerialPortException(string message, IStringLocalizer localizer) 
        : base(message)
    {
        _localizer = localizer;
    }

    public override string GetLocalizedMessage(CultureInfo? culture = null)
    {
        // 使用自定义本地化资源
        return _localizer[ErrorCode, PortName ?? "Unknown"];
    }
}
```

---

## 🔍 诊断和调试

### 生成详细诊断信息

```csharp
try
{
    await serialPort.SendAsync(data);
}
catch (SerialPortException ex)
{
    // 生成详细的诊断报告
    var diagnosticInfo = new StringBuilder();
    diagnosticInfo.AppendLine("=== 串口异常诊断报告 ===");
    diagnosticInfo.AppendLine(ex.GetDetailedErrorInfo());
    diagnosticInfo.AppendLine();
    diagnosticInfo.AppendLine("=== 系统环境信息 ===");
    diagnosticInfo.AppendLine($"操作系统: {Environment.OSVersion}");
    diagnosticInfo.AppendLine($".NET 版本: {Environment.Version}");
    diagnosticInfo.AppendLine($"可用串口: {string.Join(", ", SerialPort.GetPortNames())}");
    
    // 保存到文件或发送到日志系统
    await File.WriteAllTextAsync($"serial_error_{DateTime.Now:yyyyMMdd_HHmmss}.log", 
        diagnosticInfo.ToString());
}
```

### 异常统计和监控

```csharp
public class SerialPortExceptionMonitor
{
    private readonly ConcurrentDictionary<string, int> _errorCounts = new();
    private readonly ConcurrentQueue<SerialPortException> _recentExceptions = new();

    public void RecordException(SerialPortException exception)
    {
        // 统计错误代码出现次数
        _errorCounts.AddOrUpdate(exception.ErrorCode, 1, (key, count) => count + 1);
        
        // 保留最近的异常记录
        _recentExceptions.Enqueue(exception);
        while (_recentExceptions.Count > 100)
        {
            _recentExceptions.TryDequeue(out _);
        }
    }

    public Dictionary<string, int> GetErrorStatistics()
    {
        return new Dictionary<string, int>(_errorCounts);
    }

    public List<SerialPortException> GetRecentExceptions(int count = 10)
    {
        return _recentExceptions.TakeLast(count).ToList();
    }
}
```

---

## 🚨 常见异常场景

### 场景1: 端口不存在或被占用

```csharp
public async Task<bool> SafeOpenPortAsync(string portName)
{
    try
    {
        var config = new SerialPortConfiguration { PortName = portName };
        await serialPort.OpenAsync();
        return true;
    }
    catch (SerialPortConnectionException ex) when (ex.FailureReason == ConnectionFailureReason.PortNotFound)
    {
        logger.LogWarning("端口 {PortName} 不存在", portName);
        return false;
    }
    catch (SerialPortConnectionException ex) when (ex.FailureReason == ConnectionFailureReason.PortInUse)
    {
        logger.LogWarning("端口 {PortName} 被占用，等待后重试", portName);
        await Task.Delay(1000);
        return await SafeOpenPortAsync(portName); // 递归重试
    }
}
```

### 场景2: 配置参数验证

```csharp
public void ValidateAndSetConfiguration(SerialPortConfiguration config)
{
    try
    {
        serialPort.Configure(config);
    }
    catch (SerialPortConfigurationException ex) when (ex.ParameterName == nameof(config.BaudRate))
    {
        logger.LogError("波特率配置无效: {Value}", ex.ParameterValue);
        // 使用默认波特率
        config.BaudRate = 9600;
        serialPort.Configure(config);
    }
    catch (SerialPortConfigurationException ex)
    {
        logger.LogError(ex, "配置验证失败: {DetailedInfo}", ex.GetDetailedErrorInfo());
        throw;
    }
}
```

### 场景3: 数据传输错误恢复

```csharp
public async Task<bool> ReliableSendAsync(byte[] data, int maxRetries = 3)
{
    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            await serialPort.SendAsync(data);
            return true;
        }
        catch (SerialPortDataException ex) when (ex.OperationType == DataOperationType.Timeout)
        {
            logger.LogWarning("发送超时，第 {Attempt}/{MaxRetries} 次重试", attempt, maxRetries);
            if (attempt == maxRetries) throw;
            await Task.Delay(1000 * attempt); // 指数退避
        }
        catch (SerialPortDataException ex) when (ex.OperationType == DataOperationType.Send)
        {
            logger.LogError(ex, "发送失败: {Message}", ex.Message);
            if (attempt == maxRetries) throw;
            await Task.Delay(500);
        }
    }
    return false;
}
```

---

## 📝 总结

通过统一的异常处理策略，Alicres.SerialPort 提供了：

1. **结构化异常信息**: 包含错误代码、级别、时间戳等详细信息
2. **本地化支持**: 支持多语言错误消息
3. **程序化处理**: 通过错误代码和类型进行自动化异常处理
4. **诊断友好**: 提供详细的错误信息用于调试和监控
5. **最佳实践**: 提供完整的异常处理模式和恢复策略

遵循这些最佳实践可以显著提高应用程序的稳定性和用户体验。
